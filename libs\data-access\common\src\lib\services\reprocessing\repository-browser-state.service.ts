import { Injectable, signal, inject } from '@angular/core'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  MediaSourceType,
  QueuedRepositoryModel,
  RepositoryHierarchyModel,
} from '@venio/shared/models/interfaces'
import { UploadFacade } from '../../+state'
import { UuidGenerator } from '@venio/util/uuid'

@Injectable({ providedIn: 'root' })
export class RepositoryBrowserStateService {
  private uploadFacade = inject(UploadFacade)

  public filteredRepositoryHierarchy = signal<RepositoryHierarchyModel[]>([])

  public selectedFileFolders = toSignal(
    this.uploadFacade.selectFileFolderForUpload$
  )
  public mediaSourceType = toSignal(this.uploadFacade.selectMediaSourceType())

  public createDummyNodesOfChildren(
    selectedNode: RepositoryHierarchyModel,
    children: RepositoryHierarchyModel[]
  ): void {
    const dummies: RepositoryHierarchyModel[] = []
    children.forEach((child) => {
      if (child.type === 'FOLDER')
        dummies.push(this.createDummyChildNode(child))
    })
    this.filteredRepositoryHierarchy.set([
      ...this.filteredRepositoryHierarchy().filter(
        (node) =>
          node.parentId !== selectedNode.id &&
          node.id !== `${selectedNode.id}_dummy`
      ),
      ...children,
      ...dummies,
    ])
  }
  public createDummyChildNode(
    node: RepositoryHierarchyModel
  ): RepositoryHierarchyModel {
    return {
      ...node,
      parentId: node.id,
      id: node.id + '_dummy',
      name: `${node.name}_dummy`,
      isExpanded: false,
    }
  }

  public getSelectedFilesFolderWithoutRepeating(
    selectedFileFoldersIds: Array<string>
  ): RepositoryHierarchyModel[] {
    const filePaths = this.filteredRepositoryHierarchy()
      .filter((f) => selectedFileFoldersIds.includes(f.id))
      .map((f) => f.relativePath)
    const parentPaths = this.filterChildPaths(filePaths)
    const repos = this.filteredRepositoryHierarchy().filter(
      (f) => parentPaths.includes(f.relativePath) && !f.id.endsWith('_dummy')
    )
    return repos
  }

  public validateIfNewSourceAlreadyExists(
    newFolders: RepositoryHierarchyModel[]
  ): any {
    const newPaths = newFolders.map((path) => path.relativePath)
    const allExistingPaths = this.selectedFileFolders().flatMap(
      (queuedRepository) =>
        queuedRepository.repositoryHierarchies.map(
          (hierarchy) => hierarchy.relativePath
        )
    )

    const childOfNewPaths = this.findChildFolders(newPaths, allExistingPaths)
    const parentOfNewPaths = this.findParentFolders(newPaths, allExistingPaths)
    return {
      childOfNewPaths: childOfNewPaths,
      parentOfNewPaths: parentOfNewPaths,
    }
  }

  public updateExistingFolders(newFolders: RepositoryHierarchyModel[]): void {
    const allExistingPaths = this.selectedFileFolders().flatMap((f) =>
      f.repositoryHierarchies.map((r) => r.relativePath)
    )
    //Remove foldes that are already present
    newFolders = newFolders.filter(
      (f) => !allExistingPaths.includes(f.relativePath)
    )
    //Remove folders that are child of already present
    newFolders = newFolders.filter(
      (f) => !allExistingPaths.some((ep) => f.relativePath.startsWith(ep))
    )
    //Create lookup maps
    const newFoldersMap = new Map<string, RepositoryHierarchyModel>()
    const newFoldersPathSet = new Set<string>()

    for (const folder of newFolders) {
      newFoldersMap.set(folder.relativePath, folder)
      newFoldersPathSet.add(folder.relativePath)
    }

    //Use Set for faster lookups of already added paths
    const alreadyAddedRelativePaths = new Set<string>()
    const updatedFileFolders: QueuedRepositoryModel[] = []

    // Process all data before making updates
    for (const queuedData of this.selectedFileFolders()) {
      const updatedHierarchies: RepositoryHierarchyModel[] = []

      for (const existingHierarchy of queuedData.repositoryHierarchies) {
        const existingPath = existingHierarchy.relativePath
        let shouldReplace = false
        let replacingPath = ''

        // Optimization 4: Check against Set instead of array iteration
        for (const newPath of newFoldersPathSet) {
          if (existingPath.startsWith(newPath)) {
            replacingPath = newPath
            shouldReplace = true

            break
          }
        }

        const pathToUse = shouldReplace ? replacingPath : existingPath
        const hierarchyToUse = shouldReplace
          ? newFoldersMap.get(replacingPath)
          : existingHierarchy

        //  Single check for duplicates
        if (hierarchyToUse && !alreadyAddedRelativePaths.has(pathToUse)) {
          updatedHierarchies.push(hierarchyToUse)
          alreadyAddedRelativePaths.add(pathToUse)
        }
      }

      // Only create updated objects if there are changes
      if (updatedHierarchies.length > 0) {
        updatedFileFolders.push({
          ...queuedData,
          repositoryHierarchies: updatedHierarchies,
        })
      }
    }

    const unUsedNewPaths = Array.from(newFoldersMap.entries())
      .filter(([key]) => !alreadyAddedRelativePaths.has(key))
      .map(([, value]) => value)
    if (unUsedNewPaths.length) {
      updatedFileFolders.push({
        id: UuidGenerator.uuid,
        custodianName: '',
        mediaName: '',
        mediaSourceType: this.mediaSourceType() || MediaSourceType.SOURCE_FILE,
        repositoryHierarchies: unUsedNewPaths,
      })
    }

    // Update the files and folders
    if (updatedFileFolders.length > 0) {
      this.uploadFacade.updateFileFolderForUploadInBulk(updatedFileFolders)
    }
  }

  public filterChildPaths(paths: string[]): string[] {
    paths.sort((a, b) => a.length - b.length)

    const filteredPaths: string[] = []
    const pathSet = new Set<string>()

    for (const path of paths) {
      if (![...pathSet].some((parent) => path.startsWith(parent + '\\'))) {
        filteredPaths.push(path)
        pathSet.add(path)
      }
    }

    return filteredPaths
  }

  private findParentFolders(
    newPaths: string[],
    allExistingPaths: string[]
  ): string[] {
    const parentFolders: string[] = []

    newPaths.forEach((newPath) => {
      allExistingPaths.forEach((existingPath) => {
        if (newPath.startsWith(existingPath) && existingPath !== newPath) {
          parentFolders.push(existingPath)
        }
      })
    })

    return [...new Set(parentFolders)] // Remove duplicates
  }

  private findChildFolders(
    newPaths: string[],
    allExistingPaths: string[]
  ): string[] {
    const childFolders: string[] = []

    newPaths.forEach((newPath) => {
      allExistingPaths.forEach((existingPath) => {
        if (existingPath.startsWith(newPath)) {
          childFolders.push(existingPath)
        }
      })
    })

    return [...new Set(childFolders)] // Remove duplicates
  }
}
