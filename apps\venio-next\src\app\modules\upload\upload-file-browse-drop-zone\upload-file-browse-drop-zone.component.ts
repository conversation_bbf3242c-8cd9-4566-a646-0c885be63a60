import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  signal,
  view<PERSON><PERSON><PERSON>,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'

import { SvgLoaderDirective } from '@venio/feature/shared/directives'

import {
  FileRestrictions,
  FileSelectComponent,
  UploadsModule,
  SelectEvent,
  UploadComponent,
} from '@progress/kendo-angular-upload'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { UploadSupportedFileTypeInfoComponent } from '../upload-supported-file-type-info/upload-supported-file-type-info.component'
import {
  SelectedFileMetaInfo,
  SUPPORTED_FILE_EXTENSIONS,
  SUPPORTED_TRANSCRIPT_EXTENSION,
  UploadSourceTypes,
} from '@venio/shared/models/interfaces'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ime<PERSON> } from '@venio/util/utilities'
import {
  FileValidationService,
  UploadManagerService,
} from '@venio/data-access/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { eyeIcon, eyeSlashIcon } from '@progress/kendo-svg-icons'
import { UploadSelectedFileMetaInfoComponent } from '../upload-selected-file-meta-info/upload-selected-file-meta-info.component'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { Subject } from 'rxjs'

@Component({
  selector: 'venio-upload-file-browse-drop-zone',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    SvgLoaderDirective,
    UploadsModule,
    TooltipDirective,
    FormsModule,
    ReactiveFormsModule,
    UploadSelectedFileMetaInfoComponent,
  ],
  templateUrl: './upload-file-browse-drop-zone.component.html',
  styleUrl: './upload-file-browse-drop-zone.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadFileBrowseDropZoneComponent implements OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public readonly iconEye = eyeIcon

  public readonly iconSlashEye = eyeSlashIcon

  private readonly uploadComp = viewChild(UploadComponent)

  private readonly fileSelectComp = viewChild(FileSelectComponent)

  private readonly dialogService = inject(DialogService)

  private readonly fileValidationService = inject(FileValidationService)

  public readonly uploadManagerService = inject(UploadManagerService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private validationDialogRef: DialogRef

  public readonly selectSelectedFileMetaInfo = computed(() =>
    this.uploadManagerService.selectSelectedFileMetaInfo()
  )

  public readonly restrictions = computed<FileRestrictions>(() => {
    const { sourceType } =
      this.uploadManagerService.selectUploadSourceSelection()
    const allowedExtensions =
      sourceType === UploadSourceTypes.TRANSCRIPT
        ? SUPPORTED_TRANSCRIPT_EXTENSION.slice()
        : SUPPORTED_FILE_EXTENSIONS.slice()
    return {
      allowedExtensions,
      minFileSize: 1,
    }
  })

  public isDragOver = signal(false)

  private dragCounter = 0

  /**
   * Removes the selected file.
   *
   * Tries to remove the file from the upload and file select components and
   * notifies the upload worker service to remove the file.
   *
   * @param {SelectedFileMetaInfo} info - The selected file meta information.
   * @returns {void}
   */
  public removeSelectedFile(info: SelectedFileMetaInfo): void {
    try {
      this.uploadComp().removeFilesByUid(info.fileId)
      this.fileSelectComp().removeFileByUid(info.fileId)
    } catch {
      // Do nothing
    }
    this.uploadManagerService.removeFileById(info.fileId)
  }

  /**
   * Handles file selection from the upload components.
   *
   * @param {SelectEvent} event - The file select event.
   * @returns {void}
   */
  @DebounceTimer(100)
  public handleFileSelect(event: SelectEvent): void {
    if (!event?.files?.length) return

    const files = event.files

    // Validate files against current upload source
    this.fileValidationService.validateFiles(
      files,
      this.uploadManagerService.selectUploadSourceSelection()
    )

    const validationResults = this.fileValidationService.validationResults()
    const invalidFiles = this.fileValidationService.hasInvalidFiles()

    const validFiles = files.filter((f) =>
      validationResults.some(
        (result) => result.metadata.uid === f.uid && result.isValid
      )
    )

    if (invalidFiles) {
      this.#launchInvalidFilesDialog()
    }

    this.uploadManagerService.addFiles(validFiles)
  }

  /**
   * Shows a dialog with invalid files and their error messages.
   *
   * @returns {void}
   */
  #launchInvalidFilesDialog(): void {
    this.validationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      minWidth: '400px',
      maxWidth: '800px',
      appendTo: this.viewContainerRef,
    })

    this.#setDialogInput(this.validationDialogRef.content.instance)
    this.#attachClickHandlerToSupportedFilesLink()
  }

  /**
   * Sets the dialog input for the confirmation dialog.
   *
   * @param {ConfirmationDialogComponent} instance - The instance of the confirmation dialog.
   * @returns {void}
   */
  #setDialogInput(instance: ConfirmationDialogComponent): void {
    const validFiles = this.fileValidationService
      .validationResults()
      .some((result) => result.isValid)
    const invalidFiles = this.fileValidationService.invalidFiles()
    const s = invalidFiles.length > 1 ? 's' : ''

    instance.title = `Invalid File${s}`

    let message = ''
    if (validFiles) {
      message = `
      <p class="t-mb-4">Some of the selected files are not valid for upload.</p>
    `
    } else {
      message = `
      <p class="t-mb-4">The following file${s} cannot be uploaded:</p>
    `
    }

    // Create list of invalid file names
    let fileList = `
    <div class="t-max-h-56 t-overflow-y-auto t-p-1 t-border t-border-[#E2E2E2] t-px-4 t-py-1 t-rounded t-mb-4">
      <ul>
  `
    invalidFiles.forEach((file) => {
      fileList += `<li class="t-py-1 t-truncate" title="${file.metadata.name}">${file.metadata.name}</li>`
    })
    fileList += `
      </ul>
    </div>
  `

    instance.message = `
    <div class="t-max-w-lg t-mx-auto t-text-left">
      <header class="t-mb-4">
        ${message}
      </header>
      ${fileList}
      <footer>
        <p>
          Click <span class="t-font-bold t-text-[#1EBADC] t-cursor-pointer view-supported-files">here</span> to see supported file types.
        </p>
      </footer>
    </div>
  `

    // Configure dialog buttons
    instance.shouldShowCloseButton.set(false)
    instance.shouldShowCheckedButton.set(true)
  }

  /**
   * Attaches click handler to the Click "here" link in the dialog.
   *
   * @returns {void}
   */
  #attachClickHandlerToSupportedFilesLink(): void {
    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      const dialogElement =
        this.validationDialogRef.dialog.instance.dialog.nativeElement
      const clickHereLink = dialogElement.querySelector('.view-supported-files')

      if (clickHereLink) {
        clickHereLink.addEventListener('click', () => {
          this.validationDialogRef.close()
          this.openFileTypeInfo()
        })
      }
    })
  }

  /**
   * Triggers the file selection dialog.
   *
   * Finds the hidden file input button and simulates a click event.
   *
   * @returns {void}
   */
  public triggerFileSelect(): void {
    if (this.fileSelectComp()) {
      const button: HTMLButtonElement =
        this.fileSelectComp().wrapper.querySelector('.k-upload-button')
      button.click()
    }
  }

  /**
   * Handles the dragover event on the drop zone
   *
   * @param {DragEvent} event - the drag event
   * @returns {void}
   */
  public onDragOver(event: DragEvent): void {
    event.preventDefault()
    event.stopPropagation()
    this.dragCounter++
    this.isDragOver.set(true)
  }

  /**
   * Handles the dragleave event on the drop zone
   *
   * @param {DragEvent} event - the drag event
   * @returns {void}
   */
  public onDragLeave(event: DragEvent): void {
    event.preventDefault()
    event.stopPropagation()

    this.dragCounter--
    if (this.dragCounter === 0) {
      this.isDragOver.set(false)
    }
  }

  /**
   * Handles the drop event on the drop zone
   *
   * @param {DragEvent} event - the drag event
   * @returns {void}
   */
  public onDrop(event: DragEvent): void {
    event.preventDefault()
    event.stopPropagation()
    this.dragCounter = 0
    this.isDragOver.set(false)
  }

  /**
   * Opens the file type information dialog.
   *
   * Launches a dialog displaying supported file type information and attaches the close action.
   *
   * @returns {void}
   */
  public openFileTypeInfo(): void {
    const dialogRef = this.dialogService.open({
      content: UploadSupportedFileTypeInfoComponent,
      minHeight: '500px',
      width: '650px',
      maxWidth: 'calc(100vw - 100px)',
      appendTo: this.viewContainerRef,
    })

    this.attachDialogCloseAction(dialogRef)
  }

  /**
   * Returns a truncated file name if it exceeds a maximum length.
   *
   * Trims the file name and, if longer than the maximum length, truncates the base name and appends the extension.
   *
   * @param {string} name - The full file name.
   * @returns {string} - The truncated file name.
   */
  public getTruncatedFileName(name: string): string {
    name = name.trim()
    const parts = name.split('.')
    const extension = parts.pop()
    const fileName = parts.join('.')
    const maxLength = 20

    return name.length <= maxLength
      ? name
      : `${fileName.substring(0, maxLength)}...${extension}`
  }

  /**
   * Attaches the close action to the dialog's title bar button.
   *
   * A debounce is applied to allow the dialog to render fully before attaching the event listener.
   *
   * @param {DialogRef} dialogRef - The reference to the opened dialog.
   * @returns {void}
   */
  @DebounceTimer(0)
  private attachDialogCloseAction(dialogRef: DialogRef): void {
    const button = dialogRef.dialog.instance.dialog.nativeElement.querySelector(
      '.k-dialog-titlebar-action'
    )
    if (!button) return
    button.addEventListener('click', () => {
      dialogRef.close()
    })
  }

  /**
   * Lifecycle hook that is called when a directive, pipe, or service is destroyed.
   * @returns {void}
   */
  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
