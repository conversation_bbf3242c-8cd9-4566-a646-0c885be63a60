import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadFileBrowseDropZoneComponent } from './upload-file-browse-drop-zone.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, signal } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import {
  UploadManagerService,
  UploadFacade,
  FileValidationService,
} from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  CaseDetailModel,
  SelectedFileMetaInfo,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'

describe('UploadFileBrowseDropZoneComponent', () => {
  let component: UploadFileBrowseDropZoneComponent
  let fixture: ComponentFixture<UploadFileBrowseDropZoneComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadFileBrowseDropZoneComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: UploadFacade,
          useValue: {
            updateActiveStepperIndex: jest.fn(),
            resetUploadState: jest.fn(),
            updateUploadSourceSelection: jest.fn(),
            selectStepperActiveIndex$: of(0),
            selectSelectedCaseInfo$: of({} as CaseDetailModel),
            selectSelectedFileMetaInfo$: of([] as SelectedFileMetaInfo[]),
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
          } satisfies Partial<UploadFacade>,
        },
        {
          provide: UploadManagerService,
          useValue: {
            uploadMultipleFiles: jest.fn(),
            formattedFileInfo: signal([]),
            selectUploadSourceSelection: signal({} as UploadSourceSelection),
            selectSelectedFileMetaInfo: signal([] as SelectedFileMetaInfo[]),
          } satisfies Partial<UploadManagerService>,
        },
        {
          provide: FileValidationService,
          useValue: {
            validateFiles: jest.fn(),
          } satisfies Partial<FileValidationService>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadFileBrowseDropZoneComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
