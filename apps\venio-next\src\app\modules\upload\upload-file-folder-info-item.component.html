<form [formGroup]="form">
  <div
    class="t-flex t-flex-col t-max-h-[75%] t-overflow-y-auto t-p-[10px] t-gap-5 flex-1">
    <div
      class="t-flex t-flex-col t-gap-2 t-shadow-[0px_6px_10px_#00000029] t-p-[20px] t-rounded-[2px]">
      <kendo-combobox
        [data]="custodians()"
        placeholder="Custodian Name"
        [allowCustom]="true"
        formControlName="custodianName"
        class="t-w-full"></kendo-combobox>
      <span
        *ngIf="validationMessage()?.[fileFolder().id]?.custodianName"
        >{{validationMessage()?.[fileFolder().id]?.custodianName}}</span
      >

      <kendo-textbox
        placeholder="Media Name"
        formControlName="mediaName"
        class="t-flex t-flex-1"></kendo-textbox>
      <span
        *ngIf="validationMessage()?.[fileFolder().id]?.mediaName"
        >{{validationMessage()?.[fileFolder().id]?.mediaName}}</span
      >
      <div
        formArrayName="repositories"
        *ngFor="
          let repo of fileFolder()?.repositoryHierarchies;
          let i = index;
          let last = last
        ">
        <div [formGroupName]="i" class="t-flex t-flex-col t-gap-2">
          <div class="t-flex t-flex-row t-justify-between">
            <div class="t-flex t-items-center">
              <span class="t-text-[#000000] t-text-[14px]">{{
                repo.name
              }}</span>
            </div>

            <button
              kendoButton
              class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px]"
              rounded="full"
              (click)="deleteFileFolder(repo.id)"
              fillMode="clear"
              title="Delete">
              <span
                venioSvgLoader
                class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
                svgUrl="assets/svg/Icon-material-delete-grey.svg"
                >Del</span
              >
            </button>
          </div>

          @if(checkIfAnyRepositoryFileisEncryptable(repo)){
          <kendo-textbox
            [type]="'password'"
            placeholder="Password"
            formControlName="password"
            class="t-flex t-flex-1"></kendo-textbox>
          } @if(isLotusNotesFile(repo)){
          <div class="t-flex t-gap-2">
            <kendo-textbox
              placeholder="Browse .id"
              [readonly]="true"
              [value]="repo.nsfUserIdFiles?.name"
              class="t-flex t-flex-1">
            </kendo-textbox>
            <button
              kendoButton
              class="v-custom-secondary-button !t-rounded-none t-self-end"
              themeColor="secondary"
              fillMode="outline"
              (click)="onBrowseClick(i)">
              ...
            </button>
          </div>
          <kendo-fileselect
            #nsfFileSelect
            [hidden]="true"
            [multiple]="false"
            [restrictions]="nsfRestrictions"
            class="t-flex t-flex-1"
            formControlName="nsfUserIdFile">
          </kendo-fileselect>
          }
        </div>
        @if (!last) {
        <hr
          class="t-text-[#0000001A] t-my-2 t-border-dashed t-border-[1.4px]" />

        }
      </div>
    </div>
  </div>
</form>
