import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetContainerComponent } from './reviewset-container.component'
import {
  IframeMessengerService,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { provideMockStore } from '@ngrx/store/testing'
import { StartupsFacade } from '@venio/data-access/review'
import { of } from 'rxjs'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import {
  ModuleLoginStateService,
  ProjectFacade,
} from '@venio/data-access/common'
import {
  CaseDetailRequestInfo,
  ReviewSetDetailRequestModel,
  ReviewSetSummary,
} from '@venio/shared/models/interfaces'
import { CommonActionTypes } from '@venio/shared/models/constants'

describe('ReviewsetContainerComponent', () => {
  let component: ReviewsetContainerComponent
  let fixture: ComponentFixture<ReviewsetContainerComponent>
  let moduleLoginState: ModuleLoginStateService

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetContainerComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: IframeMessengerService,
          useValue: { sendMessage: jest.fn() },
        },
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({}),
            fetchUserRights: jest.fn(),
          },
        },
        {
          provide: ModuleLoginStateService,
          useValue: {
            updateProjectId: jest.fn(),
          },
        },
        {
          provide: ProjectFacade,
          useValue: {
            selectCaseDetailPagingInfo$: of({} as CaseDetailRequestInfo),
            selectSelectedCaseDetail$: of([]),
            selectIsReviewSetSummaryDetailLoading$: of(false),
            selectReviewSetSummaryDetail$: of({} as ReviewSetSummary),
            resetProjectState: jest.fn(),
            selectReviewSetSummaryDetailPagingInfo$: of(
              {} as ReviewSetDetailRequestModel
            ),
            selectProjectIdsToRights$: of({}),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetContainerComponent)
    moduleLoginState = TestBed.inject(ModuleLoginStateService)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
  it('should update project ID when review set grid action is clicked', () => {
    const mockEvent = {
      actionType: CommonActionTypes.ENTER_REVIEW_SET_REVIEW,
      content: {
        projectId: 123,
      },
    }

    component.reviewSetGridActionClick(mockEvent)

    expect(moduleLoginState.updateProjectId).toHaveBeenCalledWith(123)
  })
})
