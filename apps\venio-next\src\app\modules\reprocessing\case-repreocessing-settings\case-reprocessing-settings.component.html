<div
  class="t-mt-4 t-flex t-flex-col t-gap-3 t-w-full t-overflow-x-hidden t-overflow-y-auto"
  venioDynamicHeight
  [extraSpacing]="70">
  <!-- File Info Section -->
  <div
    class="t-flex t-p-4 t-items-center t-bg-[#F9F9F9] t-items-start t-items-baseline">
    <div class="t-w-1/6">
      <span class="t-font-bold t-text-sm">FILE ID</span>
      <p class="t-text-sm t-text-[#707070] t-block t-break-words t-mr-4">
        {{ currentFileData.fileId }}
      </p>
    </div>
    <div class="t-w-2/6">
      <span class="t-font-bold t-text-sm">MEDIA NAME</span>
      <p class="t-text-sm t-text-[#707070] t-block t-break-words t-mr-4">
        {{ currentFileData.mediaName }}
      </p>
    </div>
    <div class="t-w-3/6">
      <span class="t-font-bold t-text-sm">ORIGINAL FILE PATH</span>
      <p class="t-text-sm t-text-[#707070] t-block t-break-words">
        {{ currentFileData.originalFilePath }}
      </p>
    </div>
  </div>
  <!-- Replacement File Section -->
  <div
    class="t-bg-[#F9F9F9] t-flex t-items-center t-p-4 t-w-full t-flex t-gap-0">
    <kendo-textbox
      placeholder="Replacement File"
      [readonly]="true"
      [value]="replacementFilePath"
      class="t-flex-1 t-border t-border-gray-300 !t-border-r-0 !t-rounded-l-md">
    </kendo-textbox>
    <button
      kendoButton
      themeColor="secondary"
      fillMode="outline"
      (click)="toggleSlide()"
      class="t-border-solid !t-border-[1px] !t-border-[#9BD2A7] t-text-[#9BD2A7] hover:!t-text-[#FFFFFF] hover:!t-bg-[#9BD2A7] !t-min-h-auto !t-rounded-none !t-rounded-r-md"
      title="moreVerticalIcon">
      <kendo-svgicon
        [icon]="icons.moreVerticalIcon"
        class="t-rotate-90 t-scale-150"></kendo-svgicon>
    </button>
  </div>

  <!-- sliding content -->
  <div class="t-w-full">
    <!-- Repository/Local Tabs -->
    <div
      class="t-bg-[#F9F9F9] t-p-4"
      *ngIf="isVisible()"
      [ngClass]="{
        't-max-h-0 t-opacity-0 t-translate-y-[-30px]': !isExpanded(),
        't-max-h-full t-opacity-100 t-translate-y-0': isExpanded()
      }"
      [style.transition]="
        'opacity 0.5s ease, max-height 0.5s ease, transform 0.5s ease 0.3s'
      ">
      <kendo-tabstrip (tabSelect)="onSelect($event)" class="v-custom-tabstrip">
        <kendo-tabstrip-tab title="Repository" [selected]="true">
          <ng-template kendoTabContent>
            @defer{
            <venio-repository-browser
              [isForReprocessing]="true"
              (selectedReplacementFile)="onReplacementFileSelected($event)" />
            } @placeholder{
            <div class="t-flex t-flex-col t-gap-1">
              <kendo-skeleton
                *ngFor="let n of [1, 2, 3, 4, 5]"
                height="25px"
                width="100%"
                shape="rectangle"
                class="t-rounded-md t-mt-1" />
            </div>
            }
          </ng-template>
        </kendo-tabstrip-tab>

        <kendo-tabstrip-tab title="Local" [disabled]="true">
          <div class="t-p-4">
            <p class="t-text-gray-600">Local File Upload Section</p>
          </div>
        </kendo-tabstrip-tab>
      </kendo-tabstrip>
    </div>

    <!-- Options Section -->
    <div
      class="t-flex t-justify-between t-gap-4 t-bg-[#F9F9F9] t-w-full t-p-4 t-mt-4 t-flex-col">
      <div class="t-flex t-gap-3 t-w-full">
        <kendo-dropdownlist
          *ngIf="!isNsf()"
          [defaultItem]="defaultPassword"
          [data]="filteredPasswordData()"
          textField="password"
          valueField="passwordBankId"
          [valuePrimitive]="true"
          [(ngModel)]="selectedPassword"
          placeholder="Password"
          class="t-w-1/4"></kendo-dropdownlist>
        <kendo-multicolumncombobox
          [data]="filteredPasswordData()"
          [listHeight]="145"
          [popupSettings]="{ width: '240px' }"
          textField="nsfUserIdFilePath"
          valueField="passwordBankId"
          [valuePrimitive]="true"
          [(ngModel)]="selectedPassword"
          (valueChange)="onPasswordSelectionChange($event)"
          [filterable]="true"
          (filterChange)="onPasswordFilter($event)"
          [disabled]="isPasswordBankLoading()"
          placeholder="Password"
          *ngIf="isNsf()"
          class="t-w-1/4">
          <kendo-combobox-column
            *ngIf="isNsf()"
            field="nsfUserIdFilePath"
            title="NSF User ID"
            [width]="100">
            <ng-template
              kendoMultiColumnComboBoxColumnCellTemplate
              let-dataItem>
              <span>{{ dataItem.nsfUserIdFilePath }}</span>
            </ng-template>
            <ng-template kendoMultiColumnComboBoxColumnHeaderTemplate>
              <span>NSF User ID</span>
            </ng-template>
          </kendo-combobox-column>
          <kendo-combobox-column
            field="password"
            title="Password"
            [width]="100">
            <ng-template
              kendoMultiColumnComboBoxColumnCellTemplate
              let-dataItem>
              <span>{{ dataItem.password }}</span>
            </ng-template>
          </kendo-combobox-column>
        </kendo-multicolumncombobox>
        <div class="t-flex t-gap-2 t-items-center t-w-1/4">
          <kendo-numerictextbox
            format="# mins"
            [min]="1"
            [max]="240"
            [step]="1"
            [title]="'Set Timeout (In Mins)'"
            [(ngModel)]="timeoutValueInMin"
            [placeholder]="'Set Timeout (In Mins)'"></kendo-numerictextbox>
        </div>
      </div>

      <div class="t-flex t-gap-3 t-w-full" *ngIf="isFallbackIngestionEngine">
        <kendo-dropdownlist
          textField="name"
          valueField="id"
          [valuePrimitive]="true"
          [(value)]="currentFileData.metaExtractor"
          [data]="supportedMetaExtractors()"
          [defaultItem]="{ name: 'Meta Extraction', id: 0 }"
          class="t-w-1/4">
          <ng-template kendoDropDownListNoDataTemplate>
            No extractor available
          </ng-template>
        </kendo-dropdownlist>
        <kendo-dropdownlist
          textField="name"
          valueField="id"
          [valuePrimitive]="true"
          [(value)]="currentFileData.textExtractor"
          [data]="supportedFullTextExtractors()"
          [defaultItem]="{ name: 'Text Extraction', id: 0 }"
          class="t-w-1/4">
          <ng-template kendoDropDownListNoDataTemplate>
            No extractor available
          </ng-template>
        </kendo-dropdownlist>
        <kendo-dropdownlist
          textField="name"
          valueField="id"
          [valuePrimitive]="true"
          [(value)]="currentFileData.childExtractor"
          [data]="supportedChildExtractors()"
          [defaultItem]="{ name: 'Child Extraction', id: 0 }"
          class="t-w-1/4">
          <ng-template kendoDropDownListNoDataTemplate>
            No extractor available
          </ng-template>
        </kendo-dropdownlist>
      </div>
    </div>
  </div>
</div>

<!-- footer-->
<div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    (click)="onSave()"
    data-qa="save">
    SAVE
  </button>
  <button
    data-qa="cancel"
    kendoButton
    themeColor="dark"
    fillMode="outline"
    (click)="onCancel()">
    CANCEL
  </button>
</div>
