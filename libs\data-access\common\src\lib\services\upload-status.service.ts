/**
 * Upload Status Service
 *
 * This service transforms data into UI-friendly structures for displaying
 * upload, processing, and loading statuses. It manages the state of various UI
 * components using Angular signals and provides methods for UI interactions.
 * It also handles periodic polling of status endpoints to keep data updated.
 *
 * The service handles:
 * - Overall status cards (Upload, Processing, Load)
 * - Individual processing tasks and their stages
 * - File processing status and actions
 * - Unprocessed files notification
 * - Periodic polling for fresh data
 */
import { Injectable, signal, inject, DestroyRef } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { HttpClient } from '@angular/common/http'
import dayjs from 'dayjs'
import {
  Observable,
  Subscription,
  timer,
  EMPTY,
  catchError,
  switchMap,
  from,
  forkJoin,
  map,
  of,
  tap,
} from 'rxjs'
import { environment } from '@venio/shared/environments'
import { CaseConvertorService } from '@venio/util/utilities'

/**
 * Default polling interval in milliseconds
 */
export const DEFAULT_POLLING_INTERVAL_MS = 5000

/**
 * Default user ID for requests
 */
export const DEFAULT_USER_ID = 1

/**
 * Default file system ID for requests
 */
export const DEFAULT_FSID = -1

/**
 * Minimum backoff time in milliseconds for throttling
 */
export const MIN_BACKOFF_MS = 1000

/**
 * Maximum backoff time in milliseconds for throttling
 */
export const MAX_BACKOFF_MS = 5000

/**
 * Enum representing different processing states
 */
export enum ProcessingStatus {
  COMPLETED = 'COMPLETED',
  INPROGRESS = 'INPROGRESS',
  NOT_STARTED = 'NOT STARTED',
  FAILED = 'FAILED',
}

/**
 * Enum representing different card types in the UI
 */
export enum StatusCardType {
  UPLOAD = 'Overall Upload',
  PROCESSING = 'Overall Processing',
  LOAD = 'Overall Load',
}

/**
 * Enum for task ID prefixes
 */
export enum IdPrefix {
  TASK = 'task-',
  CARD = 'card-',
}

/**
 * Enum for status values
 */
export enum StatusCode {
  NOT_PROCESSED = '0',
  IN_PROGRESS = '1',
  COMPLETED = '2',
}

/**
 * Enum for status text
 */
export enum StatusText {
  NOT_PROCESSED = 'Not Processed',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  NOT_STARTED = 'Not Started',
  FAILED = 'Failed',
}

/**
 * Enum for processing stage types
 */
export enum ProcessingStageType {
  INGESTION = 'Ingestion',
  INDEXING = 'Indexing',
  EMAIL_ANALYSIS = 'Email Analysis',
  LANGUAGE_IDENTIFICATION = 'Language Identification',
}

/**
 * Enum for data fetch types
 */
enum FetchType {
  PROCESS_STATUS,
  UPLOAD_STATUS,
}

/**
 * Endpoint configuration interface
 */
export interface EndpointConfig {
  baseUrl: string
  processStatus: string
  uploadStatus: string
}

/**
 * Polling configuration interface
 */
export interface PollingConfig {
  projectId: number
  sessionId: string
  pollingIntervalMs: number
  isRVOD?: boolean
  userId?: number
  isTranscript?: boolean
  isRepository?: boolean
  fsid?: number
}

/**
 * Common progress tracking interface
 */
export interface Progress {
  completed: number
  total: number
}

/**
 * Interface for a single processing stage
 */
export interface ProcessingStage {
  type: string
  percentComplete: number
  status: ProcessingStatus
}

/**
 * Interface for file information
 */
export interface FileInfo {
  fileName: string
  percentComplete: number
  isProcessing: boolean
  canCancel: boolean
}

/**
 * Base interface for status items with common properties
 */
export interface StatusBase {
  id: string
  uploadedBy: string
  date: Date
  lastUpdated: Date
  progress: Progress
  status: ProcessingStatus
}

/**
 * Interface for status card in the UI
 */
export interface StatusCard extends StatusBase {
  type: StatusCardType
  files?: FileInfo[]
}

/**
 * Interface for a processing task in the UI
 */
export interface ProcessingTask extends StatusBase {
  name: string
  custodianName: string
  files?: FileInfo[]
  processingStages?: ProcessingStage[]
  canImportData?: boolean
  expanded?: boolean
  needsReprocessing?: boolean
}

/**
 * Interface for unprocessed files notification
 */
export interface UnprocessedFilesNotification {
  count: number
  canReprocess: boolean
}

/**
 * Interface for task status
 */
export interface TaskStatus {
  taskName: string
  totalCount: string
  completedCount: string
  timeTaken: string
  percentage: string
  status: string
  postProcessingStatus: string
  projectJobGroupId: any
}

/**
 * Interface for media entity
 */
export interface Media {
  typeCount: any
  jobList?: TaskStatus[]
  currentlyInProgressJob: any
  mediaId: number
  status: string
  mediaName: string
  docCount: number
  repeat: number
  edocs: number
  system: number
  duplicate: number
  archive: number
  uploadFileId: string
  uploadStartedDate: string
  uploadedBy: string | null
  scannedCount: any
  etaIngestion: any
  completedPercentage: any
  isRepository: boolean
  isMSTeam: boolean
  queuedOn: any
}

/**
 * Interface for custodian entity
 */
export interface Custodian {
  name: string
  fileCount: number
  mediaList: Media[]
}

/**
 * Interface for media info
 */
export interface MediaInfo {
  mediaId: number
  mediaName: string
  fileName: string
  mediaStatus: string
  createdDate: string
  uploadedBy: string
}

/**
 * Interface for upload info
 */
export interface UploadInfo {
  fileName: string
  name: string | null
  uploadStatus: number
  mediaName: string
  uploadId: number
  custodianName: string | null
  fileId: string | null
  fileSize: number
  isStructured: boolean
  mediaSourceType: number
  password: string | null
  nsfUserIdFileName: string | null
  nsfUserIdFile: string | null
}

/**
 * Interface for media status data
 */
export interface MediaStatusData {
  listMediaInfo: MediaInfo[]
  listUploadInfo: UploadInfo[]
  currentJobStatus: boolean
}

/**
 * Interface for custodian list data
 */
export interface CustodianListData {
  ignoreTiffJobsForMediaStatus: boolean
  custodianList: Custodian[]
}

/**
 * Interface for tracking upload info with date for sorting
 */
interface UploadInfoWithDate {
  uploadDate: Date
  uploadedBy: string
  isValid: boolean
}

/**
 * Interface for fetch result
 */
interface FetchResult<T> {
  type: FetchType
  data: T
}

@Injectable()
export class UploadStatusService {
  // Dependency injection using inject() instead of constructor
  readonly #http = inject(HttpClient)

  readonly #destroyRef = inject(DestroyRef)

  // Configuration defaults
  readonly #defaultEndpointConfig: EndpointConfig = {
    baseUrl: environment.apiUrl,
    processStatus: 'process/project/{projectId}/status',
    uploadStatus: 'Upload',
  }

  readonly #defaultPollingConfig: Partial<PollingConfig> = {
    pollingIntervalMs: DEFAULT_POLLING_INTERVAL_MS,
    isRVOD: false,
    userId: DEFAULT_USER_ID,
    isTranscript: false,
    isRepository: false,
    fsid: DEFAULT_FSID,
  }

  // Private properties for polling
  #endpointConfig: EndpointConfig

  #pollingConfig?: PollingConfig

  #pollingSubscription?: Subscription

  #isPolling = false

  #lastFetchType = FetchType.UPLOAD_STATUS

  #consecutiveErrorCount = 0

  // Cached upload info from most recent custodian data
  #recentUploadInfo: UploadInfoWithDate | null = null

  // Public signals that components can use
  public readonly statusCards = signal<StatusCard[]>([])

  public readonly processingTasks = signal<ProcessingTask[]>([])

  public readonly unprocessedFiles = signal<UnprocessedFilesNotification>({
    count: 0,
    canReprocess: false,
  })

  public readonly isLoading = signal<boolean>(false)

  public readonly lastUpdated = signal<Date | null>(null)

  public readonly hasError = signal<boolean>(false)

  constructor() {
    this.#endpointConfig = { ...this.#defaultEndpointConfig }
    this.#initializeEmptyData()
  }

  /**
   * Initializes the service with configuration and starts polling
   *
   * Sets up endpoints and polling configuration with the provided parameters
   * and starts periodic calls to fetch status data.
   *
   * @param {number} projectId - The project ID for calls
   * @param {string} sessionId - The session ID for calls
   * @param {Partial<PollingConfig>} [options] - Optional configuration parameters
   * @param {Partial<EndpointConfig>} [endpoints] - Optional endpoint overrides
   */
  public initialize(
    projectId: number,
    sessionId: string,
    options?: Partial<Omit<PollingConfig, 'projectId' | 'sessionId'>>,
    endpoints?: Partial<EndpointConfig>
  ): void {
    // Clear any existing data
    this.#clearData()

    // Update endpoints if provided
    if (endpoints) {
      this.#endpointConfig = { ...this.#endpointConfig, ...endpoints }
    }

    // Configure polling with required and optional parameters
    this.#pollingConfig = {
      ...this.#defaultPollingConfig,
      ...options,
      projectId,
      sessionId,
      pollingIntervalMs:
        options?.pollingIntervalMs || DEFAULT_POLLING_INTERVAL_MS,
    } as PollingConfig

    // Reset error count
    this.#consecutiveErrorCount = 0

    // Start polling
    this.startPolling()
  }

  /**
   * Clears all data maintained by the service
   *
   * Resets all signals to their initial state
   */
  #clearData(): void {
    this.#initializeEmptyData()
    this.#recentUploadInfo = null
    this.lastUpdated.set(null)
    this.hasError.set(false)
  }

  /**
   * Generates a random backoff time for throttling
   *
   * @returns {number} A random time between MIN_BACKOFF_MS and MAX_BACKOFF_MS
   */
  #getRandomBackoff(): number {
    return (
      Math.floor(Math.random() * (MAX_BACKOFF_MS - MIN_BACKOFF_MS + 1)) +
      MIN_BACKOFF_MS
    )
  }

  /**
   * Calculates a backoff interval based on consecutive errors
   *
   * Increases the polling interval when errors occur to reduce load
   *
   * @param {number} baseInterval - The base polling interval
   * @returns {number} The adjusted interval with backoff applied
   */
  #getErrorBackoff(baseInterval: number): number {
    // Apply exponential backoff for consecutive errors (max 5x the base interval)
    const backoffFactor = Math.min(
      Math.pow(1.5, this.#consecutiveErrorCount),
      5
    )
    return Math.floor(baseInterval * backoffFactor)
  }

  /**
   * Decides which type of data to fetch next
   *
   * Alternates between fetch types with randomization
   *
   * @returns {FetchType} The next fetch type to execute
   */
  #decideFetchType(): FetchType {
    // Randomly decide whether to switch or stick with the previous type
    const shouldSwitch = Math.random() > 0.5

    if (shouldSwitch) {
      return this.#lastFetchType === FetchType.PROCESS_STATUS
        ? FetchType.UPLOAD_STATUS
        : FetchType.PROCESS_STATUS
    }

    return this.#lastFetchType
  }

  /**
   * Starts polling for status updates
   *
   * Uses RxJS timer to periodically fetch status data from configured endpoints.
   * Will stop any existing polling before starting a new one.
   */
  public startPolling(): void {
    // Only start if configuration is available
    if (!this.#pollingConfig) {
      console.error(
        'Cannot start polling: service not initialized with projectId and sessionId'
      )
      return
    }

    // Stop any existing polling
    this.stopPolling()

    const { pollingIntervalMs } = this.#pollingConfig

    // Set polling flag
    this.#isPolling = true
    this.isLoading.set(true)

    // Initial data load - fetch both data types using forkJoin to avoid nested subscribes
    forkJoin({
      processStatus: this.#fetchProcessStatus(),
      uploadStatus: this.#fetchUploadStatus(),
    })
      .pipe(
        tap(({ processStatus, uploadStatus }) => {
          this.processCustodianList(processStatus)
          this.processMediaStatus(uploadStatus)
          this.isLoading.set(false)
          this.lastUpdated.set(dayjs().toDate())
        }),
        catchError((error: unknown) => {
          console.error('Error during initial data load:', error)
          this.hasError.set(true)
          this.isLoading.set(false)
          return of(null)
        }),
        // Start the alternating polling after initial load (success or failure)
        tap(() => {
          if (this.#isPolling) {
            this.#startPollingWithBackoff(pollingIntervalMs)
          }
        })
      )
      .subscribe()
  }

  /**
   * Starts polling with backoff strategy
   *
   * @param {number} baseInterval - The base polling interval in milliseconds
   */
  #startPollingWithBackoff(baseInterval: number): void {
    if (!this.#isPolling) return

    const fetchType = this.#decideFetchType()
    this.#lastFetchType = fetchType

    // Calculate backoff time with randomization and error adjustment
    const backoffTime = this.#getRandomBackoff()
    const adjustedInterval = this.#getErrorBackoff(backoffTime)

    // Use the timer to implement throttling between requests
    this.#pollingSubscription = timer(adjustedInterval)
      .pipe(
        tap(() => this.isLoading.set(true)),
        // Perform the appropriate fetch based on type
        switchMap(() => {
          if (!this.#isPolling) return EMPTY

          if (fetchType === FetchType.PROCESS_STATUS) {
            return this.#fetchProcessStatus().pipe(
              map(
                (data) =>
                  ({
                    type: FetchType.PROCESS_STATUS,
                    data,
                  } as FetchResult<CustodianListData>)
              ),
              catchError((error: unknown) => {
                console.error('Error fetching process status:', error)
                this.hasError.set(true)
                this.#consecutiveErrorCount++
                return EMPTY
              })
            )
          }
          return this.#fetchUploadStatus().pipe(
            map(
              (data) =>
                ({
                  type: FetchType.UPLOAD_STATUS,
                  data,
                } as FetchResult<MediaStatusData>)
            ),
            catchError((error: unknown) => {
              console.error('Error fetching upload status:', error)
              this.hasError.set(true)
              this.#consecutiveErrorCount++
              return EMPTY
            })
          )
        }),
        tap({
          next: (result) => {
            if (result) {
              if (result.type === FetchType.PROCESS_STATUS) {
                this.processCustodianList(result.data as CustodianListData)
              } else {
                this.processMediaStatus(result.data as MediaStatusData)
              }

              // Reset error count on successful fetch
              this.#consecutiveErrorCount = 0
              this.hasError.set(false)
              this.lastUpdated.set(dayjs().toDate())
            }
            this.isLoading.set(false)
          },
          error: (err: unknown) => {
            console.error('Unhandled error in polling:', err)
            this.isLoading.set(false)
            this.hasError.set(true)
            this.#consecutiveErrorCount++
          },
          complete: () => {
            // Continue polling with the next cycle if still active
            if (this.#isPolling) {
              this.#startPollingWithBackoff(baseInterval)
            }
          },
        }),
        takeUntilDestroyed(this.#destroyRef)
      )
      .subscribe()
  }

  /**
   * Stops polling
   *
   * Cancels the current polling subscription if active.
   */
  public stopPolling(): void {
    this.#isPolling = false

    if (this.#pollingSubscription) {
      this.#pollingSubscription.unsubscribe()
      this.#pollingSubscription = undefined
    }
  }

  /**
   * Processes media status data and updates the UI state
   *
   * This method transforms the raw media status data into UI-friendly structures
   * and updates the status cards and unprocessed files notification.
   *
   * @param {MediaStatusData} payload - The media status data containing
   *        list media info and upload info
   */
  public processMediaStatus(payload: MediaStatusData): void {
    // Create status cards with data
    const uploadCard = this.#createUploadStatusCard(payload)
    const processingCard = this.#createProcessingStatusCard(payload)
    const loadCard = this.#createLoadStatusCard(payload)

    const newCards = [uploadCard, processingCard, loadCard]

    // Always update the cards without checking for changes
    this.statusCards.set(newCards)

    // Calculate unprocessed count
    const unprocessedCount = payload.listMediaInfo.filter(
      (media) => media.mediaStatus === StatusCode.NOT_PROCESSED
    ).length

    // Always update with the new count
    this.unprocessedFiles.set({
      count: unprocessedCount,
      canReprocess: unprocessedCount > 0,
    })
  }

  /**
   * Processes custodian list data and updates the UI state
   *
   * This method transforms the raw custodian data into processing tasks
   * for the UI, preserving expanded states and updating overall statuses.
   * It also extracts and caches the most recent upload information.
   *
   * @param {CustodianListData} payload - The custodian list data
   *        containing detailed information about files and their processing status
   */
  public processCustodianList(payload: CustodianListData): void {
    // Update the cached most recent upload info
    this.#updateRecentUploadInfo(payload)

    // Transform custodian data into processing tasks
    const newProcessingTasks: ProcessingTask[] = payload.custodianList.flatMap(
      (custodian) =>
        custodian.mediaList.map((media) =>
          this.#createProcessingTask(media, custodian)
        )
    )

    // Preserve expanded state from existing tasks when updating
    const currentTasks = this.processingTasks()
    const mergedTasks = newProcessingTasks.map((newTask) => {
      const existingTask = currentTasks.find((task) => task.id === newTask.id)
      if (existingTask) {
        return { ...newTask, expanded: existingTask.expanded }
      }
      return newTask
    })

    // Always update with the merged tasks, don't check for changes
    this.processingTasks.set(mergedTasks)
  }

  /**
   * Compares status cards for meaningful changes
   *
   * @param {StatusCard[]} current - Current status cards
   * @param {StatusCard[]} updated - Updated status cards
   * @returns {boolean} True if there are meaningful changes
   */
  #hasStatusCardsChanged(
    current: StatusCard[],
    updated: StatusCard[]
  ): boolean {
    if (current.length !== updated.length) return true

    for (let i = 0; i < current.length; i++) {
      const c = current[i]
      const u = updated[i]

      if (c.type !== u.type) return true
      if (c.status !== u.status) return true
      if (c.progress.completed !== u.progress.completed) return true
      if (c.progress.total !== u.progress.total) return true

      // Compare files if present
      if (c.files?.length !== u.files?.length) return true

      if (c.files && u.files) {
        for (let j = 0; j < c.files.length; j++) {
          if (c.files[j].fileName !== u.files[j].fileName) return true
          if (c.files[j].percentComplete !== u.files[j].percentComplete)
            return true
          if (c.files[j].isProcessing !== u.files[j].isProcessing) return true
        }
      }
    }

    return false
  }

  /**
   * Compares processing tasks for meaningful changes
   *
   * @param {ProcessingTask[]} current - Current processing tasks
   * @param {ProcessingTask[]} updated - Updated processing tasks
   * @returns {boolean} True if there are meaningful changes
   */
  #hasProcessingTasksChanged(
    current: ProcessingTask[],
    updated: ProcessingTask[]
  ): boolean {
    if (current.length !== updated.length) return true

    // Map tasks by ID for easier comparison
    const currentMap = new Map(current.map((task) => [task.id, task]))

    for (const updatedTask of updated) {
      const currentTask = currentMap.get(updatedTask.id)

      // New task found
      if (!currentTask) return true

      // Check essential properties
      if (currentTask.status !== updatedTask.status) return true
      if (currentTask.progress.completed !== updatedTask.progress.completed)
        return true
      if (currentTask.progress.total !== updatedTask.progress.total) return true

      // Check processing stages
      if (
        currentTask.processingStages?.length !==
        updatedTask.processingStages?.length
      )
        return true

      if (currentTask.processingStages && updatedTask.processingStages) {
        for (let i = 0; i < currentTask.processingStages.length; i++) {
          const cs = currentTask.processingStages[i]
          const us = updatedTask.processingStages[i]

          if (cs.type !== us.type) return true
          if (cs.status !== us.status) return true
          if (cs.percentComplete !== us.percentComplete) return true
        }
      }

      // Check files
      if (currentTask.files?.length !== updatedTask.files?.length) return true

      if (currentTask.files && updatedTask.files) {
        for (let i = 0; i < currentTask.files.length; i++) {
          const cf = currentTask.files[i]
          const uf = updatedTask.files[i]

          if (cf.fileName !== uf.fileName) return true
          if (cf.percentComplete !== uf.percentComplete) return true
          if (cf.isProcessing !== uf.isProcessing) return true
        }
      }
    }

    return false
  }

  /**
   * Updates cached information about the most recent upload
   *
   * Extracts the most recent upload date and uploader name from custodian data
   *
   * @param {CustodianListData} payload - The custodian list data
   */
  #updateRecentUploadInfo(payload: CustodianListData): void {
    // Collect all media items from all custodians
    const allMedia: { media: Media; custodian: Custodian }[] = []

    payload.custodianList.forEach((custodian) => {
      custodian.mediaList.forEach((media) => {
        allMedia.push({ media, custodian })
      })
    })

    // Find the media with the most recent valid uploadStartedDate
    const uploads: UploadInfoWithDate[] = allMedia
      .map(({ media }) => {
        const uploadDate = this.#parseApiDate(media.uploadStartedDate)
        return {
          uploadDate,
          uploadedBy: media.uploadedBy || 'Unknown User',
          isValid: uploadDate.getTime() > new Date('2000-01-01').getTime(),
        }
      })
      .filter((info) => info.isValid)

    if (uploads.length > 0) {
      // Sort by date in descending order
      uploads.sort((a, b) => b.uploadDate.getTime() - a.uploadDate.getTime())
      this.#recentUploadInfo = uploads[0]
    } else {
      this.#recentUploadInfo = null
    }
  }

  /**
   * Retrieves a status card by its type
   *
   * @param {StatusCardType} type - The status card type to retrieve
   * @returns {StatusCard | undefined} The status card if found, undefined otherwise
   */
  public getStatusCardByType(type: StatusCardType): StatusCard | undefined {
    return this.statusCards().find((card) => card.type === type)
  }

  /**
   * Filters processing tasks by their status
   *
   * @param {ProcessingStatus} status - The processing status to filter by
   * @returns {ProcessingTask[]} Array of tasks with the specified status
   */
  public getProcessingTasksByStatus(
    status: ProcessingStatus
  ): ProcessingTask[] {
    return this.processingTasks().filter((task) => task.status === status)
  }

  /**
   * Toggles the expanded state of a task
   *
   * This allows showing/hiding detailed information about a task
   * in the UI without fetching new data.
   *
   * @param {string} taskId - The unique identifier of the task to toggle
   */
  public toggleTaskExpanded(taskId: string): void {
    this.processingTasks.update((tasks) => {
      return tasks.map((task) => {
        if (task.id === taskId) {
          return { ...task, expanded: !task.expanded }
        }
        return task
      })
    })
  }

  /**
   * Simulates cancelling a file in a task
   *
   * Removes the file from the task's files list and updates overall statuses.
   *
   * @param {string} taskId - The unique identifier of the task containing the file
   * @param {string} fileName - The name of the file to cancel
   */
  public cancelFile(taskId: string, fileName: string): void {
    this.processingTasks.update((tasks) => {
      const updatedTasks = tasks.map((task) => {
        if (task.id === taskId && task.files) {
          return {
            ...task,
            files: task.files.filter((file) => file.fileName !== fileName),
          }
        }
        return task
      })

      return updatedTasks
    })

    this.#updateOverallStatusFromTasks()
  }

  /**
   * Formats a date as a relative time string for display in the UI
   *
   * Converts a date to a user-friendly string like "Today", "Yesterday",
   * or "X days ago" based on the difference from the current date.
   *
   * @param {Date} date - The date to format
   * @returns {string} The formatted relative time string
   */
  public formatRelativeTime(date: Date): string {
    if (!date) return ''

    const today = dayjs().startOf('day')
    const targetDate = dayjs(date).startOf('day')
    const diffDays = today.diff(targetDate, 'day')

    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    }
    return `${diffDays} days ago`
  }

  /**
   * Fetches process status data
   *
   * Makes an HTTP GET request to the process status endpoint with
   * appropriate query parameters.
   *
   * @returns {Observable<CustodianListData>} Observable with the response
   */
  #fetchProcessStatus(): Observable<CustodianListData> {
    if (!this.#pollingConfig) {
      return EMPTY
    }

    const { projectId, sessionId, isRVOD, userId } = this.#pollingConfig

    // Construct the URL with path parameters using template interpolation
    let url = this.#endpointConfig.processStatus

    // Replace template parameters
    url = url.replace('{projectId}', String(projectId))

    // Construct the full URL
    const fullUrl = `${this.#endpointConfig.baseUrl}${url}`

    // Add query parameters
    const params = {
      sessionId,
      isRVOD: isRVOD?.toString() || 'false',
      userId: userId?.toString() || '1',
    }

    return this.#http.get<CustodianListData>(fullUrl, { params }).pipe(
      switchMap((data) => {
        // convert to camel case using service
        const camelCaseConvertorService = new CaseConvertorService()
        return from(
          camelCaseConvertorService.convertToCase<CustodianListData>(
            data,
            'camelCase'
          )
        )
      }),
      catchError((error: unknown) => {
        console.error('HTTP error fetching process status:', error)
        this.#consecutiveErrorCount++
        throw error
      })
    )
  }

  /**
   * Fetches upload status data
   *
   * Makes an HTTP GET request to the upload status endpoint with
   * appropriate query parameters.
   *
   * @returns {Observable<MediaStatusData>} Observable with the response
   */
  #fetchUploadStatus(): Observable<MediaStatusData> {
    if (!this.#pollingConfig) {
      return EMPTY
    }

    const { projectId, sessionId, isTranscript, isRepository, fsid } =
      this.#pollingConfig

    // Construct the URL
    const url = `${this.#endpointConfig.baseUrl}/${
      this.#endpointConfig.uploadStatus
    }`

    // Add query parameters with defaults
    const params: Record<string, string> = {
      projectId: String(projectId),
      SID: sessionId || 'undefined',
    }

    // Add optional parameters only if they're defined
    if (isTranscript !== undefined) {
      params.isTranscript = isTranscript.toString()
    }

    if (isRepository !== undefined) {
      params.isRepository = isRepository.toString()
    }

    if (fsid !== undefined) {
      params.fsid = fsid.toString()
    }

    return this.#http.get<MediaStatusData>(url, { params }).pipe(
      switchMap((data) => {
        // convert to camel case using service
        const camelCaseConvertorService = new CaseConvertorService()
        return from(
          camelCaseConvertorService.convertToCase<MediaStatusData>(
            data,
            'camelCase'
          )
        )
      }),
      catchError((error: unknown) => {
        console.error('HTTP error fetching upload status:', error)
        this.#consecutiveErrorCount++
        throw error
      })
    )
  }

  /**
   * Initializes empty data structures for the UI state
   *
   * Sets up initial empty status cards with default values to prevent
   * null reference errors before data is loaded.
   */
  #initializeEmptyData(): void {
    const now = dayjs().toDate()
    const defaultProgress = { completed: 0, total: 0 }
    const defaultStatus = ProcessingStatus.NOT_STARTED

    const emptyStatusCards: StatusCard[] = [
      {
        id: `${IdPrefix.CARD}${StatusCardType.UPLOAD}`,
        type: StatusCardType.UPLOAD,
        uploadedBy: 'Unknown User',
        date: now,
        lastUpdated: now,
        progress: defaultProgress,
        status: defaultStatus,
      },
      {
        id: `${IdPrefix.CARD}${StatusCardType.PROCESSING}`,
        type: StatusCardType.PROCESSING,
        uploadedBy: 'Unknown User',
        date: now,
        lastUpdated: now,
        progress: defaultProgress,
        status: defaultStatus,
      },
      {
        id: `${IdPrefix.CARD}${StatusCardType.LOAD}`,
        type: StatusCardType.LOAD,
        uploadedBy: 'Unknown User',
        date: now,
        lastUpdated: now,
        progress: defaultProgress,
        status: defaultStatus,
        files: [],
      },
    ]

    this.statusCards.set(emptyStatusCards)
    this.processingTasks.set([])
    this.unprocessedFiles.set({ count: 0, canReprocess: false })
  }

  /**
   * Creates an upload status card from media status data
   *
   * Processes upload information to determine completion counts and status.
   * Uses actual data for all fields whenever possible.
   *
   * @param {MediaStatusData} payload - The media status data
   * @returns {StatusCard} The created upload status card
   */
  #createUploadStatusCard(payload: MediaStatusData): StatusCard {
    const uploadItems = payload.listUploadInfo
    const completedCount = uploadItems.filter(
      (item) => item.uploadStatus === 2
    ).length
    const totalCount = uploadItems.length

    // Get the most recent upload info
    const { uploadedBy, date } = this.#getRecentUploadInfo()

    return {
      id: `${IdPrefix.CARD}${StatusCardType.UPLOAD}`,
      type: StatusCardType.UPLOAD,
      uploadedBy,
      date,
      lastUpdated: date, // Use same date for lastUpdated
      progress: { completed: completedCount, total: totalCount },
      status: this.#determineOverallStatus(completedCount, totalCount),
    }
  }

  /**
   * Creates a processing status card from media status data
   *
   * Processes media information to determine completion counts and status.
   * Uses actual data for all fields whenever possible.
   *
   * @param {MediaStatusData} payload - The media status data
   * @returns {StatusCard} The created processing status card
   */
  #createProcessingStatusCard(payload: MediaStatusData): StatusCard {
    const mediaItems = payload.listMediaInfo
    const completedCount = mediaItems.filter(
      (item) => item.mediaStatus === StatusCode.COMPLETED
    ).length
    const totalCount = mediaItems.length

    // Get the most recent upload info
    const { uploadedBy, date } = this.#getRecentUploadInfo()

    return {
      id: `${IdPrefix.CARD}${StatusCardType.PROCESSING}`,
      type: StatusCardType.PROCESSING,
      uploadedBy,
      date,
      lastUpdated: date, // Use same date for lastUpdated
      progress: { completed: completedCount, total: totalCount },
      status: this.#determineOverallStatus(completedCount, totalCount),
    }
  }

  /**
   * Creates a load status card from media status data
   *
   * Processes media and upload information to determine completion counts,
   * status, and file information for the load card. Uses actual data from
   * the data for all fields whenever possible.
   *
   * @param {MediaStatusData} payload - The media status data
   * @returns {StatusCard} The created load status card
   */
  #createLoadStatusCard(payload: MediaStatusData): StatusCard {
    const mediaItems = payload.listMediaInfo
    const completedCount = mediaItems.filter(
      (item) => item.mediaStatus === StatusCode.COMPLETED
    ).length
    const totalCount = mediaItems.length

    // Get the most recent upload info
    const { uploadedBy, date } = this.#getRecentUploadInfo()

    // Create file objects from upload info with dynamic processing percentages
    const files: FileInfo[] = payload.listUploadInfo.map((item) => {
      // Calculate percentage based on upload status from data
      let percentComplete = 0
      switch (item.uploadStatus) {
        case 0:
          percentComplete = 0
          break
        case 1:
          // For in-progress items, use completedPercentage if available
          percentComplete = 65 // Default in-progress value
          break
        case 2:
          percentComplete = 100
          break
      }

      return {
        fileName: item.fileName || item.mediaName,
        percentComplete,
        isProcessing: item.uploadStatus === 1,
        canCancel: item.uploadStatus !== 2,
      }
    })

    return {
      id: `${IdPrefix.CARD}${StatusCardType.LOAD}`,
      type: StatusCardType.LOAD,
      uploadedBy,
      date,
      lastUpdated: date, // Use same date for lastUpdated
      progress: { completed: completedCount, total: totalCount },
      status: this.#determineOverallStatus(completedCount, totalCount),
      files,
    }
  }

  /**
   * Gets the most recent upload information for display in cards
   *
   * Returns consistent uploadedBy and date information based on the
   * most recent upload from the custodian list data
   *
   * @returns {{uploadedBy: string, date: Date}} Upload information
   */
  #getRecentUploadInfo(): { uploadedBy: string; date: Date } {
    if (this.#recentUploadInfo && this.#recentUploadInfo.isValid) {
      return {
        uploadedBy: this.#recentUploadInfo.uploadedBy,
        date: this.#recentUploadInfo.uploadDate,
      }
    }

    // Fallback to latest date from processing tasks
    const tasks = this.processingTasks()
    if (tasks.length > 0) {
      // Sort by date in descending order
      const sortedTasks = [...tasks].sort(
        (a, b) => b.date.getTime() - a.date.getTime()
      )

      const recentTask = sortedTasks[0]
      return {
        uploadedBy: recentTask.uploadedBy || 'Unknown User',
        date: recentTask.date,
      }
    }

    // Last resort: current date
    return {
      uploadedBy: 'Unknown User',
      date: dayjs().toDate(),
    }
  }

  /**
   * Creates a processing task from media and custodian data
   *
   * Transforms raw data into a UI-friendly processing task structure
   * with calculated status, progress, and processing stages.
   * Uses data directly from the source whenever possible.
   *
   * @param {Media} media - The media data
   * @param {Custodian} custodian - The custodian data
   * @returns {ProcessingTask} The created processing task
   */
  #createProcessingTask(media: Media, custodian: Custodian): ProcessingTask {
    // Map status to ProcessingStatus enum
    const status = this.#mapApiStatusToEnum(media.status)

    // Extract processing stages if available
    const processingStages: ProcessingStage[] = media.jobList
      ? media.jobList.map((job) => this.#createProcessingStage(job))
      : []

    // Calculate progress based on stages or overall status
    const progress =
      processingStages.length > 0
        ? {
            completed: processingStages.filter(
              (stage) => stage.status === ProcessingStatus.COMPLETED
            ).length,
            total: processingStages.length,
          }
        : { completed: status === ProcessingStatus.COMPLETED ? 2 : 0, total: 2 }

    // Get custodian name directly from custodian.name
    const custodianName = custodian.name || 'Unknown Custodian'

    // Parse the upload date from uploadStartedDate
    const uploadDate = this.#parseApiDate(media.uploadStartedDate)

    // Use same date for lastUpdated to maintain consistency
    const lastUpdated = uploadDate

    // Check if data can be imported - only for Not Processed or Not Started states
    const canImportData =
      status === ProcessingStatus.NOT_STARTED ||
      media.status === 'Not Processed'

    // Create the task object with all required properties
    return {
      id: `${IdPrefix.TASK}${media.mediaId || media.uploadFileId}`,
      name: media.mediaName,
      custodianName: custodianName,
      uploadedBy: media.uploadedBy || 'Unknown User',
      date: uploadDate,
      lastUpdated,
      progress,
      status,
      processingStages:
        processingStages.length > 0 ? processingStages : undefined,
      canImportData,
      needsReprocessing: media.status === 'Not Processed',
      expanded: false,
    }
  }

  /**
   * Parses and validates a date from data
   *
   * Handles special cases like "0001-01-01T00:00:00" which indicates
   * an unset date. Uses fallbacks when primary date is invalid.
   *
   * @param {string} primaryDate - The primary date string to parse
   * @param {string} [fallbackDate] - Optional fallback date if primary is invalid
   * @returns {Date} The parsed date
   */
  #parseApiDate(primaryDate: string, fallbackDate?: string): Date {
    const minValidDate = '2000-01-01'
    const now = dayjs().toDate()

    // Try primary date
    if (primaryDate) {
      // Check for uninitialized date formats (handle both formats)
      if (
        primaryDate.includes('0001-01-01') ||
        primaryDate.includes('01-01-0001')
      ) {
        // Use fallback or current date
        return fallbackDate ? this.#parseApiDate(fallbackDate) : now
      }

      const parsed = dayjs(primaryDate)
      // Check if parsed date is valid and after min date
      if (parsed.isValid() && parsed.isAfter(minValidDate)) {
        return parsed.toDate()
      }
    }

    // Try fallback date
    if (fallbackDate) {
      const fallbackParsed = dayjs(fallbackDate)
      if (fallbackParsed.isValid() && fallbackParsed.isAfter(minValidDate)) {
        return fallbackParsed.toDate()
      }
    }

    // Last resort: current date
    return now
  }

  /**
   * Creates a processing stage from task data
   *
   * Transforms raw task data into a UI-friendly processing stage structure
   * with calculated percentage and status.
   *
   * @param {TaskStatus} task - The task data
   * @returns {ProcessingStage} The created processing stage
   */
  #createProcessingStage(task: TaskStatus): ProcessingStage {
    // Extract percentage and convert to number
    const percentComplete = parseFloat(task.percentage) || 0

    // Map status to enum
    const status = this.#mapApiStatusToEnum(task.status)

    return {
      type: task.taskName,
      percentComplete,
      status,
    }
  }

  /**
   * Maps status strings to ProcessingStatus enum values
   *
   * Handles various status string formats and converts
   * them to consistent enum values for the UI.
   *
   * @param {string} status - The status string
   * @returns {ProcessingStatus} The corresponding ProcessingStatus enum value
   */
  #mapApiStatusToEnum(status: string): ProcessingStatus {
    if (!status) return ProcessingStatus.NOT_STARTED

    const lowerStatus = status.toLowerCase()

    // Check for exact matches with status text constants
    if (lowerStatus === StatusText.COMPLETED.toLowerCase()) {
      return ProcessingStatus.COMPLETED
    }

    if (lowerStatus === StatusText.IN_PROGRESS.toLowerCase()) {
      return ProcessingStatus.INPROGRESS
    }

    if (
      lowerStatus === StatusText.NOT_STARTED.toLowerCase() ||
      lowerStatus === StatusText.NOT_PROCESSED.toLowerCase()
    ) {
      return ProcessingStatus.NOT_STARTED
    }

    // Add explicit handling for "Failed" status
    if (
      lowerStatus === StatusText.FAILED.toLowerCase() ||
      lowerStatus.includes('fail') ||
      lowerStatus.includes('error')
    ) {
      return ProcessingStatus.FAILED
    }

    // Fallback to partial matches
    if (lowerStatus.includes('complete')) return ProcessingStatus.COMPLETED
    if (lowerStatus.includes('progress')) return ProcessingStatus.INPROGRESS
    return ProcessingStatus.NOT_STARTED
  }

  /**
   * Calculates status based on completion percentage
   *
   * @param {number} percent - The completion percentage (0-100)
   * @returns {ProcessingStatus} The corresponding ProcessingStatus enum value
   */
  #getStatusFromPercent(percent: number): ProcessingStatus {
    if (percent === 100) return ProcessingStatus.COMPLETED
    if (percent > 0) return ProcessingStatus.INPROGRESS
    return ProcessingStatus.NOT_STARTED
  }

  /**
   * Calculates overall task status based on processing stages
   *
   * Analyzes all stages to determine if the task is completed, in progress,
   * or not started.
   *
   * @param {ProcessingStage[]} stages - The processing stages to analyze
   * @returns {ProcessingStatus} The overall task status
   */
  #calculateTaskStatus(stages: ProcessingStage[]): ProcessingStatus {
    if (!stages.length) return ProcessingStatus.NOT_STARTED

    // Check for failed stages first
    if (stages.some((stage) => stage.status === ProcessingStatus.FAILED)) {
      return ProcessingStatus.FAILED
    }

    if (stages.every((stage) => stage.status === ProcessingStatus.COMPLETED)) {
      return ProcessingStatus.COMPLETED
    } else if (
      stages.some((stage) => stage.status === ProcessingStatus.INPROGRESS)
    ) {
      return ProcessingStatus.INPROGRESS
    }
    return ProcessingStatus.NOT_STARTED
  }

  /**
   * Determines overall status based on completion ratio
   *
   * @param {number} completed - The number of completed items
   * @param {number} total - The total number of items
   * @returns {ProcessingStatus} The overall status
   */
  #determineOverallStatus(completed: number, total: number): ProcessingStatus {
    if (total === 0) return ProcessingStatus.NOT_STARTED
    if (completed === total) return ProcessingStatus.COMPLETED
    if (completed > 0) return ProcessingStatus.INPROGRESS
    return ProcessingStatus.NOT_STARTED
  }

  /**
   * Updates overall status cards from individual task data
   *
   * Recalculates completion statistics for upload, processing, and load
   * status cards based on the current state of all tasks and their stages.
   * This ensures that the overall status indicators accurately reflect
   * the detailed task information.
   */
  #updateOverallStatusFromTasks(): void {
    const tasks = this.processingTasks()

    // Calculate overall upload statistics
    const uploadTotal = tasks.length
    const uploadCompleted = tasks.filter(
      (task) => task.status === ProcessingStatus.COMPLETED
    ).length

    // Calculate overall processing statistics from all processing stages
    let processingTotal = 0
    let processingCompleted = 0

    tasks.forEach((task) => {
      if (task.processingStages && task.processingStages.length > 0) {
        processingTotal += task.processingStages.length
        processingCompleted += task.processingStages.filter(
          (stage) => stage.status === ProcessingStatus.COMPLETED
        ).length
      }
    })

    // Calculate overall load statistics from all files
    let loadTotal = 0
    let loadCompleted = 0

    tasks.forEach((task) => {
      if (task.files && task.files.length > 0) {
        loadTotal += task.files.length
        loadCompleted += task.files.filter(
          (file) => file.percentComplete === 100
        ).length
      }
    })

    // Get the most recent upload info to ensure consistent dates across cards
    const { uploadedBy, date } = this.#getRecentUploadInfo()

    // Create new status cards with the calculated statistics
    const newCards: StatusCard[] = [
      {
        id: `${IdPrefix.CARD}${StatusCardType.UPLOAD}`,
        type: StatusCardType.UPLOAD,
        uploadedBy,
        date,
        lastUpdated: date,
        progress: { completed: uploadCompleted, total: uploadTotal },
        status: this.#determineOverallStatus(uploadCompleted, uploadTotal),
      },
      {
        id: `${IdPrefix.CARD}${StatusCardType.PROCESSING}`,
        type: StatusCardType.PROCESSING,
        uploadedBy,
        date,
        lastUpdated: date,
        progress: { completed: processingCompleted, total: processingTotal },
        status: this.#determineOverallStatus(
          processingCompleted,
          processingTotal
        ),
      },
      {
        id: `${IdPrefix.CARD}${StatusCardType.LOAD}`,
        type: StatusCardType.LOAD,
        uploadedBy,
        date,
        lastUpdated: date,
        progress: { completed: loadCompleted, total: loadTotal },
        status: this.#determineOverallStatus(loadCompleted, loadTotal),
        files: this.#collectFilesFromTasks(tasks),
      },
    ]

    // Check if there are actual changes before updating
    const currentCards = this.statusCards()
    const hasChanges = this.#hasStatusCardsChanged(currentCards, newCards)

    if (hasChanges) {
      this.statusCards.set(newCards)
    }
  }

  /**
   * Collects all file information from tasks for display in status cards
   *
   * @param {ProcessingTask[]} tasks - The tasks containing file information
   * @returns {FileInfo[]} The collected file information
   */
  #collectFilesFromTasks(tasks: ProcessingTask[]): FileInfo[] {
    const allFiles: FileInfo[] = []

    tasks.forEach((task) => {
      if (task.files && task.files.length > 0) {
        allFiles.push(...task.files)
      }
    })

    return allFiles
  }
}
