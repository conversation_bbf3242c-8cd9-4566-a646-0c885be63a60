import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  input,
  OnInit,
  output,
  signal,
  untracked,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { eyeIcon, eyeSlashIcon } from '@progress/kendo-svg-icons'
import {
  SelectedFileMetaInfo,
  UploadSourceTypes,
} from '@venio/shared/models/interfaces'
import {
  ComboBoxComponent,
  NoDataTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import {
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { UploadFacade, UploadManagerService } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { filter, take } from 'rxjs'
import { De<PERSON>un<PERSON><PERSON>imer } from '@venio/util/utilities'
import { FormsModule } from '@angular/forms'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { DialogService } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-upload-selected-file-meta-info',
  standalone: true,
  imports: [
    CommonModule,
    ComboBoxComponent,
    TextBoxComponent,
    ButtonComponent,
    TextBoxSuffixTemplateDirective,
    SVGIconComponent,
    NoDataTemplateDirective,
    SvgLoaderDirective,
    FormsModule,
  ],
  templateUrl: './upload-selected-file-meta-info.component.html',
  styleUrl: './upload-selected-file-meta-info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadSelectedFileMetaInfoComponent implements OnInit {
  private readonly injector = inject(Injector)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly uploadFacade = inject(UploadFacade)

  private readonly dialogService = inject(DialogService)

  private readonly uploadManager = inject(UploadManagerService)

  public readonly fileMetaInfo = input<SelectedFileMetaInfo>()

  public readonly removeSelectedFile = output<SelectedFileMetaInfo>()

  public readonly iconEye = eyeIcon

  public readonly iconSlashEye = eyeSlashIcon

  private baseCustodians: string[] = []

  public custodianNames: string[] = []

  public readonly customCustodians = toSignal(
    this.uploadFacade.selectCustomCustodian$,
    {
      initialValue: [],
    }
  )

  public readonly passwordVisibilityMap = signal<Record<string, boolean>>({})

  private readonly loadedCustodians = toSignal(
    this.uploadFacade.selectCustodians$.pipe(
      filter((custodians) => Boolean(custodians))
    )
  )

  public readonly shouldShowInputControls = computed(() => {
    const { sourceType } = this.uploadManager.selectUploadSourceSelection()
    // const isMsTeam = socialMediaType === UploadSourceSocialMediaTypes.MSTEAM

    // TODO: MS team has some logic which will be added later
    return (
      sourceType === UploadSourceTypes.UNSTRUCTURED ||
      sourceType === UploadSourceTypes.SOCIAL_MEDIA
    )
  })

  public ngOnInit(): void {
    this.#populateCustodians()
  }

  /**
   * Toggles the password visibility for the current file.
   *
   * Retrieves the file meta info and updates the password visibility mapping.
   *
   * @returns {void}
   */
  public passwordVisibilityClicked(): void {
    const info = this.fileMetaInfo()
    const passwordVisibility = this.passwordVisibilityMap()
    const isVisible = passwordVisibility[info.fileId] || false
    this.passwordVisibilityMap.update((prev) => ({
      ...prev,
      [info.fileId]: !isVisible,
    }))
  }

  /**
   * Filters custodian names based on the provided value.
   *
   * Combines custom and base custodians and filters the list
   * to include only those names containing the provided value (case-insensitive).
   *
   * @param {string} value - The filter value.
   * @returns {void}
   */
  public handleFilter(value: string): void {
    const all = new Set(this.customCustodians()?.concat(this.baseCustodians))
    this.custodianNames = [...all].filter(
      (cust) =>
        cust.trim().toLowerCase().indexOf(value.trim().toLowerCase()) !== -1
    )
  }

  /**
   * Adds a new custom custodian.
   *
   * Stores the new custodian via the upload facade, hides the ComboBox dropdown,
   * and updates the selected file meta information.
   *
   * @param {ComboBoxComponent} comp - The ComboBox component.
   * @returns {void}
   */
  public addCustomCustodian(comp: ComboBoxComponent): void {
    this.uploadFacade.storeCustomCustodian(comp.text)
    comp.toggle(false)
    this.#updateSelectedFileMetaInfo({ custodianName: comp.text })
  }

  public idFileSelect(ev: Event): void {
    const e = ev.target as HTMLInputElement
    const file = e.files[0]
    if (!file?.name?.endsWith('.id')) return

    this.uploadManager.selectedIdFiles.set(this.fileMetaInfo().fileId, file)

    this.#updateSelectedFileMetaInfo({ idFileName: file.name })
  }

  /**
   * Blur event handler for the custodian ComboBox. A workaround solution to prevent
   * the user from entering a custom custodian that is not in the list.
   * The custom custodian is added only when user clicks the add button.
   *
   * When the [allowCustom] is set, the control automatically adds input that is not in the selection options,
   * so we need to remove it from the input if it is not in the selection options which user adds it using the add button.
   *
   * If the value is selected from base custodians, then no action is taken.
   *
   * @param {ComboBoxComponent} comp - the ComboBox component
   * @returns {void}
   * @see baseCustodians
   * @see customCustodians
   * @see addCustomCustodian
   */
  @DebounceTimer(1) // Allow store to be updated if user clicks the add button
  public custodianBlur(comp: ComboBoxComponent): void {
    const value = comp.text
    if (this.baseCustodians?.includes(value)) return

    if (!this.customCustodians()?.includes(value)) {
      comp.value = null
    }
  }

  /**
   * Debounced change handler for file meta information.
   *
   * Updates the selected file meta information property with the new value.
   *
   * @param {string} value - The new value.
   * @param {string} prop - The property to update.
   * @returns {void}
   */
  @DebounceTimer(100)
  public fileMetaInfoChange(
    value: string,
    prop: keyof SelectedFileMetaInfo
  ): void {
    this.#updateSelectedFileMetaInfo({ [prop]: value })
  }

  /**
   * Initiates the file removal process.
   *
   * Launches a confirmation dialog and, upon confirmation, emits the removal event.
   *
   * @returns {void}
   */
  public removeFile(): void {
    this.#launchAndSetupConfirmationDialog()
  }

  /**
   * Populates base custodians and custodian names.
   *
   * Combines loaded custodians with custom custodians and updates the lists.
   *
   * @returns {void}
   */
  #populateCustodians(): void {
    effect(
      () => {
        const custodians = this.loadedCustodians() || []
        const customCustodians = this.customCustodians() || []
        const hasCustodians = custodians.length || customCustodians.length
        if (!hasCustodians) return

        untracked(() => {
          this.baseCustodians = custodians
          this.custodianNames = [
            ...new Set([...this.baseCustodians, ...customCustodians]),
          ]
        })
      },
      { injector: this.injector }
    )
  }

  /**
   * Updates the selected file meta information.
   *
   * Merges the existing meta info with the new data and updates it via the upload facade.
   *
   * @param {Partial<SelectedFileMetaInfo>} newInfo - Object containing updated meta information.
   * @returns {void}
   */
  #updateSelectedFileMetaInfo(newInfo: Partial<SelectedFileMetaInfo>): void {
    const prevInfo = this.fileMetaInfo()
    this.uploadFacade.updateSelectedFileMetaInfo({
      ...prevInfo,
      ...newInfo,
    })
  }

  /**
   * Sets the dialog input for the confirmation dialog.
   *
   * Configures the title and message of the dialog using the current file meta information.
   *
   * @param {ConfirmationDialogComponent} instance - The instance of the confirmation dialog.
   * @returns {void}
   */
  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Delete'
    instance.message = `Are you sure you want to remove selected file <b>${
      this.fileMetaInfo().name
    }?</b>`
  }

  /**
   * Launches and sets up the confirmation dialog.
   *
   * Opens the dialog, waits for confirmation, and emits the file removal event if confirmed.
   *
   * @returns {void}
   */
  #launchAndSetupConfirmationDialog(): void {
    const confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
      appendTo: this.viewContainerRef,
    })

    this.#setDialogInput(confirmationDialogRef.content.instance)

    const isConfirmed = toSignal(
      confirmationDialogRef.result.pipe(
        filter((action) => typeof action === 'boolean' && action),
        take(1)
      ),
      { injector: this.injector }
    )

    effect(
      () => {
        if (isConfirmed()) {
          untracked(() => this.removeSelectedFile.emit(this.fileMetaInfo()))
        }
      },
      { injector: this.injector }
    )
  }
}
