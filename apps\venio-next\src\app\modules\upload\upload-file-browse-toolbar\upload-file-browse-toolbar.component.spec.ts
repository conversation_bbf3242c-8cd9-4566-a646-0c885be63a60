import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadFileBrowseToolbarComponent } from './upload-file-browse-toolbar.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, signal } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { UploadFacade, UploadManagerService } from '@venio/data-access/common'
import { of } from 'rxjs'
import { UploadSourceSelection } from '@venio/shared/models/interfaces'

describe('UploadFileBrowseToolbarComponent', () => {
  let component: UploadFileBrowseToolbarComponent
  let fixture: ComponentFixture<UploadFileBrowseToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadFileBrowseToolbarComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: UploadManagerService,
          useValue: {
            uploadMultipleFiles: jest.fn(),
            formattedFileInfo: signal([]),
            clearFileWhenSourceChange: jest.fn(),
          } satisfies Partial<UploadManagerService>,
        },
        {
          provide: UploadFacade,
          useValue: {
            updateActiveStepperIndex: jest.fn(),
            resetUploadState: jest.fn(),
            updateUploadSourceSelection: jest.fn(),
            selectStepperActiveIndex$: of(0),
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
          } satisfies Partial<UploadFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadFileBrowseToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
