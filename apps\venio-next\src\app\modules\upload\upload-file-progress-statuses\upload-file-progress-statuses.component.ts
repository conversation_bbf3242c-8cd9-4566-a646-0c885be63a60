// upload-file-progress-statuses.component.ts
// (Content is identical to the previous response's version of this file)

import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnInit,
  output,
  signal, // Import signal
} from '@angular/core'
import { CommonModule, DatePipe } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  CircularProgressbarCenterTemplateDirective,
  CircularProgressBarComponent,
  LabelFn,
  LabelSettings,
  ProgressBarComponent,
  ProgressColor,
} from '@progress/kendo-angular-progressbar'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { xIcon } from '@progress/kendo-svg-icons'
import {
  DEFAULT_POLLING_INTERVAL_MS,
  DEFAULT_USER_ID,
  ProcessingStage,
  ProcessingStatus,
  StatusCardType,
  UploadFacade,
  UploadManagerService, // Ensure this is correctly imported
  UploadStatusService,
  UserFacade,
} from '@venio/data-access/common'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  CaseDetailModel,
  UploadLocalStorageKeys,
  UploadSourceTypes,
} from '@venio/shared/models/interfaces'
import { toSignal } from '@angular/core/rxjs-interop'
import { filter } from 'rxjs'
import { LocalStorage } from '@venio/shared/storage'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { isEqual } from 'lodash'

@Component({
  selector: 'venio-upload-file-progress-statuses',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    CircularProgressBarComponent,
    CircularProgressbarCenterTemplateDirective,
    ProgressBarComponent,
    SvgLoaderDirective,
    SVGIconComponent,
    DatePipe,
  ],
  templateUrl: './upload-file-progress-statuses.component.html',
  styleUrl: './upload-file-progress-statuses.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [UploadStatusService],
})
export class UploadFileProgressStatusesComponent implements OnInit {
  public readonly uploadStatusAction =
    output<Partial<Record<CommonActionTypes, CaseDetailModel>>>()

  public readonly closeIcon = xIcon

  public readonly StatusCardType = StatusCardType

  public readonly ProcessingStatus = ProcessingStatus

  readonly #userFacade = inject(UserFacade)

  readonly #uploadStatusService = inject(UploadStatusService)

  readonly #uploadFacade = inject(UploadFacade)

  readonly #uploadManagerService = inject(UploadManagerService) // Inject manager service

  readonly #selectedCaseInfo = toSignal(
    this.#uploadFacade.selectSelectedCaseInfo$.pipe(
      filter((info): info is CaseDetailModel => Boolean(info))
    )
  )

  readonly #selectCurrentUserInfo = toSignal(
    this.#userFacade.selectCurrentUserDetails$.pipe(
      filter((info) => Boolean(info))
    )
  )

  public readonly uploadStatusHeaderLabel = computed(() => {
    const sourceSelection =
      this.#uploadManagerService?.selectUploadSourceSelection()
    const sourceType = sourceSelection?.sourceType // Access sourceType safely

    switch (sourceType) {
      case UploadSourceTypes.UNSTRUCTURED:
        return 'Unstructured Data'
      case UploadSourceTypes.STRUCTURED:
        return 'Structured Data'
      case UploadSourceTypes.TRANSCRIPT:
        return 'Transcript Data'
      case UploadSourceTypes.SOCIAL_MEDIA:
        return 'Social Media Data'
      default:
        return 'Status'
    }
  })

  private readonly sessionId = computed(() =>
    LocalStorage.get<string>(UploadLocalStorageKeys.UploadRegisteredSessionId)
  )

  public showLoadCardDetails = signal(false) // Use signal

  public colors: ProgressColor[] = [
    { from: 0, to: 64, color: '#FFBC3E' },
    { from: 65, to: 99, color: '#1DBADC' },
    { from: 100, to: 100, color: '#9BD2A7' },
  ]

  public failedColor = '#ED7428'

  public progressMax = 100

  public progressLabel: LabelSettings = {}

  // Get the list directly from the manager service's signal
  public readonly currentQueuedUploads = computed(() =>
    this.#uploadManagerService.selectedFiles()
  )

  // Derive overall progress using the derived total count from the service
  public readonly overallLoadProgress = computed(
    () => {
      // Use the total selected files count for the total
      const total = this.#uploadManagerService.totalSelectedFileCount()
      // Use the totalQueuedUploadedFileCount for completed files
      const completed =
        this.#uploadManagerService.totalQueuedUploadedFileCount()
      const percentage = total === 0 ? 0 : Math.round((completed / total) * 100)
      return { completed, total, percentage }
    },
    { equal: isEqual }
  )

  // Show load card if there are files and not all are complete
  public readonly shouldShowLoadCard = computed(() => {
    const total = this.#uploadManagerService.totalSelectedFileCount()
    const uploadedCount =
      this.#uploadManagerService.totalQueuedUploadedFileCount()

    return total > 0 && uploadedCount < total
  })

  public ngOnInit(): void {
    this.#initUiState()
    this.#initializeUploadStatus()
  }

  public formatter: LabelFn = (value: number): string => {
    return `${value}/${this.progressMax}`
  }

  public getProgressBarStyle(value: number): { [key: string]: string } {
    let color = ''
    if (value >= 0 && value <= 50) color = '#FFBB12'
    else if (value > 50 && value <= 75) color = '#1DBADC'
    else if (value > 75) color = '#9BD2A7'
    else color = '#EDEBE9'
    return { background: color }
  }

  public getFailedProgressBarStyle(): { [key: string]: string } {
    return { background: this.failedColor }
  }

  public getProgressPercentage(completed: number, total: number): number {
    if (total <= 0) return 0
    return Math.round((completed / total) * 100)
  }

  public getStageProgressColors(stage: ProcessingStage): ProgressColor[] {
    if (stage.status === ProcessingStatus.FAILED) {
      return [{ from: 0, to: 100, color: this.failedColor }]
    }
    return this.colors
  }

  public openImportDataDialog(taskId: string, event: Event): void {
    event.stopPropagation()
    const caseInfo = this.#selectedCaseInfo()
    if (caseInfo) {
      this.uploadStatusAction.emit({ [CommonActionTypes.IMPORT]: caseInfo })
    }
  }

  public toggleTaskExpanded(taskId: string): void {
    this.#uploadStatusService.toggleTaskExpanded(taskId)
  }

  public toggleLoadCardDetails(): void {
    this.showLoadCardDetails.update((v) => !v)
  }

  public reprocessFiles(): void {
    const caseInfo = this.#selectedCaseInfo()
    if (caseInfo) {
      this.uploadStatusAction.emit({
        [CommonActionTypes.UPLOAD_REPROCESS]: caseInfo,
      })
    }
  }

  public cancelFile(fileId: string, statusTaskId?: string): void {
    // Always cancel via manager service using fileId
    this.#uploadManagerService.cancelUpload(fileId)

    // If it's also tracked as a status task (e.g., failed), notify status service
    if (statusTaskId) {
      const task = this.#uploadStatusService
        .processingTasks()
        .find((t) => t.id === statusTaskId)
      const fileName = task?.name // Assuming task name is filename
      if (fileName) {
        this.#uploadStatusService.cancelFile(statusTaskId, fileName)
      }
    }
  }

  public formatRelativeTime(date: Date | string | undefined): string {
    if (!date) return ''
    const dateObj = typeof date === 'string' ? new Date(date) : date
    // Add validation for Date object
    if (isNaN(dateObj.getTime())) return ''
    return this.#uploadStatusService.formatRelativeTime(dateObj)
  }

  // Signals from UploadStatusService
  public readonly uploadCard = computed(() =>
    this.#uploadStatusService.getStatusCardByType(StatusCardType.UPLOAD)
  )

  public readonly processingCard = computed(() =>
    this.#uploadStatusService.getStatusCardByType(StatusCardType.PROCESSING)
  )

  public readonly completedTasks = computed(() =>
    this.#uploadStatusService.getProcessingTasksByStatus(
      ProcessingStatus.COMPLETED
    )
  )

  public readonly inProgressTasks = computed(() =>
    this.#uploadStatusService.getProcessingTasksByStatus(
      ProcessingStatus.INPROGRESS
    )
  )

  public readonly notStartedTasks = computed(() =>
    this.#uploadStatusService.getProcessingTasksByStatus(
      ProcessingStatus.NOT_STARTED
    )
  )

  public readonly failedTasks = computed(() =>
    this.#uploadStatusService.getProcessingTasksByStatus(
      ProcessingStatus.FAILED
    )
  )

  public readonly unprocessedFiles = computed(() =>
    this.#uploadStatusService.unprocessedFiles()
  )

  #initUiState(): void {
    this.progressLabel = {
      format: this.formatter,
      position: 'end',
      visible: true, // Or false based on design
    }
  }

  #initializeUploadStatus(): void {
    const caseInfo = this.#selectedCaseInfo()
    const userInfo = this.#selectCurrentUserInfo()
    const currentSessionId = this.sessionId()

    if (!caseInfo || !userInfo || !currentSessionId) {
      console.warn(
        'Cannot initialize UploadStatusService: Missing required info.'
      )
      return
    }

    const { projectId } = caseInfo
    const { userRole, userId } = userInfo
    const isExternal = userRole?.toLowerCase() === 'external'

    this.#uploadStatusService.initialize(projectId, currentSessionId, {
      pollingIntervalMs: DEFAULT_POLLING_INTERVAL_MS,
      isRVOD: false,
      userId: isExternal ? userId : DEFAULT_USER_ID,
      isTranscript: false,
      isRepository: false,
      fsid: -1,
    })
  }
}
