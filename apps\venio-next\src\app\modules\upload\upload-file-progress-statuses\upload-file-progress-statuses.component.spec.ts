import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadFileProgressStatusesComponent } from './upload-file-progress-statuses.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, signal } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import {
  FileValidationService,
  UnprocessedFilesNotification,
  UploadFacade,
  UploadManagerService,
  UploadStatusService,
  UserFacade,
} from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  CaseDetailModel,
  SelectedFileMetaInfo,
  UploadLocalStorageKeys,
  UploadSourceSelection,
  UserModel,
} from '@venio/shared/models/interfaces'

// Mock LocalStorage
jest.mock('@venio/shared/storage', () => ({
  LocalStorage: {
    get: jest.fn((key) => {
      if (key === UploadLocalStorageKeys.UploadRegisteredSessionId) {
        return 'mock-session-id-123'
      }
      return null
    }),
    set: jest.fn(),
  },
}))

describe('UploadFileProgressStatusesComponent', () => {
  let component: UploadFileProgressStatusesComponent
  let fixture: ComponentFixture<UploadFileProgressStatusesComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      teardown: { destroyAfterEach: true },
      imports: [UploadFileProgressStatusesComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: UploadFacade,
          useValue: {
            updateActiveStepperIndex: jest.fn(),
            resetUploadState: jest.fn(),
            updateUploadSourceSelection: jest.fn(),
            selectStepperActiveIndex$: of(0),
            selectSelectedCaseInfo$: of({} as CaseDetailModel),
            selectSelectedFileMetaInfo$: of([] as SelectedFileMetaInfo[]),
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
          } satisfies Partial<UploadFacade>,
        },
        {
          provide: UploadManagerService,
          useValue: {
            uploadMultipleFiles: jest.fn(),
            formattedFileInfo: signal([]),
            selectUploadSourceSelection: signal({} as UploadSourceSelection),
            selectSelectedFileMetaInfo: signal([] as SelectedFileMetaInfo[]),
          } satisfies Partial<UploadManagerService>,
        },
        {
          provide: FileValidationService,
          useValue: {
            validateFiles: jest.fn(),
          } satisfies Partial<FileValidationService>,
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserDetails$: of({} as UserModel),
          } satisfies Partial<UserFacade>,
        },
        {
          provide: UploadStatusService,
          useValue: {
            initialize: jest.fn(),
            startPolling: jest.fn(),
            stopPolling: jest.fn(),
            formatRelativeTime: jest.fn(),
            getStatusCardByType: jest.fn(),
            getProcessingTasksByStatus: jest.fn(),
            unprocessedFiles: signal({} as UnprocessedFilesNotification),
            toggleTaskExpanded: jest.fn(),
            cancelFile: jest.fn(),
            isLoading: signal(false),
            lastUpdated: signal(new Date()),
            hasError: signal(false),
            statusCards: signal([]),
            processingTasks: signal([]),
            processCustodianList: jest.fn(),
            processMediaStatus: jest.fn(),
          } satisfies Partial<UploadStatusService>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadFileProgressStatusesComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
