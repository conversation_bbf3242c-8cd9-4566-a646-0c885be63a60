import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadSupportedFileTypeInfoComponent } from './upload-supported-file-type-info.component'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { of } from 'rxjs'
import { UploadFacade } from '@venio/data-access/common'
import { UploadSourceSelection } from '@venio/shared/models/interfaces'

describe('UploadSupportedFileTypeInfoComponent', () => {
  let component: UploadSupportedFileTypeInfoComponent
  let fixture: ComponentFixture<UploadSupportedFileTypeInfoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadSupportedFileTypeInfoComponent],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: UploadFacade,
          useValue: {
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
          } satisfies Partial<UploadFacade>,
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadSupportedFileTypeInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
