import { inject, Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import {
  AddedFilesList,
  MSTeamHierarchyModel,
  MSteamModel,
  NsfUserIdFile,
  QueuedRepositoryModel,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { Observable } from 'rxjs'

@Injectable({ providedIn: 'root' })
export class UploadService {
  private readonly httpClient = inject(HttpClient)

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public processRepository$<ResponseModel>(
    projectId: number,
    repositoryFiles: QueuedRepositoryModel[],
    nsfUserIdFiles: NsfUserIdFile[]
  ): Observable<ResponseModel> {
    const formData = new FormData()
    // nsfUserIdFiles.forEach((x) => {
    //   formData.append(x.fileId.toString(), x.userIdFile)
    // })

    repositoryFiles.forEach((repo) => {
      repo.repositoryHierarchies.forEach((hierarchy) => {
        if (hierarchy.nsfUserIdFiles) {
          formData.append(hierarchy.id, hierarchy.nsfUserIdFiles)
        }
      })
    })

    formData.append('QueuedRepositoryModel', JSON.stringify(repositoryFiles))

    const docUrl = this._apiUrl + `repository/project/${projectId}/process`
    return this.httpClient.post<ResponseModel>(docUrl, formData, {
      params: new HttpParams()
        .set('externalUserId', String(1))
        .set('isStructured', String(false))
        .set('isTranscript', String(false)),
    })
  }

  public startRepositoryProcessing(
    projectId: number,
    sessionId: string,
    UploadedFileModel: AddedFilesList[],
    externalUserId: number,
    isTranscript: boolean,
    mediaSourceType: string,
    isOverLay: boolean,
    isRepository: boolean,
    isMSTeam: boolean,
    msTeamModel: MSteamModel,
    msTeamHierarchies: MSTeamHierarchyModel[],
    isRepositoryUpload: boolean,
    isAwsS3Upload: boolean
  ): Observable<ResponseModel> {
    const msTableParam = {
      uploadedFileModel: UploadedFileModel,
      msTeamModel: msTeamModel,
      msTeamHierarchies: msTeamHierarchies,
    }

    const docUrl = this._apiUrl + `process/project/${projectId}`
    return this.httpClient.post<ResponseModel>(docUrl, msTableParam, {
      params: new HttpParams()
        .set('sessionId', sessionId)
        .set('externalUserId', String(externalUserId))
        .set('isTranscript', String(isTranscript))
        .set('mediaSourceType', String(mediaSourceType))
        .set('isOverLay', String(isOverLay))
        .set('isRepository', String(isRepository))
        .set('isMSTeam', String(isMSTeam))
        .set('isRepositoryUpload', String(isRepositoryUpload))
        .set('isAwsS3Upload', String(isAwsS3Upload)),
    })
  }

  public fetchCustodians$<ResponseModel>(
    projectId: number
  ): Observable<ResponseModel> {
    const docUrl = this._apiUrl + `project/${projectId}/custodian`
    return this.httpClient.get<ResponseModel>(docUrl)
  }
}
