import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadSourceSelectorComponent } from './upload-source-selector.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'

describe('UploadSourceSelectorComponent', () => {
  let component: UploadSourceSelectorComponent
  let fixture: ComponentFixture<UploadSourceSelectorComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadSourceSelectorComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadSourceSelectorComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
