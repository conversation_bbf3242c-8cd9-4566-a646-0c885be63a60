import {
  MediaSourceType,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'

/**
 * Utility service for mapping between upload source selections and media source types
 */
export class MediaSourceTypeMapper {
  /**
   * Maps an UploadSourceSelection to the appropriate MediaSourceType
   *
   * @param sourceOption The upload source selection to map
   * @returns The corresponding MediaSourceType
   */
  public static mapToMediaSourceType(
    sourceOption: UploadSourceSelection
  ): MediaSourceType {
    if (!sourceOption) {
      return MediaSourceType.SOURCE_FILE
    }

    const uploadSourceType = sourceOption.sourceType

    switch (uploadSourceType) {
      case 'UNSTRUCTURED':
      case 'STRUCTURED':
      case 'TRANSCRIPT':
        return MediaSourceType.SOURCE_FILE

      case 'SOCIAL_MEDIA':
        if (!sourceOption.socialMediaType) {
          return MediaSourceType.SOURCE_FILE
        }

        switch (sourceOption.socialMediaType) {
          case 'FACEBOOK':
            return MediaSourceType.SOCIAL_MEDIA_FACEBOOK
          case 'SLACK':
            return MediaSourceType.SOCIAL_MEDIA_SLACK
          case 'CELLEBRITE':
            return MediaSourceType.SOCIAL_MEDIA_CELLEBRITE
          case 'BLOOMBERG':
            return MediaSourceType.SOCIAL_MEDIA_BLOOMBERG
          case 'TWITTER':
            return MediaSourceType.SOCIAL_MEDIA_TWITTER
          case 'MSTEAM':
            return MediaSourceType.SOCIAL_MEDIA_MSTEAM
          case 'RSMF':
            return MediaSourceType.RSMF
          default:
            return MediaSourceType.SOURCE_FILE
        }

      default:
        return MediaSourceType.SOURCE_FILE
    }
  }
}
