import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  DropDownListComponent,
  HeaderTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { UploadManagerService, UploadFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  MediaSourceDropdownItem,
  UploadMediaTypes,
  UploadSourceTypes,
} from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-upload-file-browse-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DropDownListComponent,
    HeaderTemplateDirective,
    SvgLoaderDirective,
  ],
  templateUrl: './upload-file-browse-toolbar.component.html',
  styleUrl: './upload-file-browse-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadFileBrowseToolbarComponent implements OnInit {
  private readonly uploadFacade = inject(UploadFacade)

  private readonly uploadService = inject(UploadManagerService)

  private readonly sourceSelectionInfo = toSignal(
    this.uploadFacade.selectUploadSourceSelection$
  )

  public readonly mediaSourceItems = computed<MediaSourceDropdownItem[]>(
    () => this.sourceSelectionInfo()?.mediaSourceItems || []
  )

  public readonly selectedMediaTypeValue = computed<UploadMediaTypes>(
    () => this.sourceSelectionInfo()?.mediaSourceType?.value
  )

  public readonly isBatchMediaType = computed<boolean>(
    () => this.selectedMediaTypeValue() === UploadMediaTypes.BATCH_MEDIA
  )

  public readonly selectedSourceText = computed<string>(() => {
    const source = this.sourceSelectionInfo()?.sourceType?.toLowerCase()
    const isSocialMedia =
      this.sourceSelectionInfo()?.sourceType === UploadSourceTypes.SOCIAL_MEDIA
    const socialMedia = isSocialMedia
      ? `(${this.sourceSelectionInfo()?.socialMediaType?.toLowerCase()})`
      : ''
    return `${source} Data ${socialMedia} `.trim().replace(/_/g, ' ')
  })

  public ngOnInit(): void {
    this.uploadService.clearFileWhenSourceChange()
  }

  public mediaTypeSelectionChange(
    mediaSourceType: MediaSourceDropdownItem
  ): void {
    // Clear existing file states when the media source type changes
    this.#clearExistingFileStates()
    this.uploadFacade.updateUploadSourceSelection({
      mediaSourceType,
    })
  }

  #clearExistingFileStates(): void {
    this.uploadFacade.resetUploadState([
      'customCustodians',
      'selectedFileMetaInfo',
      'fileFolderToUpload',
    ])
  }
}
