import { Action, createReducer, on } from '@ngrx/store'
import * as UploadActions from './upload.actions'
import { resetStateProperty } from '@venio/util/utilities'
import {
  CaseDetailModel,
  QueuedRepositoryModel,
  ResponseModel,
  SelectedFileMetaInfo,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'

export const UPLOAD_FEATURE_KEY = 'uploadStore'

export interface UploadState {
  /**
   * State for storing the selected source of the upload.
   * THe state is partially updated based on the source selected.
   */
  uploadSourceSelection: UploadSourceSelection | undefined

  /**
   * State for storing the active stepper index.
   *
   * When the user selects a source, the stepper index is updated to the next step.
   * The stepper index is used to navigate between the steps of the upload process.
   */
  activeStepperIndex: number | undefined

  selectedCaseInfo: CaseDetailModel

  fileFolderToUpload: QueuedRepositoryModel[]
  selectedFileMetaInfo: SelectedFileMetaInfo[] | undefined
  custodians: string[]
  customCustodians: string[] | undefined

  processFromRepositorySuccess: ResponseModel
  processFromRepositoryError: ResponseModel
}

export const uploadState: UploadState = {
  uploadSourceSelection: undefined,
  // Initially, the first step is active
  activeStepperIndex: 0,
  selectedCaseInfo: undefined,
  fileFolderToUpload: [],
  custodians: [],
  processFromRepositorySuccess: undefined,
  processFromRepositoryError: undefined,
  selectedFileMetaInfo: undefined,
  customCustodians: undefined,
}

const reducer = createReducer<UploadState>(
  uploadState,
  on(UploadActions.resetUploadState, (state, { stateKey }) =>
    resetStateProperty<UploadState>(state, uploadState, stateKey)
  ),
  on(
    UploadActions.updateUploadSourceSelection,
    (state, { uploadSourceSelection }) => {
      return {
        ...state,
        uploadSourceSelection: {
          ...state.uploadSourceSelection,
          ...uploadSourceSelection,
        },
      }
    }
  ),
  on(
    UploadActions.updateActiveStepperIndex,
    (state, { activeStepperIndex }) => {
      return {
        ...state,
        activeStepperIndex,
      }
    }
  ),
  on(UploadActions.StoreSelectedCaseInfo, (state, { selectedCaseInfo }) => {
    return {
      ...state,
      selectedCaseInfo,
    }
  }),
  on(UploadActions.addFileFolder, (state, { fileFolder }) => {
    return {
      ...state,
      fileFolderToUpload: [...state.fileFolderToUpload, fileFolder],
      custodians: [
        ...new Set([...state.custodians, fileFolder.custodianName]),
      ].filter((c) => c !== ''),
    }
  }),
  on(UploadActions.updateFileFolder, (state, { fileFolder }) => {
    return {
      ...state,
      fileFolderToUpload: state.fileFolderToUpload.map((f) =>
        f.id === fileFolder.id ? { ...f, ...fileFolder } : f
      ),
      custodians: [
        ...new Set([...state.custodians, fileFolder.custodianName]),
      ].filter((c) => c !== ''),
    }
  }),
  on(UploadActions.updateFileFolderInBulk, (state, { fileFolders }) => {
    return {
      ...state,
      fileFolderToUpload: fileFolders,
    }
  }),
  on(UploadActions.removeFileFolder, (state, { folderId }) => {
    return {
      ...state,
      fileFolderToUpload: state.fileFolderToUpload.map((f) => {
        return {
          ...f,
          repositoryHierarchies: f.repositoryHierarchies.filter(
            (rep) => rep.id !== folderId
          ),
        }
      }),
    }
  }),
  on(UploadActions.removeQueuedMedia, (state, { queuedMediaId }) => {
    return {
      ...state,
      fileFolderToUpload: state.fileFolderToUpload.filter(
        (m) => m.id !== queuedMediaId
      ),
    }
  }),
  on(UploadActions.clearFileFolder, (state) => {
    return {
      ...state,
      fileFolderToUpload: [],
    }
  }),
  on(UploadActions.FetchCustodiansSuccess, (state, { custodiansSuccess }) => {
    return {
      ...state,
      custodians: custodiansSuccess.data.map((c) => c.custodianName),
    }
  }),
  on(UploadActions.addCustomCustodian, (state, { custodianName }) => {
    return {
      ...state,
      custodians: [...state.custodians, custodianName],
    }
  }),
  on(
    UploadActions.processFromRepositorySuccess,
    (state, { processSuccess }) => {
      return {
        ...state,
        processFromRepositorySuccess: processSuccess,
      }
    }
  ),
  on(UploadActions.processFromRepositoryFailure, (state, { processError }) => {
    return {
      ...state,
      processFromRepositoryError: processError,
    }
  }),
  on(
    UploadActions.storeSelectedFileMetaInfo,
    (state, { selectedFileMetaInfo }) => {
      const payload = Array.isArray(selectedFileMetaInfo)
        ? selectedFileMetaInfo
        : [selectedFileMetaInfo]
      return {
        ...state,
        selectedFileMetaInfo: (state.selectedFileMetaInfo || []).concat(
          payload
        ),
      }
    }
  ),
  on(UploadActions.removeSelectedFileMetaInfo, (state, { fileId }) => {
    const fileIds = Array.isArray(fileId) ? fileId : [fileId]
    return {
      ...state,
      selectedFileMetaInfo: (state.selectedFileMetaInfo || []).filter(
        (f) => !fileIds.includes(f.fileId)
      ),
    }
  }),
  on(
    UploadActions.updateSelectedFileMetaInfo,
    (state, { selectedFileMetaInfo }) => {
      return {
        ...state,
        selectedFileMetaInfo: (state.selectedFileMetaInfo || []).map((f) =>
          f.fileId === selectedFileMetaInfo.fileId ? selectedFileMetaInfo : f
        ),
      }
    }
  ),
  on(UploadActions.storeCustomCustodian, (state, { customCustodianName }) => {
    return {
      ...state,
      customCustodians: [
        ...(state.customCustodians || []),
        customCustodianName,
      ],
    }
  })
)

export function uploadReducer(
  state: UploadState | undefined,
  action: Action
): UploadState {
  return reducer(state, action)
}
