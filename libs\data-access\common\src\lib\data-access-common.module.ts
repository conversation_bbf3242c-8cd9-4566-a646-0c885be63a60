import { ProductionShareInvitationService } from './services/production-share-invitation.service'
import { NgModule } from '@angular/core'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import {
  CODING_FEATURE_KEY,
  codingReducer,
  PROJECT_FEATURE_KEY,
  projectReducer,
  DOCUMENT_TAGS_FEATURE_KEY,
  DOCUMENT_VIEW_FEATURE_KEY,
  REPROCESSING_FEATURE_KEY,
  tagsReducer,
  documentViewReducer,
  TagsEffects,
  ProjectEffects,
  CodingEffects,
  DocumentViewEffects,
  TagsFacade,
  ProjectFacade,
  CodingFacade,
  DocumentViewFacade,
  CommandsEffects,
  COMMANDS_FEATURE_KEY,
  commandsReducer,
  jobStatusReducer,
  JOB_STATUS_FEATURE_KEY,
  JobStatusFacade,
  JobStatusEffects,
  DeletedExportFacade,
  DELETED_EXPORT_FEATURE_KEY,
  deletedExportReducer,
  DeletedExportEffects,
  productionReducer,
  PRODUCTION_FEATURE_KEY,
  ProductionEffects,
  ProductionFacade,
  reprocessingReducer,
  ReprocessingEffects,
  ReprocessingFacade,
  REVIEWSET_FEATURE_KEY,
  reviewSetReducer,
  ReviewSetEffects,
  UPLOAD_FEATURE_KEY,
  uploadReducer,
  UploadEffects,
} from './+state'
import {
  TagsService,
  ProjectService,
  CodingService,
  DocumentViewService,
  DeletedExportService,
} from './services'
import { LegalHoldReportService } from './services/legal-hold-report.service'
import { LegalHoldReportEffects } from './+state/legal-hold-report/legal-hold-report.effects'
import {
  LEGAL_HOLD_REPORT_FEATURE_KEY,
  legalHoldReportReducer,
} from './+state/legal-hold-report/legal-hold-report.reducer'
import { DirectExportService } from './services/direct-export.service'
import { DirectExportEffects } from './+state/direct-export/direct-export.effects'
import {
  DIRECT_EXPORT_FEATURE_KEY,
  directExportReducer,
} from './+state/direct-export/direct-export.reducer'
import { DirectExportFacade } from './+state/direct-export/direct-export.facade'
import { ProductionService } from './services/production.service'
import { ReprocessingService } from './services/reprocessing/reprocessing.service'
import { MaintenanceService } from './services/maintenance.service'

@NgModule({
  imports: [
    StoreModule.forFeature(DOCUMENT_TAGS_FEATURE_KEY, tagsReducer),
    StoreModule.forFeature(PROJECT_FEATURE_KEY, projectReducer),
    StoreModule.forFeature(CODING_FEATURE_KEY, codingReducer),
    StoreModule.forFeature(DOCUMENT_VIEW_FEATURE_KEY, documentViewReducer),
    StoreModule.forFeature(COMMANDS_FEATURE_KEY, commandsReducer),
    StoreModule.forFeature(JOB_STATUS_FEATURE_KEY, jobStatusReducer),
    StoreModule.forFeature(DELETED_EXPORT_FEATURE_KEY, deletedExportReducer),
    StoreModule.forFeature(
      LEGAL_HOLD_REPORT_FEATURE_KEY,
      legalHoldReportReducer
    ),
    StoreModule.forFeature(DIRECT_EXPORT_FEATURE_KEY, directExportReducer),
    StoreModule.forFeature(PRODUCTION_FEATURE_KEY, productionReducer),
    StoreModule.forFeature(REPROCESSING_FEATURE_KEY, reprocessingReducer),
    StoreModule.forFeature(REVIEWSET_FEATURE_KEY, reviewSetReducer),
    StoreModule.forFeature(UPLOAD_FEATURE_KEY, uploadReducer),
    EffectsModule.forFeature([
      TagsEffects,
      ProjectEffects,
      CodingEffects,
      DocumentViewEffects,
      CommandsEffects,
      JobStatusEffects,
      DeletedExportEffects,
      LegalHoldReportEffects,
      DirectExportEffects,
      ProductionEffects,
      ReprocessingEffects,
      ReviewSetEffects,
      UploadEffects,
    ]),
  ],
  providers: [
    TagsFacade,
    TagsService,
    ProjectFacade,
    ProjectService,
    CodingService,
    CodingFacade,
    DocumentViewService,
    DocumentViewFacade,
    JobStatusFacade,
    DeletedExportFacade,
    DeletedExportService,
    LegalHoldReportService,
    DirectExportService,
    DirectExportFacade,
    ProductionFacade,
    ProductionShareInvitationService,
    ProductionService,
    ReprocessingFacade,
    ReprocessingService,
    MaintenanceService,
  ],
})
export class DataAccessCommonModule {}
