import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  CellTemplateDirective,
  ColumnComponent,
  GridComponent,
} from '@progress/kendo-angular-grid'
import { DialogTitleBarComponent } from '@progress/kendo-angular-dialog'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { UploadFacade } from '@venio/data-access/common'
import { UploadSourceTypes } from '@venio/shared/models/interfaces'
import { toSignal } from '@angular/core/rxjs-interop'
import { filter } from 'rxjs'

@Component({
  selector: 'venio-upload-supported-file-type-info',
  standalone: true,
  imports: [
    CommonModule,
    CellTemplateDirective,
    ColumnComponent,
    DialogTitleBarComponent,
    GridComponent,
    SvgLoaderDirective,
  ],
  templateUrl: './upload-supported-file-type-info.component.html',
  styleUrl: './upload-supported-file-type-info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadSupportedFileTypeInfoComponent {
  private readonly uploadFacade = inject(UploadFacade)

  private readonly selectUploadSourceSelection = toSignal(
    this.uploadFacade.selectUploadSourceSelection$.pipe(
      filter((source) => Boolean(source))
    )
  )

  public readonly supportedTypeInfo = computed(() => {
    const { sourceType } = this.selectUploadSourceSelection()
    switch (sourceType) {
      case UploadSourceTypes.TRANSCRIPT:
        return this.transcriptTypes
      default:
        return this.fileTypesInfo
    }
  })

  private readonly fileTypesInfo = [
    {
      fileType: '7z Archive File',
      extension: '.7z',
    },
    {
      fileType: '.ZIP File',
      extension: '.zip',
    },
    {
      fileType: '.RAR File',
      extension: '.rar',
    },
    {
      fileType: 'UNIX Tar',
      extension: '.tar',
    },
    {
      fileType: 'Lotus Notes Database R6.x',
      extension: '.NS2',
    },
    {
      fileType: 'Microsoft Cabinet File',
      extension: '.cab',
    },
    {
      fileType: 'LZH Compress',
      extension: '.lzh',
    },
    {
      fileType: 'Self-Extracting LZH',
      extension: '.lzh',
    },
    {
      fileType: 'UNIX GZip',
      extension: '.gz',
    },
    {
      fileType: 'mbox(RFC-822 mailbox)',
      extension: '.mbox',
    },
    {
      fileType: 'MS Office Binder',
      extension: '.OBD',
    },
    {
      fileType: 'Outlook Express File Type',
      extension: '.dbx',
    },
    {
      fileType: 'Mail Archive DXL',
      extension: '.dxl',
    },
    {
      fileType: 'Microsoft Office 365 OST file',
      extension: '.ost',
    },
    {
      fileType: 'Microsoft Outlook PST/OST 2003',
      extension: '.pst',
    },
    {
      fileType: 'Microsoft Outlook PST/OST 97/2000/XP',
      extension: '.pst',
    },
    {
      fileType: 'Microsoft Outlook file for Mac',
      extension: '.olm',
    },
    {
      fileType: 'Lotus Notes Database File',
      extension: '.nsf',
    },
    {
      fileType: 'UNIX Compress',
      extension: '.gz',
    },
    {
      fileType: 'Forensic Image',
      extension: '.e01, .l01, .ad1, .vhd',
    },
  ]

  private readonly transcriptTypes = [
    {
      fileType: 'Transcript File',
      extension: '.ptf, .pcf',
    },
  ]
}
