import {
  StatusCode,
  CustodianListData,
  MediaStatusData,
} from './upload-status.service'

/**
 * Interface for the mock data factory return type
 */
export interface MockDataFactory {
  getDefaultCustodianListData(): CustodianListData
  getDefaultMediaStatusData(): MediaStatusData
  getUnprocessedFilesData(): MediaStatusData
  getEdgeCaseData(): CustodianListData
  getMixedStatusData(): MediaStatusData
  getMixedTasksData(): CustodianListData
  getDateParsingTestData(): MediaStatusData
  getStatusMappingTestData(): CustodianListData
  getCancellationTestData(): CustodianListData
  getCustomPollingIntervalTestData(): MediaStatusData
  getErrorHandlingTestData(): MediaStatusData
  createCustomCustodianListData(
    customData: Partial<CustodianListData>
  ): CustodianListData
  createCustomMediaStatusData(
    customData: Partial<MediaStatusData>
  ): MediaStatusData
}

/**
 * Creates mock data for testing the UploadStatusService
 * @returns {MockDataFactory} A factory object with methods to get different mock data sets
 */
export const createMockData = (): MockDataFactory => {
  // Base mock data
  const defaultCustodianListData: CustodianListData = {
    ignoreTiffJobsForMediaStatus: false,
    custodianList: [
      {
        name: 'Test Custodian',
        fileCount: 2,
        mediaList: [
          {
            typeCount: null,
            jobList: [
              {
                taskName: 'Ingestion',
                totalCount: '10',
                completedCount: '5',
                timeTaken: '00:05:00',
                percentage: '50',
                status: 'INPROGRESS',
                postProcessingStatus: '',
                projectJobGroupId: 123,
              },
              {
                taskName: 'Indexing',
                totalCount: '10',
                completedCount: '0',
                timeTaken: '00:00:00',
                percentage: '0',
                status: 'NOT STARTED',
                postProcessingStatus: '',
                projectJobGroupId: 124,
              },
            ],
            currentlyInProgressJob: null,
            mediaId: 1,
            status: 'INPROGRESS',
            mediaName: 'Test Media 1',
            docCount: 10,
            repeat: 0,
            edocs: 0,
            system: 0,
            duplicate: 0,
            archive: 0,
            uploadFileId: 'file-123',
            uploadStartedDate: '2023-05-15T10:00:00',
            uploadedBy: 'Test User',
            scannedCount: null,
            etaIngestion: null,
            completedPercentage: null,
            isRepository: false,
            isMSTeam: false,
            queuedOn: null,
          },
          {
            typeCount: null,
            jobList: [
              {
                taskName: 'Ingestion',
                totalCount: '5',
                completedCount: '5',
                timeTaken: '00:03:00',
                percentage: '100',
                status: 'COMPLETED',
                postProcessingStatus: '',
                projectJobGroupId: 125,
              },
              {
                taskName: 'Indexing',
                totalCount: '5',
                completedCount: '5',
                timeTaken: '00:02:00',
                percentage: '100',
                status: 'COMPLETED',
                postProcessingStatus: '',
                projectJobGroupId: 126,
              },
            ],
            currentlyInProgressJob: null,
            mediaId: 2,
            status: 'COMPLETED',
            mediaName: 'Test Media 2',
            docCount: 5,
            repeat: 0,
            edocs: 0,
            system: 0,
            duplicate: 0,
            archive: 0,
            uploadFileId: 'file-456',
            uploadStartedDate: '2023-05-14T09:00:00',
            uploadedBy: 'Test User',
            scannedCount: null,
            etaIngestion: null,
            completedPercentage: null,
            isRepository: false,
            isMSTeam: false,
            queuedOn: null,
          },
        ],
      },
    ],
  }

  const defaultMediaStatusData: MediaStatusData = {
    listMediaInfo: [
      {
        mediaId: 1,
        mediaName: 'Test Media 1',
        fileName: 'test-file-1.txt',
        mediaStatus: StatusCode.IN_PROGRESS,
        createdDate: '2023-05-15T10:00:00',
        uploadedBy: 'Test User',
      },
      {
        mediaId: 2,
        mediaName: 'Test Media 2',
        fileName: 'test-file-2.txt',
        mediaStatus: StatusCode.COMPLETED,
        createdDate: '2023-05-14T09:00:00',
        uploadedBy: 'Test User',
      },
      {
        mediaId: 3,
        mediaName: 'Test Media 3',
        fileName: 'test-file-3.txt',
        mediaStatus: StatusCode.NOT_PROCESSED,
        createdDate: '2023-05-13T08:00:00',
        uploadedBy: 'Test User',
      },
    ],
    listUploadInfo: [
      {
        fileName: 'test-file-1.txt',
        name: 'Test File 1',
        uploadStatus: 1, // In progress
        mediaName: 'Test Media 1',
        uploadId: 1,
        custodianName: 'Test Custodian',
        fileId: 'file-123',
        fileSize: 1024,
        isStructured: false,
        mediaSourceType: 1,
        password: null,
        nsfUserIdFileName: null,
        nsfUserIdFile: null,
      },
      {
        fileName: 'test-file-2.txt',
        name: 'Test File 2',
        uploadStatus: 2, // Completed
        mediaName: 'Test Media 2',
        uploadId: 2,
        custodianName: 'Test Custodian',
        fileId: 'file-456',
        fileSize: 2048,
        isStructured: false,
        mediaSourceType: 1,
        password: null,
        nsfUserIdFileName: null,
        nsfUserIdFile: null,
      },
      {
        fileName: 'test-file-3.txt',
        name: 'Test File 3',
        uploadStatus: 0, // Not processed
        mediaName: 'Test Media 3',
        uploadId: 3,
        custodianName: 'Test Custodian',
        fileId: 'file-789',
        fileSize: 3072,
        isStructured: false,
        mediaSourceType: 1,
        password: null,
        nsfUserIdFileName: null,
        nsfUserIdFile: null,
      },
    ],
    currentJobStatus: true,
  }

  // Factory methods for specific test scenarios
  return {
    // Return default data
    getDefaultCustodianListData: (): CustodianListData =>
      JSON.parse(JSON.stringify(defaultCustodianListData)),
    getDefaultMediaStatusData: (): MediaStatusData =>
      JSON.parse(JSON.stringify(defaultMediaStatusData)),

    // Create data for specific test scenarios
    getUnprocessedFilesData: (): MediaStatusData => ({
      ...JSON.parse(JSON.stringify(defaultMediaStatusData)),
      listUploadInfo: defaultMediaStatusData.listUploadInfo.map((item) => ({
        ...item,
        uploadStatus: 0, // All files not processed
      })),
    }),

    getEdgeCaseData: (): CustodianListData => ({
      ignoreTiffJobsForMediaStatus: false,
      custodianList: [
        {
          name: 'Test Custodian',
          fileCount: 2,
          mediaList: [
            {
              // Media with no job list
              typeCount: null,
              jobList: [],
              currentlyInProgressJob: null,
              mediaId: 1,
              status: 'COMPLETED',
              mediaName: 'No Jobs Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-1',
              uploadStartedDate: '2023-05-15T10:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              // Media with invalid status
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '5',
                  timeTaken: '00:03:00',
                  percentage: '50',
                  status: 'INVALID_STATUS',
                  postProcessingStatus: '',
                  projectJobGroupId: 124,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 2,
              status: 'INVALID_STATUS',
              mediaName: 'Invalid Status Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-2',
              uploadStartedDate: '2023-05-14T09:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
          ],
        },
      ],
    }),

    getMixedStatusData: (): MediaStatusData => ({
      ...JSON.parse(JSON.stringify(defaultMediaStatusData)),
      listMediaInfo: [
        {
          mediaId: 1,
          mediaName: 'Completed Media',
          fileName: 'completed.txt',
          mediaStatus: StatusCode.COMPLETED,
          createdDate: '2023-05-15T10:00:00',
          uploadedBy: 'Test User',
        },
        {
          mediaId: 2,
          mediaName: 'In Progress Media',
          fileName: 'inprogress.txt',
          mediaStatus: StatusCode.IN_PROGRESS,
          createdDate: '2023-05-14T09:00:00',
          uploadedBy: 'Test User',
        },
        {
          mediaId: 3,
          mediaName: 'Not Processed Media',
          fileName: 'notprocessed.txt',
          mediaStatus: StatusCode.NOT_PROCESSED,
          createdDate: '2023-05-13T08:00:00',
          uploadedBy: 'Test User',
        },
        {
          mediaId: 4,
          mediaName: 'Failed Media',
          fileName: 'failed.txt',
          mediaStatus: '3', // Using string directly since FAILED is not in StatusCode enum
          createdDate: '2023-05-12T07:00:00',
          uploadedBy: 'Test User',
        },
      ],
      listUploadInfo: [
        {
          fileName: 'completed.txt',
          name: 'Completed File',
          uploadStatus: 2, // Completed
          mediaName: 'Completed Media',
          uploadId: 1,
          custodianName: 'Test Custodian',
          fileId: 'file-1',
          fileSize: 1024,
          isStructured: false,
          mediaSourceType: 1,
          password: null,
          nsfUserIdFileName: null,
          nsfUserIdFile: null,
        },
        {
          fileName: 'inprogress.txt',
          name: 'In Progress File',
          uploadStatus: 1, // In progress
          mediaName: 'In Progress Media',
          uploadId: 2,
          custodianName: 'Test Custodian',
          fileId: 'file-2',
          fileSize: 2048,
          isStructured: false,
          mediaSourceType: 1,
          password: null,
          nsfUserIdFileName: null,
          nsfUserIdFile: null,
        },
        {
          fileName: 'notprocessed.txt',
          name: 'Not Processed File',
          uploadStatus: 0, // Not processed
          mediaName: 'Not Processed Media',
          uploadId: 3,
          custodianName: 'Test Custodian',
          fileId: 'file-3',
          fileSize: 3072,
          isStructured: false,
          mediaSourceType: 1,
          password: null,
          nsfUserIdFileName: null,
          nsfUserIdFile: null,
        },
        {
          fileName: 'failed.txt',
          name: 'Failed File',
          uploadStatus: 3, // Failed
          mediaName: 'Failed Media',
          uploadId: 4,
          custodianName: 'Test Custodian',
          fileId: 'file-4',
          fileSize: 4096,
          isStructured: false,
          mediaSourceType: 1,
          password: null,
          nsfUserIdFileName: null,
          nsfUserIdFile: null,
        },
      ],
    }),

    // Data for testing date parsing
    getDateParsingTestData: (): MediaStatusData => ({
      ...JSON.parse(JSON.stringify(defaultMediaStatusData)),
      listMediaInfo: [
        {
          mediaId: 1,
          mediaName: 'Valid Date Media',
          fileName: 'valid-date.txt',
          mediaStatus: StatusCode.COMPLETED,
          createdDate: '2023-05-15T10:00:00', // Valid date
          uploadedBy: 'Test User',
        },
        {
          mediaId: 2,
          mediaName: 'Invalid Date Media',
          fileName: 'invalid-date.txt',
          mediaStatus: StatusCode.COMPLETED,
          createdDate: '0001-01-01T00:00:00', // Invalid date
          uploadedBy: 'Test User',
        },
        {
          mediaId: 3,
          mediaName: 'Null Date Media',
          fileName: 'null-date.txt',
          mediaStatus: StatusCode.COMPLETED,
          createdDate: null as unknown as string, // Null date
          uploadedBy: 'Test User',
        },
      ],
    }),

    // Data for testing status mapping
    getStatusMappingTestData: (): CustodianListData => ({
      ignoreTiffJobsForMediaStatus: false,
      custodianList: [
        {
          name: 'Test Custodian',
          fileCount: 5,
          mediaList: [
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '10',
                  timeTaken: '00:05:00',
                  percentage: '100',
                  status: 'COMPLETED', // Exact match
                  postProcessingStatus: '',
                  projectJobGroupId: 123,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 1,
              status: 'COMPLETED',
              mediaName: 'Completed Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-1',
              uploadStartedDate: '2023-05-15T10:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '5',
                  timeTaken: '00:03:00',
                  percentage: '50',
                  status: 'IN PROGRESS', // With space
                  postProcessingStatus: '',
                  projectJobGroupId: 124,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 2,
              status: 'INPROGRESS',
              mediaName: 'In Progress Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-2',
              uploadStartedDate: '2023-05-14T09:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '0',
                  timeTaken: '00:00:00',
                  percentage: '0',
                  status: 'NOT STARTED', // Exact match
                  postProcessingStatus: '',
                  projectJobGroupId: 125,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 3,
              status: 'NOT STARTED',
              mediaName: 'Not Started Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-3',
              uploadStartedDate: '2023-05-13T08:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '0',
                  timeTaken: '00:00:00',
                  percentage: '0',
                  status: 'NOT PROCESSED', // Alternative text
                  postProcessingStatus: '',
                  projectJobGroupId: 126,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 4,
              status: 'NOT PROCESSED',
              mediaName: 'Not Processed Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-4',
              uploadStartedDate: '2023-05-12T07:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '0',
                  timeTaken: '00:00:00',
                  percentage: '0',
                  status: '', // Empty string
                  postProcessingStatus: '',
                  projectJobGroupId: 127,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 5,
              status: '',
              mediaName: 'Empty Status Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-5',
              uploadStartedDate: '2023-05-11T06:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
          ],
        },
      ],
    }),

    // Data for testing cancellation functionality
    getCancellationTestData: (): CustodianListData => ({
      ignoreTiffJobsForMediaStatus: false,
      custodianList: [
        {
          name: 'Test Custodian',
          fileCount: 3,
          mediaList: [
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '5',
                  timeTaken: '00:03:00',
                  percentage: '50',
                  status: 'INPROGRESS',
                  postProcessingStatus: '',
                  projectJobGroupId: 123,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 1,
              status: 'INPROGRESS',
              mediaName: 'In Progress Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-1',
              uploadStartedDate: '2023-05-15T10:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '10',
                  timeTaken: '00:05:00',
                  percentage: '100',
                  status: 'COMPLETED',
                  postProcessingStatus: '',
                  projectJobGroupId: 124,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 2,
              status: 'COMPLETED',
              mediaName: 'Completed Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-2',
              uploadStartedDate: '2023-05-14T09:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '0',
                  timeTaken: '00:00:00',
                  percentage: '0',
                  status: 'NOT STARTED',
                  postProcessingStatus: '',
                  projectJobGroupId: 125,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 3,
              status: 'NOT STARTED',
              mediaName: 'Not Started Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-3',
              uploadStartedDate: '2023-05-13T08:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
          ],
        },
      ],
    }),

    // Data for testing polling interval configuration
    getCustomPollingIntervalTestData: (): MediaStatusData => ({
      ...JSON.parse(JSON.stringify(defaultMediaStatusData)),
      currentJobStatus: true,
    }),

    // Data for testing error handling edge cases
    getErrorHandlingTestData: (): MediaStatusData => ({
      listMediaInfo: [
        {
          mediaId: 1,
          mediaName: 'Invalid Media',
          fileName: 'invalid.txt',
          mediaStatus: 'INVALID_STATUS' as unknown as StatusCode, // Invalid status
          createdDate: '2023-05-15T10:00:00',
          uploadedBy: 'Test User',
        },
      ],
      listUploadInfo: [
        {
          fileName: 'invalid.txt',
          name: 'Invalid File',
          uploadStatus: 999, // Invalid status
          mediaName: 'Invalid Media',
          uploadId: 1,
          custodianName: 'Test Custodian',
          fileId: 'file-1',
          fileSize: 1024,
          isStructured: false,
          mediaSourceType: 1,
          password: null,
          nsfUserIdFileName: null,
          nsfUserIdFile: null,
        },
      ],
      currentJobStatus: true,
    }),

    getMixedTasksData: (): CustodianListData => ({
      ignoreTiffJobsForMediaStatus: false,
      custodianList: [
        {
          name: 'Test Custodian',
          fileCount: 4,
          mediaList: [
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '10',
                  timeTaken: '00:05:00',
                  percentage: '100',
                  status: 'COMPLETED',
                  postProcessingStatus: '',
                  projectJobGroupId: 123,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 1,
              status: 'COMPLETED',
              mediaName: 'Completed Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-1',
              uploadStartedDate: '2023-05-15T10:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '5',
                  timeTaken: '00:03:00',
                  percentage: '50',
                  status: 'INPROGRESS',
                  postProcessingStatus: '',
                  projectJobGroupId: 124,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 2,
              status: 'INPROGRESS',
              mediaName: 'In Progress Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-2',
              uploadStartedDate: '2023-05-14T09:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '0',
                  timeTaken: '00:00:00',
                  percentage: '0',
                  status: 'NOT STARTED',
                  postProcessingStatus: '',
                  projectJobGroupId: 125,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 3,
              status: 'NOT STARTED',
              mediaName: 'Not Started Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-3',
              uploadStartedDate: '2023-05-13T08:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
            {
              typeCount: null,
              jobList: [
                {
                  taskName: 'Ingestion',
                  totalCount: '10',
                  completedCount: '10',
                  timeTaken: '00:05:00',
                  percentage: '100',
                  status: 'COMPLETED',
                  postProcessingStatus: '',
                  projectJobGroupId: 126,
                },
              ],
              currentlyInProgressJob: null,
              mediaId: 4,
              status: 'COMPLETED',
              mediaName: 'Another Completed Media',
              docCount: 10,
              repeat: 0,
              edocs: 0,
              system: 0,
              duplicate: 0,
              archive: 0,
              uploadFileId: 'file-4',
              uploadStartedDate: '2023-05-12T07:00:00',
              uploadedBy: 'Test User',
              scannedCount: null,
              etaIngestion: null,
              completedPercentage: null,
              isRepository: false,
              isMSTeam: false,
              queuedOn: null,
            },
          ],
        },
      ],
    }),

    // Helper to create custom data by merging with defaults
    createCustomCustodianListData: (
      customData: Partial<CustodianListData>
    ): CustodianListData => {
      return {
        ...JSON.parse(JSON.stringify(defaultCustodianListData)),
        ...customData,
      }
    },

    createCustomMediaStatusData: (
      customData: Partial<MediaStatusData>
    ): MediaStatusData => {
      return {
        ...JSON.parse(JSON.stringify(defaultMediaStatusData)),
        ...customData,
      }
    },
  }
}
