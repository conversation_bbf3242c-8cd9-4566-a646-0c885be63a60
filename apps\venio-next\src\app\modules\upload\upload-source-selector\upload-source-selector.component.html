<div
  class="t-flex t-flex-row t-flex-wrap t-gap-5 t-grow t-items-stretch t-justify-start t-px-0 sm:t-px-4 md:t-px-6 lg:t-px-20 xl:t-px-24 2xl:t-px-20">
  <!-- Unstructured Data Block -->
  <div
    class="t-flex t-flex-col t-grow t-basis-0 t-min-w-[280px] t-gap-3 t-self-stretch t-p-7 t-bg-[#F8F8F8] t-rounded-[4px] t-opacity-100 t-min-h-[10rem] t-items-center t-justify-between">
    <span
      (click)="selectSource(uploadSourceType.UNSTRUCTURED)"
      class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
      venioSvgLoader
      svgUrl="assets/svg/icon-unstructured.svg"
      height="10rem"
      width="9rem">
    </span>
    <p class="t-text-[#4A4B90] t-text-[12px] t-font-medium t-uppercase">
      Unstructured Data
    </p>
  </div>

  <!-- Structured Data Block -->
  <div
    class="t-flex t-flex-col t-grow t-basis-0 t-min-w-[280px] t-gap-3 t-p-7 t-bg-[#F8F8F8] t-rounded-[4px] t-opacity-100 t-min-h-[10rem] t-items-center t-justify-between">
    <span
      (click)="selectSource(uploadSourceType.STRUCTURED)"
      class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
      venioSvgLoader
      svgUrl="assets/svg/icon-structured.svg"
      height="10rem"
      width="9rem">
    </span>
    <p class="t-text-[#4A4B90] t-text-[12px] t-font-medium t-uppercase">
      Structured Data
    </p>
  </div>

  <!-- Social Media Block -->
  <div
    class="t-flex t-flex-col t-grow t-basis-0 t-min-w-[280px] t-gap-3 t-p-7 t-bg-[#F8F8F8] t-rounded-[4px] t-opacity-100 t-min-h-[10rem] t-items-center t-justify-between">
    <!-- Icon Grid Container -->
    <div class="t-grid t-grid-cols-3 t-gap-x-8 t-gap-y-4 t-w-full t-flex-1">
      <!-- Row 1 -->
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.FACEBOOK
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/icon-facebook.svg"
          height="2rem"
          width="2rem">
        </span>
      </div>
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.TWITTER
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/X-01.svg"
          height="2rem"
          width="2rem">
        </span>
      </div>
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.BLOOMBERG
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/bloomberg-svgrepo-com.svg"
          height="1.53rem"
          width="1.75rem">
        </span>
      </div>

      <!-- Row 2 -->
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.CELLEBRITE
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/<EMAIL>"
          height="2rem"
          width="2.3rem">
        </span>
      </div>
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.SLACK
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/slack_Technologies_Logo.svg"
          height="2rem"
          width="2rem">
        </span>
      </div>
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.MSTEAM
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/microsoft-logo-svgrepo-com.svg"
          height="2rem"
          width="2rem">
        </span>
      </div>

      <!-- Row 3 -->
      <div class="t-flex t-items-center t-justify-center">
        <span
          (click)="
            selectSource(
              uploadSourceType.SOCIAL_MEDIA,
              uploadSourceSocialMediaTypes.RSMF
            )
          "
          class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
          venioSvgLoader
          svgUrl="assets/svg/RSMF-01.svg"
          height="2.1rem"
          width="2.1rem">
        </span>
      </div>
      <!-- Empty cells for future icons -->
      <div class="t-flex t-items-center t-justify-center">
        <!-- Space for future icon -->
      </div>
      <div class="t-flex t-items-center t-justify-center">
        <!-- Space for future icon -->
      </div>
    </div>

    <!-- Caption -->
    <p
      class="t-text-[#4A4B90] t-text-[12px] t-font-medium t-uppercase t-tracking-wider t-mt-2">
      Social Media
    </p>
  </div>
  <!-- Transcript Block -->
  <div
    class="t-flex t-flex-col t-grow t-basis-0 t-min-w-[280px] t-gap-3 t-p-7 t-bg-[#F8F8F8] t-rounded-[4px] t-opacity-100 t-min-h-[10rem] t-items-center t-justify-between">
    <span
      (click)="selectSource(uploadSourceType.TRANSCRIPT)"
      class="t-cursor-pointer t-transition-all t-duration-200 hover:t-scale-105 hover:t-translate-y-[-2px] hover:t-opacity-90"
      venioSvgLoader
      svgUrl="assets/svg/live-transcribe.svg"
      height="10rem"
      width="9rem">
    </span>
    <p class="t-text-[#4A4B90] t-text-[12px] t-font-medium t-uppercase">
      Transcript
    </p>
  </div>
</div>
