import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  MediaSourceDropdownItem,
  UploadMediaTypes,
  UploadSourceSocialMediaTypes,
  UploadSourceTypes,
} from '@venio/shared/models/interfaces'
import { UploadFacade } from '@venio/data-access/common'

@Component({
  selector: 'venio-upload-source-selector',
  standalone: true,
  imports: [CommonModule, SvgLoaderDirective],
  templateUrl: './upload-source-selector.component.html',
  styleUrl: './upload-source-selector.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadSourceSelectorComponent {
  private readonly uploadFacade = inject(UploadFacade)

  public readonly uploadSourceType = UploadSourceTypes

  public readonly uploadSourceSocialMediaTypes = UploadSourceSocialMediaTypes

  public selectSource(
    sourceType: UploadSourceTypes,
    socialMediaType?: UploadSourceSocialMediaTypes
  ): void {
    const isUnstructured = sourceType === UploadSourceTypes.UNSTRUCTURED
    const items = this.#getSourceList(isUnstructured)
    this.uploadFacade.updateUploadSourceSelection({
      mediaSourceItems: items,
      sourceType,
      // Initially, it should route to the local computer upload
      // From there, the user then selects the source type as needed
      mediaSourceType: items[0],
      socialMediaType,
    })

    // The next step would be to file media source selection
    // e.g. local upload, repository, batch media, aws s3 etc.
    this.uploadFacade.updateActiveStepperIndex(1)
  }

  #getSourceList(isUnstructured: boolean): MediaSourceDropdownItem[] {
    const baseTypes: MediaSourceDropdownItem[] = [
      { value: UploadMediaTypes.LOCAL_UPLOAD, label: 'Local Computer' },
      { value: UploadMediaTypes.REPOSITORY, label: 'Repository' },
    ]
    return isUnstructured
      ? [
          baseTypes[0],
          { value: UploadMediaTypes.BATCH_MEDIA, label: 'Batch Media' },
          baseTypes[1],
          { value: UploadMediaTypes.AWS_S3, label: 'AWS S3 Data' },
        ]
      : baseTypes
  }
}
