import { RepositoryHierarchyModel } from '../reprocessing/reprocessing.model'
export const SUPPORTED_FILE_EXTENSIONS = [
  '.7z',
  '.zip',
  '.rar',
  '.tar',
  '.NS2',
  '.cab',
  '.lzh',
  '.lzh',
  '.gz',
  '.mbox',
  '.OBD',
  '.dbx',
  '.dxl',
  '.ost',
  '.pst',
  '.pst',
  '.olm',
  '.nsf',
  '.gz',
  '.e01',
  '.l01',
  '.ad1',
  '.vhd',
]

export const SUPPORTED_TRANSCRIPT_EXTENSION = ['.ptf', '.pcf']

/**
 * Enum representing the primary categories of data upload sources.
 */
export enum UploadSourceTypes {
  UNSTRUCTURED = 'UNSTRUCTURED',
  STRUCTURED = 'STRUCTURED',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
  TRANSCRIPT = 'TRANSCRIPT',
}

export enum UploadLocalStorageKeys {
  UploadRegisteredSessionId = 'UploadRegisteredSessionId',
}

/**
 * Enum representing specific social media platforms.
 * Only used when UploadSource.SOCIAL_MEDIA is selected.
 */
export enum UploadSourceSocialMediaTypes {
  FACEBOOK = 'FACEBOOK',
  SLACK = 'SLACK',
  CELLEBRITE = 'CELLEBRITE',
  BLOOMBERG = 'BLOOMBERG',
  TWITTER = 'TWITTER',
  MSTEAM = 'MSTEAM',
  RSMF = 'RSMF',
}

/**
 * Enum representing upload media source types.
 * All options are available for UNSTRUCTURED source type.
 * Only LOCAL_UPLOAD and REPOSITORY are available for other source types.
 */
export enum UploadMediaTypes {
  LOCAL_UPLOAD = 'LOCAL_UPLOAD',
  BATCH_MEDIA = 'BATCH_MEDIA',
  REPOSITORY = 'REPOSITORY',
  AWS_S3 = 'AWS_S3',
}

/**
 * Represents a complete upload source configuration selected by the user.
 *
 * @property {UploadSourceTypes} sourceType - The primary category of the upload source.
 *   This is always required and determines which other properties must be populated.
 *
 * @property {UploadMediaTypes} mediaSourceType - The specific upload method or location.
 *   When sourceType is UNSTRUCTURED, this can be any value from UploadMediaTypes.
 *   When sourceType is STRUCTURED, TRANSCRIPT, or SOCIAL_MEDIA, this must be either
 *   LOCAL_UPLOAD or REPOSITORY.
 *
 * @property {UploadSourceTypes[]} mediaSourceItems - The available media sources for the selected sourceType.
 * When sourceType is UNSTRUCTURED, this will be filled with all available source types.
 * When sourceType is STRUCTURED, TRANSCRIPT, or SOCIAL_MEDIA, this will be filled with
 * LOCAL_UPLOAD and REPOSITORY.
 *
 * @property {UploadSourceSocialMediaTypes} [socialMediaType] - The specific social media platform.
 *   Required when sourceType is SOCIAL_MEDIA.
 *   Must be undefined or null for all other sourceType values.
 */
export interface UploadSourceSelection {
  // The primary category of data upload source. E.g. UNSTRUCTURED, STRUCTURED, SOCIAL_MEDIA, TRANSCRIPT
  sourceType: UploadSourceTypes
  // The specific media source type. E.g. LOCAL_UPLOAD, BATCH_MEDIA, REPOSITORY, AWS_S3
  mediaSourceType: MediaSourceDropdownItem
  // When main source is selected, this will be filled with available media sources
  mediaSourceItems: MediaSourceDropdownItem[]
  // The specific social media platform. E.g. FACEBOOK, SLACK, CELLEBRITE, BLOOMBERG, TWITTER, MSTEAM, RSMF
  socialMediaType?: UploadSourceSocialMediaTypes | undefined
}

// Base interface for properties common to both media and upload items.
export interface BaseMedia {
  mediaName: string
  fileName: string
}

export interface MediaInfo extends BaseMedia {
  mediaId: number
  mediaStatus: string
}

export interface UploadInfo extends BaseMedia {
  // Optional properties that might be null in the source.
  name?: string | null
  custodianName?: string | null
  fileId?: number | null
  password?: string | null
  nsfUserIdFileName?: string | null
  nsfUserIdFile?: string | null

  uploadStatus: number
  uploadId: number
  fileSize: number
  isStructured: boolean
  mediaSourceType: number
}

export interface UploadStatuses {
  listMediaInfo: MediaInfo[]
  listUploadInfo: UploadInfo[]
  currentJobStatus: boolean
}

export interface QueuedRepositoryModel {
  id: string
  custodianName: string
  mediaName: string
  repositoryHierarchies: RepositoryHierarchyModel[]
  mediaSourceType: MediaSourceType
}

export interface AddedFilesList {
  fileId: number
  fileName: string
  fileSize: number
  fileSizeFormatted: string
  custodianName: string
  isStructured: boolean
  mediaSourceType?: MediaSourceType
  fullName: string
  extension: string
  name: string
  isForensicImageMultipart: string
  msTeamRelativePathDetails?: any[]
  msTeamRelativeId?: string
  mediaName: string
  password: string
}

export interface SelectedFileMetaInfo {
  fileId: string
  name: string
  custodianName: string
  mediaName: string
  password: string
  idFileName?: string
}

export interface NsfUserIdFile {
  fileId: string
  userIdFile: File
}

export interface FileFolderAdditionValidationData {
  isNewPathParent: boolean
  queuedData: QueuedRepositoryModel
  relativePath: string
}

export interface MSteamModel {
  accessToken: string
  loggedInUserId?: string
}

export interface MSTeamHierarchyModel {
  nodeName: string
  nodeId: string
  nodeType: string
  nodeParentId: string
  MSId: string
}

export const preValidationMessage = {
  generic: { required: 'Select file/folder to process' },
  custodianName: { required: 'Custodian name is required' },
  mediaName: {
    required: 'Media name is required',
  },
}

export enum MediaSourceType {
  SOURCE_FILE = 'SOURCE_FILE',
  SOURCE_FOLDER = 'SOURCE_FOLDER',
  AWSS3_FILE = 'AWSS3_FILE',
  AWSS3_FOLDER = 'AWSS3_FOLDER',
  IMAGE_DD = 'IMAGE_DD',
  IMAGE_ENCASE = 'IMAGE_ENCASE',
  IMAGE_ENCASE_LOGICAL = 'IMAGE_ENCASE_LOGICAL',
  IMAGE_001 = 'IMAGE_001',
  IMAGE_ISO = 'IMAGE_ISO',
  IMAGE_AD1 = 'IMAGE_AD1',
  SOCIAL_MEDIA_FACEBOOK = 'SOCIAL_MEDIA_FACEBOOK',
  SOCIAL_MEDIA_SLACK = 'SOCIAL_MEDIA_SLACK',
  SOCIAL_MEDIA_CELLEBRITE = 'SOCIAL_MEDIA_CELLEBRITE',
  SOCIAL_MEDIA_BLOOMBERG = 'SOCIAL_MEDIA_BLOOMBERG',
  SOCIAL_MEDIA_TWITTER = 'SOCIAL_MEDIA_TWITTER',
  SOCIAL_MEDIA_MSTEAM = 'SOCIAL_MEDIA_MSTEAM',
  RSMF = 'RSMF',
}

export interface MediaSourceDropdownItem {
  value: UploadMediaTypes
  label: string
}

export enum UploadState {
  Initializing = 'Initializing',
  Uploading = 'Uploading',
  Paused = 'Paused',
  Completed = 'Completed',
  Error = 'Error',
  Canceled = 'Canceled',
  Processing = 'Processing',
  Processed = 'Processed',
  ProcessingFailed = 'ProcessingFailed',
}

export enum ChunkStatus {
  Pending = 'pending',
  Processing = 'processing',
  Completed = 'completed',
  Failed = 'failed',
}

export interface ChunkMetadata {
  index: number
  start: number
  end: number
  attempts: number
  status: ChunkStatus
}

export interface FileMetadata {
  name: string
  size: number
  type: string
  lastModified: number
}

export interface UploadOptions {
  file: File
  chunkSize?: number
  concurrency?: number
  uploadEndpoint: string
  uploadRegisterEndpoint: string
  initializeEndpoint: string
  processEndpoint: string
  statusEndpoint?: string
  authToken?: string
  projectId?: number
  externalUserId?: number
  isTranscript?: boolean
  isStructured?: boolean
  fileId?: string
  isForensicImageMultipart?: boolean
  headers?: { [key: string]: string }
  custodianName?: string
  mediaName?: string
  password?: string
  mediaSourceType: MediaSourceType
  retryCount?: number
  retryDelay?: number
  maxRetries?: number
  maxRetryDelay?: number
  supportedMimeTypes?: string[]
}

export interface WorkerInitOptions {
  chunkSize?: number
  concurrency?: number
  fileId?: string
  retryCount?: number
  retryDelay?: number
  supportedMimeTypes?: string[]
}

export interface WorkerInitResult {
  valid: boolean
  message?: string
  chunks: number
  totalSize: number
  fileId: string
}

export interface ChunkUpdateResult {
  completed: boolean
  progress: number
  retriesExhausted: boolean
  retryAfter?: number
}

export interface WorkerStats {
  totalChunks: number
  completedChunks: number
  failedChunks: number
  pendingChunks: number
  processingChunks: number
  progress: number
}

export interface UploadProgress {
  taskId: string
  progress: number
  loaded: number
  total: number
  sessionId?: string
  status?: string
}

export interface FileUploadWorkerAPI {
  initializeUpload(
    fileMetadata: FileMetadata,
    options: WorkerInitOptions
  ): Promise<WorkerInitResult>

  getNextChunks(): Promise<ChunkMetadata[]>

  updateChunkStatus(index: number, success: boolean): Promise<ChunkUpdateResult>

  getStats(): Promise<WorkerStats>

  resetFailedChunks(): Promise<number>
}
