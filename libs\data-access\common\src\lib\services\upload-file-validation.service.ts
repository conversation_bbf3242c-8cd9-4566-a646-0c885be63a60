import {
  Injectable,
  signal,
  computed,
  WritableSignal,
  Signal,
} from '@angular/core'
import {
  UploadSourceTypes,
  UploadMediaTypes,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'
import { FileInfo } from '@progress/kendo-angular-upload'

/**
 * @enum FileTypeCategory
 * @description Categorizes files based on their primary type determination using extension and forensic sequence analysis.
 */
export enum FileTypeCategory {
  Transcript = 'Transcript',
  Forensic = 'Forensic', // Includes multi-part sequence members and initial forensic types
  Container = 'Container', // Standard archives like zip, rar etc. (non-forensic)
  LoadFile = 'LoadFile', // Structured load files like txt, csv
  Other = 'Other', // Files not matching the above categories
}

/**
 * @interface ValidationResult
 * @description Represents the outcome of validating a single file's metadata.
 * Contains metadata, validity flag, and determined file type category.
 * Error messages are handled via a unified signal from the service.
 */
export interface ValidationResult {
  /** The Kendo FileInfo object containing metadata used for validation. */
  readonly metadata: FileInfo
  /** A boolean flag indicating if the file passed all validation rules for the given context. */
  readonly isValid: boolean
  /** The determined category of the file (e.g., Transcript, Forensic, Container), considering batch context if available via `validateFiles`. */
  readonly category: FileTypeCategory
}

/**
 * @class FileValidationService
 * @description Provides centralized validation for file metadata (FileInfo) based on upload context.
 * Uses Angular Signals for reactive state. Encapsulates rules for extensions, optional strict MIME checks,
 * and multi-part forensic sequences. Determines file category accurately based on batch analysis.
 * Returns individual validity results and a single, unified error message if failures occur.
 * Avoids holding File references. Follows SoC, DRY principles for high quality and maintainability.
 *
 * @Injectable
 */
@Injectable()
export class FileValidationService {
  // --- Configuration Sets (Private & Readonly) ---

  /** @private @readonly @description Set of allowed uppercase extensions for non-forensic container file types. */
  readonly #containerExtensions: ReadonlySet<string> = new Set([
    '7Z',
    'ZIP',
    'RAR',
    'TAR',
    'NS2',
    'CAB',
    'LZH',
    'GZ',
    'MBOX',
    'OBD',
    'DBX',
    'DXL',
    'OST',
    'PST',
    'OLM',
    'NSF',
    'ZIPX',
  ])

  /** @private @readonly @description Set of allowed uppercase extensions for known forensic file types (single files or sequence starters). */
  readonly #forensicExtensions: ReadonlySet<string> = new Set([
    'E01',
    'L01',
    'AD1',
    'VHD',
    'ISO',
  ])

  /** @private @readonly @description Combined set for contexts allowing both container and forensic types. */
  readonly #containerAndForensicExtensions: ReadonlySet<string> = new Set([
    ...this.#containerExtensions,
    ...this.#forensicExtensions,
  ])

  /** @private @readonly @description Set of allowed uppercase extensions for structured load files. */
  readonly #structuredLoadFileExtensions: ReadonlySet<string> = new Set([
    'TXT',
    'CSV',
  ])

  /** @private @readonly @description Set of allowed uppercase extensions specifically for transcript files. */
  readonly #transcriptExtensions: ReadonlySet<string> = new Set(['PTF', 'PCF'])

  /** @private @readonly @description Set of allowed uppercase extensions marking the start of a multi-part forensic sequence. */
  readonly #initialForensicImageExtensions: ReadonlySet<string> = new Set([
    'L01',
    'E01',
  ])

  /** @private @readonly @description Map for optional strict MIME type to required extension validation. */
  readonly #strictMimeExtensionMap: ReadonlyMap<string, ReadonlySet<string>> =
    new Map([
      ['application/zip', new Set(['ZIP'])],
      ['application/vnd.rar', new Set(['RAR'])],
      ['application/x-7z-compressed', new Set(['7Z'])],
      ['application/x-tar', new Set(['TAR'])],
      ['application/gzip', new Set(['GZ'])],
      ['application/vnd.ms-outlook', new Set(['PST', 'OST'])],
      ['message/rfc822', new Set(['MBOX', 'EML'])],
      ['application/x-zip-compressed', new Set(['ZIP'])],
    ])

  /** @private @readonly @description Set of MIME types triggering fallback to extension-only validation. */
  readonly #fallbackMimeTypes: ReadonlySet<string> = new Set([
    '',
    'application/octet-stream',
  ])

  // --- Regular Expression Constants ---

  /** @private @readonly @description RegExp for forensic numeric extensions (e.g., E01). */
  readonly #forensicNumericExtRegex: RegExp = /^[ELS](\d{2})$/

  /** @private @readonly @description RegExp for forensic alphabetic extensions (e.g., EAA). */
  readonly #forensicAlphaExtRegex: RegExp = /^[ELS]([A-Z]{2,})$/

  /** @private @readonly @description RegExp for generic numeric extensions (e.g., 001). */
  readonly #genericNumericExtRegex: RegExp = /^\d+$/

  /** @private @readonly @description RegExp to capture forensic prefix (E, L, S). */
  readonly #forensicPrefixRegex: RegExp = /^([ELS])/

  // --- State Signals ---

  /** @private @description Internal signal holding individual validation results. */
  readonly #validationResults: WritableSignal<ValidationResult[]> = signal([])

  /** @private @description Internal signal holding the unified error message if failures exist. */
  readonly #unifiedErrorMessage: WritableSignal<string | undefined> =
    signal(undefined)

  /** @public @readonly @description Public signal for validation results (metadata, isValid, category). */
  public readonly validationResults: Signal<ValidationResult[]> =
    this.#validationResults.asReadonly()

  /** @public @readonly @description Public signal for the unified error message (undefined if all valid). */
  public readonly unifiedErrorMessage: Signal<string | undefined> =
    this.#unifiedErrorMessage.asReadonly()

  /** @public @readonly @description Computed signal filtering for invalid results. */
  public readonly invalidFiles: Signal<ValidationResult[]> = computed(() =>
    this.#validationResults().filter(
      (result: ValidationResult) => !result.isValid
    )
  )

  /** @public @readonly @description Computed signal indicating if any validation failures occurred. */
  public readonly hasInvalidFiles: Signal<boolean> = computed(
    () => this.invalidFiles().length > 0
  )

  // --- Public API Methods ---

  /**
   * @public
   * @description Validates a list of file metadata objects (FileInfo) based on the upload context.
   * Updates the service's reactive state signals. Determines validity and file category for each file,
   * accurately identifying forensic sequence parts within the batch. Provides a single unified error message if needed.
   * @param {FileInfo[]} filesMetadata - Array of Kendo FileInfo objects. Must not be null.
   * @param {UploadSourceSelection} selection - Upload source configuration. Must not be null.
   * @returns {void} Updates internal signals.
   */
  public validateFiles(
    filesMetadata: FileInfo[],
    selection: UploadSourceSelection
  ): void {
    if (!filesMetadata || filesMetadata.length === 0) {
      this.clearValidation()
      return
    }

    const fileNames: string[] = filesMetadata.map((meta: FileInfo) => meta.name)
    const validForensicParts: Set<string> =
      this.#getValidForensicParts(fileNames)
    const unifiedErrorMessageContext: string | undefined =
      this.#getUnifiedValidationMessage(selection)

    let hasAnyInvalid = false
    const results: ValidationResult[] = filesMetadata.map(
      (metadata: FileInfo): ValidationResult => {
        const upperCaseExtension = this.#getExtension(
          metadata.name
        )?.toUpperCase()
        const isForensicSequencePart = validForensicParts.has(
          metadata.name.toUpperCase()
        )

        // Determine category using batch context (isForensicSequencePart)
        const category = this.#determineFileTypeCategory(
          upperCaseExtension,
          isForensicSequencePart
        )
        // Determine validity using batch context (isForensicSequencePart)
        const isValid = this.#checkSingleFileValidity(
          metadata,
          selection,
          upperCaseExtension,
          isForensicSequencePart
        )

        if (!isValid) {
          hasAnyInvalid = true
        }
        return { metadata, isValid, category }
      }
    )

    this.#validationResults.set(results)
    this.#unifiedErrorMessage.set(
      hasAnyInvalid ? unifiedErrorMessageContext : undefined
    )
  }

  /**
   * @public
   * @description Determines the category of a file based primarily on its extension.
   * **Limitation:** This method operates on a single FileInfo object and lacks the context of other files in a potential batch.
   * Therefore, it cannot reliably identify files as part of a multi-part forensic *sequence* (e.g., it won't categorize '.E02' as Forensic).
   * It can only categorize based on known single-file forensic extensions (like '.E01', '.L01').
   * For sequence-aware categorization, use the `category` property within the `validationResults` signal *after* calling `validateFiles`.
   * @param {FileInfo} metadata - The Kendo FileInfo object (requires `metadata.name`).
   * @returns {FileTypeCategory} The determined category based *only* on the file's extension.
   */
  public getFileTypeCategory(metadata: FileInfo): FileTypeCategory {
    const upperCaseExtension = this.#getExtension(metadata.name)?.toUpperCase()
    // Pass 'false' for isForensicSequencePart as this method lacks batch context
    return this.#determineFileTypeCategory(upperCaseExtension, false)
  }

  /**
   * @public
   * @description Resets the internal validation state (results and error message).
   * @returns {void}
   */
  public clearValidation(): void {
    this.#validationResults.set([])
    this.#unifiedErrorMessage.set(undefined)
  }

  // --- Private Validation Logic Methods ---

  /**
   * @private
   * @description Checks the validity of a single file's metadata based on context rules, extension, optional MIME check, and forensic sequence status.
   * This is the core validity check logic used internally by `validateFiles`.
   * @param {FileInfo} metadata - The metadata for the individual file.
   * @param {UploadSourceSelection} selection - The overall upload context.
   * @param {(string | undefined)} upperCaseExtension - Pre-calculated uppercase extension.
   * @param {boolean} isForensicSequencePart - Pre-calculated flag indicating if this file is part of a valid sequence within the batch.
   * @returns {boolean} True if the file is valid according to all rules, false otherwise.
   */
  #checkSingleFileValidity(
    metadata: FileInfo,
    selection: UploadSourceSelection,
    upperCaseExtension: string | undefined,
    isForensicSequencePart: boolean
  ): boolean {
    const isForensicContext: boolean =
      selection.sourceType !== UploadSourceTypes.TRANSCRIPT
    // Priority 1: If it's a recognized part of a forensic sequence (and context allows), it's valid.
    if (isForensicSequencePart && isForensicContext) {
      return true
    }

    // Priority 2: Check if the extension itself is allowed for the current upload context.
    const isExtensionContextuallyAllowed: boolean = this.#isExtensionAllowed(
      upperCaseExtension,
      selection
    )
    if (!isExtensionContextuallyAllowed) {
      return false // Extension not allowed by context rules
    }

    // Priority 3: Optional Strict MIME Check (only perform if extension passed and MIME is not generic/unknown)
    const mimeType: string = metadata.rawFile?.type?.toLowerCase() || ''
    const requiresMimeFallback: boolean = this.#fallbackMimeTypes.has(mimeType)
    let isStrictCheckValid = true // Assume passes unless strict check is needed and fails

    if (!requiresMimeFallback && this.#strictMimeExtensionMap.has(mimeType)) {
      isStrictCheckValid = this.#validateStrictMimeExtension(
        mimeType,
        upperCaseExtension
      )
    }

    // Final Validity: Extension must be contextually allowed AND the strict MIME check (if performed) must pass.
    return isExtensionContextuallyAllowed && isStrictCheckValid
  }

  /**
   * @private
   * @description Determines the file category based on its extension and whether it's known to be part of a forensic sequence (from batch analysis).
   * @param {(string | undefined)} upperCaseExtension - The uppercase file extension.
   * @param {boolean} isForensicSequencePart - Flag indicating if the file is part of a detected forensic sequence (requires batch context).
   * @returns {FileTypeCategory} The determined file category.
   */
  #determineFileTypeCategory(
    upperCaseExtension: string | undefined,
    isForensicSequencePart: boolean
  ): FileTypeCategory {
    if (!upperCaseExtension) return FileTypeCategory.Other

    if (this.#transcriptExtensions.has(upperCaseExtension))
      return FileTypeCategory.Transcript
    if (this.#structuredLoadFileExtensions.has(upperCaseExtension))
      return FileTypeCategory.LoadFile

    // Forensic Check: Prioritize sequence membership flag, then check known single forensic extensions.
    if (isForensicSequencePart) return FileTypeCategory.Forensic
    if (this.#forensicExtensions.has(upperCaseExtension))
      return FileTypeCategory.Forensic
    // Check initial extensions too, as a single .E01 is forensic
    if (this.#initialForensicImageExtensions.has(upperCaseExtension))
      return FileTypeCategory.Forensic

    if (this.#containerExtensions.has(upperCaseExtension))
      return FileTypeCategory.Container

    return FileTypeCategory.Other
  }

  /**
   * @private
   * @description Performs strict validation: if MIME type is in the strict map, ensures the extension matches the allowed set.
   * @param {string} mimeType - The lowercase MIME type.
   * @param {(string | undefined)} upperCaseExtension - The file's uppercase extension.
   * @returns {boolean} True if the strict check passes or is not applicable; false if it fails.
   */
  #validateStrictMimeExtension(
    mimeType: string,
    upperCaseExtension: string | undefined
  ): boolean {
    if (!this.#strictMimeExtensionMap.has(mimeType)) return true
    if (!upperCaseExtension) return false
    const allowedExtensions: ReadonlySet<string> | undefined =
      this.#strictMimeExtensionMap.get(mimeType)
    return !!allowedExtensions?.has(upperCaseExtension)
  }

  /**
   * @private
   * @description Checks if a file extension is contextually allowed based *only* on source and media type rules.
   * Delegates to specific helpers based on source type.
   * @param {(string | undefined)} upperCaseExtension - Uppercase file extension or undefined.
   * @param {UploadSourceSelection} selection - The upload context.
   * @returns {boolean} True if allowed by extension rules for the context.
   */
  #isExtensionAllowed(
    upperCaseExtension: string | undefined,
    selection: UploadSourceSelection
  ): boolean {
    if (!upperCaseExtension) return false

    switch (selection.sourceType) {
      case UploadSourceTypes.TRANSCRIPT:
        return this.#transcriptExtensions.has(upperCaseExtension)
      case UploadSourceTypes.STRUCTURED:
        return this.#isAllowedForStructured(
          upperCaseExtension,
          selection.mediaSourceType.value
        )
      case UploadSourceTypes.SOCIAL_MEDIA:
        return this.#isAllowedForSocialMedia(
          upperCaseExtension,
          selection.mediaSourceType.value
        )
      case UploadSourceTypes.UNSTRUCTURED:
      default:
        return this.#isAllowedForUnstructured(
          upperCaseExtension,
          selection.mediaSourceType.value
        )
    }
  }

  /** @private @description Extension check helper for STRUCTURED source. */
  #isAllowedForStructured(
    upperCaseExtension: string,
    mediaType: UploadMediaTypes
  ): boolean {
    const isRepoOrCloud: boolean =
      mediaType === UploadMediaTypes.REPOSITORY ||
      mediaType === UploadMediaTypes.AWS_S3
    return isRepoOrCloud
      ? this.#structuredLoadFileExtensions.has(upperCaseExtension) ||
          this.#containerAndForensicExtensions.has(upperCaseExtension)
      : this.#containerAndForensicExtensions.has(upperCaseExtension)
  }

  /** @private @description Extension check helper for SOCIAL_MEDIA source (assuming file upload). */
  #isAllowedForSocialMedia(
    upperCaseExtension: string,
    mediaType: UploadMediaTypes
  ): boolean {
    const isFileBasedUpload: boolean =
      mediaType === UploadMediaTypes.REPOSITORY ||
      mediaType === UploadMediaTypes.AWS_S3 ||
      mediaType === UploadMediaTypes.LOCAL_UPLOAD ||
      mediaType === UploadMediaTypes.BATCH_MEDIA
    return (
      isFileBasedUpload &&
      this.#containerAndForensicExtensions.has(upperCaseExtension)
    )
  }

  /** @private @description Extension check helper for UNSTRUCTURED source. */
  #isAllowedForUnstructured(
    upperCaseExtension: string,
    mediaType: UploadMediaTypes
  ): boolean {
    const isRepoOrCloud: boolean =
      mediaType === UploadMediaTypes.REPOSITORY ||
      mediaType === UploadMediaTypes.AWS_S3
    return isRepoOrCloud
      ? this.#structuredLoadFileExtensions.has(upperCaseExtension) ||
          this.#containerAndForensicExtensions.has(upperCaseExtension)
      : this.#containerAndForensicExtensions.has(upperCaseExtension)
  }

  /**
   * @private
   * @description Generates a single, unified validation failure message listing allowed file types for the context.
   * @param {UploadSourceSelection} selection - The upload context.
   * @returns {string} A descriptive message listing allowed types.
   */
  #getUnifiedValidationMessage(selection: UploadSourceSelection): string {
    const mediaTypeDesc: string = selection.mediaSourceType.label.toLowerCase()
    let allowedTypesDescription: string

    switch (selection.sourceType) {
      case UploadSourceTypes.TRANSCRIPT:
        allowedTypesDescription = 'transcript files (.ptf, .pcf)'
        break
      case UploadSourceTypes.STRUCTURED:
        allowedTypesDescription = this.#getAllowedTypesDescriptionStructured(
          selection.mediaSourceType.value
        )
        break
      case UploadSourceTypes.SOCIAL_MEDIA:
        allowedTypesDescription = this.#getAllowedTypesDescriptionSocial(
          selection.mediaSourceType.value
        )
        break
      case UploadSourceTypes.UNSTRUCTURED:
      default:
        allowedTypesDescription = this.#getAllowedTypesDescriptionUnstructured(
          selection.mediaSourceType.value
        )
        break
    }
    return `Only ${allowedTypesDescription} are allowed for ${selection.sourceType.toLowerCase()} ${mediaTypeDesc}. Please remove unsupported files.`
  }

  /** @private @description Gets description string of allowed types for STRUCTURED context. */
  #getAllowedTypesDescriptionStructured(mediaType: UploadMediaTypes): string {
    const isRepoOrCloud: boolean =
      mediaType === UploadMediaTypes.REPOSITORY ||
      mediaType === UploadMediaTypes.AWS_S3
    // Combine descriptions for clarity
    const containerDesc = 'container/forensic files (e.g., .zip, .rar, .e01)'
    const loadFileDesc = 'load files (.txt, .csv)'
    return isRepoOrCloud ? `${loadFileDesc} or ${containerDesc}` : containerDesc
  }

  /** @private @description Gets description string of allowed types for SOCIAL_MEDIA file upload context. */
  #getAllowedTypesDescriptionSocial(mediaType: UploadMediaTypes): string {
    const isFileBasedUpload: boolean =
      mediaType === UploadMediaTypes.REPOSITORY ||
      mediaType === UploadMediaTypes.AWS_S3 ||
      mediaType === UploadMediaTypes.LOCAL_UPLOAD ||
      mediaType === UploadMediaTypes.BATCH_MEDIA
    return isFileBasedUpload
      ? 'container/forensic files (e.g., .zip, .rar, .e01)'
      : 'formats via direct connection'
  }

  /** @private @description Gets description string of allowed types for UNSTRUCTURED context. */
  #getAllowedTypesDescriptionUnstructured(mediaType: UploadMediaTypes): string {
    const isRepoOrCloud: boolean =
      mediaType === UploadMediaTypes.REPOSITORY ||
      mediaType === UploadMediaTypes.AWS_S3
    // Combine descriptions for clarity
    const containerDesc = 'container/forensic files (e.g., .zip, .rar, .e01)'
    const loadFileDesc = 'load files (.txt, .csv)'
    return isRepoOrCloud ? `${loadFileDesc} or ${containerDesc}` : containerDesc
  }

  /**
   * @private
   * @description Safely extracts the file extension from a filename string.
   * @param {string} filename - The filename.
   * @returns {(string | undefined)} The extension without the dot, or undefined.
   */
  #getExtension(filename: string): string | undefined {
    if (!filename) return undefined
    const lastDotIndex: number = filename.lastIndexOf('.')
    return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : undefined
  }

  // --- Forensic Multi-part Validation Helpers ---

  /**
   * @private
   * @description Identifies valid, contiguous forensic sequences within a list of filenames.
   * @param {string[]} fileNames - Array of filenames.
   * @returns {Set<string>} A Set of uppercase filenames belonging to valid sequences.
   */
  #getValidForensicParts(fileNames: string[]): Set<string> {
    const validList: Set<string> = new Set()
    if (!fileNames || fileNames.length < 2) return validList

    let currentSequenceBaseName: string | null = null
    let expectedNextExtension: string | null = null
    const orderedFilenames: string[] = this.#orderFilenames(fileNames)

    for (const fileName of orderedFilenames) {
      const upperCaseFileName: string = fileName.toUpperCase()
      const baseName: string = this.#getBaseName(fileName)
      const currentExtension: string | undefined = this.#getExtension(fileName)
      const upperCaseCurrentExtension: string | undefined =
        currentExtension?.toUpperCase()

      if (!currentExtension || !upperCaseCurrentExtension) {
        currentSequenceBaseName = null
        expectedNextExtension = null
        continue
      }

      if (this.#initialForensicImageExtensions.has(upperCaseCurrentExtension)) {
        validList.add(upperCaseFileName)
        currentSequenceBaseName = baseName
        expectedNextExtension =
          this.#incrementForensicImageExtension(currentExtension)
        continue
      }

      const isContinuing: boolean =
        baseName === currentSequenceBaseName &&
        expectedNextExtension !== null &&
        upperCaseCurrentExtension === expectedNextExtension.toUpperCase()

      if (isContinuing) {
        validList.add(upperCaseFileName)
        expectedNextExtension =
          this.#incrementForensicImageExtension(currentExtension)
        continue
      }

      currentSequenceBaseName = null
      expectedNextExtension = null
      if (this.#initialForensicImageExtensions.has(upperCaseCurrentExtension)) {
        validList.add(upperCaseFileName)
        currentSequenceBaseName = baseName
        expectedNextExtension =
          this.#incrementForensicImageExtension(currentExtension)
      }
    }
    return validList
  }

  /**
   * @private
   * @description Sorts filenames for forensic sequence checking (base name, then extension order value).
   * @param {string[]} filenames - Array of filenames.
   * @returns {string[]} A new, sorted array.
   */
  #orderFilenames(filenames: string[]): string[] {
    return [...filenames].sort((a: string, b: string): number => {
      const baseA: string = this.#getBaseName(a)
      const baseB: string = this.#getBaseName(b)
      if (baseA !== baseB) return baseA.localeCompare(baseB)
      const extA: string = this.#getExtension(a) || ''
      const extB: string = this.#getExtension(b) || ''
      return (
        this.#getExtensionOrderValue(extA) - this.#getExtensionOrderValue(extB)
      )
    })
  }

  /**
   * @private
   * @description Extracts the base name (excluding the last extension) from a filename.
   * @param {string} filename - The filename.
   * @returns {string} The base name.
   */
  #getBaseName(filename: string): string {
    if (!filename) return ''
    const lastDotIndex: number = filename.lastIndexOf('.')
    return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename
  }

  /**
   * @private
   * @description Assigns a numeric value for sorting forensic extensions correctly.
   * @param {string} ext - The file extension.
   * @returns {number} Sort order value.
   */
  #getExtensionOrderValue(ext: string): number {
    const upperExt: string = ext.toUpperCase()
    const numericMatch: RegExpMatchArray | null = upperExt.match(
      this.#forensicNumericExtRegex
    )
    if (numericMatch) return parseInt(numericMatch[1], 10)

    const alphaMatch: RegExpMatchArray | null = upperExt.match(
      this.#forensicAlphaExtRegex
    )
    if (alphaMatch) {
      let value = 1000
      const alphaPart: string = alphaMatch[1]
      for (let i = 0; i < alphaPart.length; i++) {
        value = value * 26 + (alphaPart.charCodeAt(i) - 'A'.charCodeAt(0))
      }
      return value
    }

    if (this.#genericNumericExtRegex.test(upperExt))
      return 500 + parseInt(upperExt, 10)

    return 99999
  }

  /**
   * @private
   * @description Checks if a filename has an initial forensic sequence extension.
   * @param {string} fileName - The filename.
   * @returns {boolean} True if it's an initial forensic extension.
   */
  #isInitialForensicFile(fileName: string): boolean {
    const extension: string | undefined = this.#getExtension(fileName)
    return (
      !!extension &&
      this.#initialForensicImageExtensions.has(extension.toUpperCase())
    )
  }

  /**
   * @private
   * @description Calculates the next expected extension in a forensic sequence.
   * Handles numeric (01-99) and alphabetic (AA-ZZ+) increments. Returns null if format unknown or limit reached.
   * @param {string} extension - Current forensic extension (e.g., "E01", "EAZ").
   * @returns {(string | null)} The next extension or null.
   */
  #incrementForensicImageExtension(extension: string): string | null {
    if (!extension) return null
    const upperExt: string = extension.toUpperCase()
    const prefixMatch: RegExpMatchArray | null = upperExt.match(
      this.#forensicPrefixRegex
    )
    if (!prefixMatch) return null

    const prefix: string = prefixMatch[1]
    const suffix: string = upperExt.substring(1)

    if (
      this.#forensicNumericExtRegex.test(upperExt) &&
      /^\d{2}$/.test(suffix)
    ) {
      const num: number = parseInt(suffix, 10)
      return num < 99
        ? prefix + (num + 1).toString().padStart(2, '0')
        : prefix + 'AA'
    }

    if (
      this.#forensicAlphaExtRegex.test(upperExt) &&
      /^[A-Z]{2,}$/.test(suffix)
    ) {
      let carry = true
      const chars: string[] = suffix.split('')
      for (let i: number = chars.length - 1; i >= 0; i--) {
        if (!carry) break
        if (chars[i] === 'Z') {
          chars[i] = 'A'
        } else {
          chars[i] = String.fromCharCode(chars[i].charCodeAt(0) + 1)
          carry = false
        }
      }
      return carry ? null : prefix + chars.join('')
    }

    return null
  }
}
