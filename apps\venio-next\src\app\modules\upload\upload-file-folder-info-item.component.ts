import {
  ChangeDetectionStrategy,
  Component,
  input,
  inject,
  OnInit,
  DestroyRef,
  ViewChildren,
  QueryList,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { KENDO_DROPDOWNS } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  preValidationMessage,
  QueuedRepositoryModel,
  RepositoryHierarchyModel,
} from '@venio/shared/models/interfaces'
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop'
import { UploadFacade } from '@venio/data-access/common'
import { debounceTime, distinctUntilChanged } from 'rxjs'
import {
  FileRestrictions,
  FileSelectComponent,
  FileSelectModule,
} from '@progress/kendo-angular-upload'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-upload-file-folder-info-item',
  standalone: true,
  imports: [
    CommonModule,
    KENDO_DROPDOWNS,
    InputsModule,
    ReactiveFormsModule,
    SvgLoaderDirective,
    FileSelectModule,
    KENDO_BUTTONS,
  ],
  templateUrl: './upload-file-folder-info-item.component.html',
  styleUrl: './upload-file-folder-info-item.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadFileFolderInfoItemComponent implements OnInit {
  public fb = inject(FormBuilder)

  private destroyedRef = inject(DestroyRef)

  private uploadFacade = inject(UploadFacade)

  @ViewChildren(FileSelectComponent)
  public fileSelects: QueryList<FileSelectComponent>

  public form: FormGroup

  public fileFolder = input<QueuedRepositoryModel>(undefined)

  public validationMessage = toSignal(this.uploadFacade.validationMessage$)

  public custodians = toSignal(this.uploadFacade.selectCustodians$, {
    initialValue: [],
  })

  private forensicImageFileFormat = 'ISO|E\\d+|L\\d+'

  public get repositories(): FormArray {
    return this.form.get('repositories') as FormArray
  }

  public nsfRestrictions: FileRestrictions = {
    allowedExtensions: ['.id'],
  }

  constructor() {
    this.initializeForm()
  }

  private initializeForm(): void {
    this.form = this.fb.group({
      custodianName: [
        this.fileFolder()?.custodianName ?? '',
        Validators.required,
      ],
      mediaName: [this.fileFolder()?.mediaName ?? '', Validators.required],
      repositories: this.fb.array([]),
    })
  }

  public ngOnInit(): void {
    this.initializeFormArray()
    this.handleFormValueChanges()
  }

  private initializeFormArray(): void {
    const repositoryHierarchies = this.fileFolder()?.repositoryHierarchies ?? []
    this.repositories.clear()

    repositoryHierarchies.forEach((repo) => {
      this.repositories.push(this.createRepositoryGroup(repo))
    })
  }

  private createRepositoryGroup(repo: RepositoryHierarchyModel): FormGroup {
    return this.fb.group({
      password: [repo.password ?? ''],
      nsfUserIdFile: [],
    })
  }

  private handleFormValueChanges(): void {
    this.form.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyedRef)
      )
      .subscribe((data) => {
        const updatedData: QueuedRepositoryModel = {
          ...this.fileFolder(),
          custodianName: data.custodianName,
          mediaName: data.mediaName,
          repositoryHierarchies: this.fileFolder().repositoryHierarchies.map(
            (r, index) => ({
              ...r,
              password: data.repositories[index]?.password,
              nsfUserIdFiles: data.repositories[index]?.nsfUserIdFile?.[0],
            })
          ),
        }

        //this.formControlValidation(data)

        this.uploadFacade.updateFileFolderForUpload(updatedData)
      })
  }

  //For now validation is not required.
  private formControlValidation(data: any): void {
    const currentValidationMessage =
      this.uploadFacade.validationMessage$.value || {}

    if (!currentValidationMessage[this.fileFolder().id]) {
      currentValidationMessage[this.fileFolder().id] = {}
    }

    if (!data.custodianName) {
      currentValidationMessage[this.fileFolder().id].custodianName =
        preValidationMessage.custodianName.required
    } else {
      delete currentValidationMessage[this.fileFolder().id].custodianName
    }

    if (!data.mediaName) {
      currentValidationMessage[this.fileFolder().id].mediaName =
        preValidationMessage.mediaName.required
    } else {
      delete currentValidationMessage[this.fileFolder().id].mediaName
    }
    this.uploadFacade.validationMessage$.next({
      ...currentValidationMessage,
    })
  }

  public onBrowseClick(index: number): void {
    if (this.fileSelects && this.fileSelects.length > index) {
      const button: HTMLButtonElement = this.fileSelects
        .toArray()
        [index].wrapper.querySelector('.k-upload-button')
      button.click()
    }
  }

  public deleteFileFolder(folderId: string): void {
    this.uploadFacade.removeFileFolder(folderId)
  }

  public checkIfAnyRepositoryFileisEncryptable(
    repo: RepositoryHierarchyModel
  ): boolean {
    const archiveFileExtensions = `zip|zipx|rar|7z|gz|lzh|obd|cab|tar|dbx|dxl|mbox|ns2|nsf|ost|pst|olm|ISO|E\\d+|L\\d+|${this.forensicImageFileFormat}`
    const documentFileExtensons = `pdf|doc|docx|dotm|dot|docm|dotx|xltx|xltm|xlsx|xlsm|xlsb|xls|xlam|pptx|pptm|ppt|ppsx|potx|ppsm|potm`

    const filePattern = new RegExp(
      `(${archiveFileExtensions}|${documentFileExtensons})$`,
      'i'
    )

    return filePattern.test(repo.name.split('.').pop())
  }

  public isLotusNotesFile(file: RepositoryHierarchyModel): boolean {
    return file.name.endsWith('.nsf')
  }
}
