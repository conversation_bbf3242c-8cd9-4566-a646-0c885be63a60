import {
  ChangeDetectionStrategy,
  Component,
  effect,
  HostListener,
  inject,
  Injector,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  untracked,
  viewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  StepperComponent,
  StepperStepTemplateDirective,
} from '@progress/kendo-angular-layout'
import {
  DialogService,
  WindowCloseActionDirective,
  WindowComponent,
  WindowTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { checkIcon, pencilIcon } from '@progress/kendo-svg-icons'
import { UploadSourceSelectorComponent } from './upload-source-selector/upload-source-selector.component'
import {
  UploadManagerService,
  UploadFacade,
  FileValidationService,
  UploadStatusService,
} from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { UploadFileBrowseToolbarComponent } from './upload-file-browse-toolbar/upload-file-browse-toolbar.component'
import { UploadFileBrowseContainerComponent } from './upload-file-browse-container/upload-file-browse-container.component'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { UploadFileProgressStatusesComponent } from './upload-file-progress-statuses/upload-file-progress-statuses.component'
import { filter, take } from 'rxjs'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { CaseDetailModel } from '@venio/shared/models/interfaces'
import { CommonActionTypes } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-upload-container',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    WindowCloseActionDirective,
    WindowComponent,
    WindowTitleBarComponent,
    SvgLoaderDirective,
    SVGIconComponent,
    StepperComponent,
    StepperStepTemplateDirective,
    UploadSourceSelectorComponent,
    UploadFileBrowseToolbarComponent,
    UploadFileBrowseContainerComponent,
    SkeletonComponent,
    UploadFileProgressStatusesComponent,
  ],
  templateUrl: './upload-container.component.html',
  styleUrl: './upload-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // Register component-level providers so they get destroyed when the component is destroyed
  // avoiding memory leaks due to root-level injections which becomes singletons and never get destroyed
  providers: [UploadManagerService, FileValidationService, UploadStatusService],
})
export class UploadContainerComponent implements OnInit, OnDestroy {
  public readonly uploadActionEvent =
    output<Partial<Record<CommonActionTypes, CaseDetailModel>>>()

  private readonly uploadFacade = inject(UploadFacade)

  private readonly uploadManager = inject(UploadManagerService)

  private readonly injector = inject(Injector)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly dialogService = inject(DialogService)

  private readonly windowRef = inject(WINDOW)

  private readonly confirmationMessage =
    'You have files selected or operations in progress. Closing this dialog will cancel these operations. Are you sure you want to exit?'

  private readonly selectedProjectInfo = toSignal(
    this.uploadFacade.selectSelectedCaseInfo$.pipe(
      filter((selectedCaseInfo) => Boolean(selectedCaseInfo))
    )
  )

  public readonly stepsComp = viewChild<StepperComponent>(StepperComponent)

  public readonly dialogClose = output<void>()

  public readonly steps = [
    { label: 'SOURCE SELECT' },
    { label: 'UPLOAD' },
    { label: 'STATUS' },
  ]

  public readonly pencilIcon = pencilIcon

  public readonly checkIcon = checkIcon

  public readonly currentStepIndex = toSignal(
    this.uploadFacade.selectStepperActiveIndex$,
    {
      // initially, the main step is active
      initialValue: 0,
    }
  )

  public ngOnInit(): void {
    this.#fetchSelectedProjectCustodians()
  }

  public handleUploadStatusAction(
    payload: Partial<Record<CommonActionTypes, CaseDetailModel>>
  ): void {
    this.uploadActionEvent.emit(payload)
  }

  /**
   * Closes the dialog, but only if there are no active uploads to confirm.
   * @param {Event} e - The event object.
   * @returns {void}
   */
  public closeDialog(e: Event): void {
    if (this.#hasActiveUploadsToConfirm()) {
      e.preventDefault()
      e.stopPropagation()
      this.#launchAndSetupConfirmationDialog()
    } else {
      this.dialogClose.emit()
    }
  }

  /**
   * Launches and sets up the confirmation dialog.
   * @returns {void}
   */
  #launchAndSetupConfirmationDialog(): void {
    const confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
      appendTo: this.viewContainerRef,
    })

    this.#setDialogInput(confirmationDialogRef.content.instance)

    const isConfirmed = toSignal(
      confirmationDialogRef.result.pipe(
        filter((action) => typeof action === 'boolean' && action),
        take(1)
      ),
      { injector: this.injector }
    )

    effect(
      () => {
        if (isConfirmed()) {
          untracked(() =>
            this.uploadManager
              .cancelAllUploads()
              .then(() => this.dialogClose.emit())
          )
        }
      },
      { injector: this.injector }
    )
  }

  /**
   * Sets the dialog input for the confirmation dialog.
   * @param {ConfirmationDialogComponent} instance - The instance of the confirmation dialog.
   * @returns {void}
   */
  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Exit Upload'
    instance.message = this.confirmationMessage
  }

  public ngOnDestroy(): void {
    this.#resetUploadState()
  }

  #fetchSelectedProjectCustodians(): void {
    effect(
      () => {
        const { projectId } = this.selectedProjectInfo()
        untracked(() => this.uploadFacade.fetchCustodians(projectId))
      },
      { injector: this.injector }
    )
  }

  #resetUploadState(): void {
    this.uploadFacade.resetUploadState([
      'uploadSourceSelection',
      'activeStepperIndex',
      'selectedCaseInfo',
      'processFromRepositorySuccess',
      'custodians',
      'processFromRepositoryError',
      'fileFolderToUpload',
      'customCustodians',
      'selectedFileMetaInfo',
    ])
  }

  /**
   * Checks if there are any active uploads that should prevent dialog closing
   * @returns {boolean} True if there are active uploads that should be confirmed before closing
   */
  #hasActiveUploadsToConfirm(): boolean {
    return this.uploadManager.hasActiveUploads()
  }

  /**
   * Handles the beforeunload event to show a confirmation dialog when closing the browser/tab
   * @param {BeforeUnloadEvent} event The beforeunload event
   * @returns {string | undefined} The confirmation message if there are active uploads to confirm, otherwise undefined
   */
  @HostListener('window:beforeunload', ['$event'])
  public handleBeforeUnload(event: BeforeUnloadEvent): string | undefined {
    event.stopImmediatePropagation()
    event.stopPropagation()

    if (this.#hasActiveUploadsToConfirm()) {
      // Standard way to show a confirmation dialog when closing the browser/tab
      event.preventDefault()
      event.returnValue = this.confirmationMessage
      return this.confirmationMessage
    }
    return undefined
  }
}
