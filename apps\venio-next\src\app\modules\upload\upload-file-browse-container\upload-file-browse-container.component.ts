import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { RepositoryUploadComponent } from '../repository-upload.component'
import { UploadFileBrowseDropZoneComponent } from '../upload-file-browse-drop-zone/upload-file-browse-drop-zone.component'
import { UploadFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { UploadMediaTypes } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-upload-file-browse-container',
  standalone: true,
  imports: [
    CommonModule,
    UploadFileBrowseDropZoneComponent,
    RepositoryUploadComponent,
  ],
  templateUrl: './upload-file-browse-container.component.html',
  styleUrl: './upload-file-browse-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadFileBrowseContainerComponent {
  private readonly uploadFacade = inject(UploadFacade)

  public readonly uploadMediaTypes = UploadMediaTypes

  private readonly sourceSelectionInfo = toSignal(
    this.uploadFacade.selectUploadSourceSelection$
  )

  public readonly selectedMediaTypeValue = computed<UploadMediaTypes>(
    () => this.sourceSelectionInfo()?.mediaSourceType?.value
  )
}
