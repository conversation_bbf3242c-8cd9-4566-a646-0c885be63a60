import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PDFHighlightGroupComponent } from './highlight-group.component'
import { provideMockStore } from '@ngrx/store/testing'
import {
  FieldFacade,
  PdfViewerFacade,
  ReasonModel,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { of } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { signal } from '@angular/core'

describe('TextSearchComponent', () => {
  let component: PDFHighlightGroupComponent
  let fixture: ComponentFixture<PDFHighlightGroupComponent>
  const mockPdfViewerFacade = {
    fetchRedactionTypes: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: [{ id: 1, redactionTypeName: 'Rectangle' }],
      } as ResponseModel)
    ),
    fetchRedactionReasons: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: [{ id: 1, reason: 'Confidential', isInUse: true }],
      } as ResponseModel)
    ),
    setRedactionReasons: jest.fn(),
    getRedactionReasons: jest
      .fn()
      .mockReturnValue(
        of([{ id: 1, reason: 'Confidential', isInUse: true }] as ReasonModel[])
      ),
    fetchProjectGroups: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: [{ groupId: 1, groupName: 'Group 1' }],
      } as ResponseModel)
    ),
    addRedactionSet: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        message: 'Redaction set created successfully',
      })
    ),
    lookupRedactionSet: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: false,
      } as ResponseModel)
    ),
    isAnnotationChanged: signal(false),
  }

  // Mock for SearchFacade
  const mockSearchFacade = {
    getSearchTempTables$: of({
      searchResultTempTable: 'temp_table_123',
    }),
  }

  // Mock for FieldFacade
  const mockFieldFacade = {}
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PDFHighlightGroupComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: PdfViewerFacade, useValue: mockPdfViewerFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: FieldFacade, useValue: mockFieldFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PDFHighlightGroupComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
