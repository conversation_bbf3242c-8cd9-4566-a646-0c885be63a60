import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import { UploadState, UPLOAD_FEATURE_KEY } from './upload.reducer'

export const memoizedSelector =
  createFeatureSelector<UploadState>(UPLOAD_FEATURE_KEY)

export const getStateFromUploadStore = <T extends keyof UploadState>(
  stateKey: T
): MemoizedSelector<object, UploadState[T], unknown> =>
  createSelector(memoizedSelector, (state: UploadState) => state[stateKey])
