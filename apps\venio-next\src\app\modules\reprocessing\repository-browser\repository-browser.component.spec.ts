import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { RepositoryBrowserComponent } from './repository-browser.component'
import {
  ReprocessingFacade,
  RepositoryBrowserStateService,
} from '@venio/data-access/common'
import { of } from 'rxjs'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { TreeItem } from '@progress/kendo-angular-treeview'
import {
  RepositoryHierarchyModel,
  RepositoryModel,
} from '@venio/shared/models/interfaces'
import { WritableSignal } from '@angular/core'

describe('RepositoryBrowserComponent', () => {
  let component: RepositoryBrowserComponent
  let fixture: ComponentFixture<RepositoryBrowserComponent>
  let mockReprocessingFacade: any
  let mockRepoStateService: any
  let mockFilteredRepositoryHierarchy: WritableSignal<any>

  beforeEach(async () => {
    mockReprocessingFacade = {
      fetchReposirotyList: jest.fn(),
      fetchRepositoryHeirarchy: jest.fn(),
      clearRepositoryHierarchy: jest.fn(),
      fetchSpecificRepositoryHeirarchy: jest
        .fn()
        .mockReturnValue(of({ data: [] })),
      getRepositoryList$: of([{ fsid: 1, fsDisplayName: 'Test Repo' }]),
      getRepositoryHierarchy$: of([
        { id: '1', parentId: '-1', type: 'FOLDER', name: 'Root Folder' },
      ]),
    }

    mockFilteredRepositoryHierarchy = jest
      .fn()
      .mockReturnValue([
        { id: '1_dummy', parentId: '1' },
      ]) as unknown as WritableSignal<any>

    mockFilteredRepositoryHierarchy.set = jest.fn()

    mockRepoStateService = {
      filteredRepositoryHierarchy: mockFilteredRepositoryHierarchy,
      createDummyChildNode: jest.fn().mockImplementation(
        (child) =>
          ({
            ...child,
            id: `${child.id}_dummy`,
          } as RepositoryHierarchyModel)
      ),
      createDummyNodesOfChildren: jest.fn(),
      getSelectedFilesFolderWithoutRepeating: jest
        .fn()
        .mockReturnValue([{ dummy: true }]),
    }

    await TestBed.configureTestingModule({
      imports: [RepositoryBrowserComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: ReprocessingFacade, useValue: mockReprocessingFacade },
        {
          provide: RepositoryBrowserStateService,
          useValue: mockRepoStateService,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(RepositoryBrowserComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should call fetchReposirotyList on ngOnInit', () => {
    jest.spyOn(component as any, 'fetchLoadRepositoryList')
    component.ngOnInit()
    expect(mockReprocessingFacade.fetchReposirotyList).toHaveBeenCalledWith(
      true
    )
  })

  it('should handle addFileFolder', () => {
    jest.spyOn(component.selectedReplacementFiles, 'emit')
    component.checkedFileFoldersIds = [1, 2]
    component.addFileFolder()
    expect(component.selectedReplacementFiles.emit).toHaveBeenCalledWith([
      { dummy: true },
    ])
    expect(component.checkedFileFoldersIds).toEqual([])
  })

  it('should handle onFileSelectionChange when file is selected', () => {
    fixture.componentRef.setInput('isForReprocessing', true)
    jest.spyOn(component.selectedReplacementFile, 'emit')

    const treeItem: TreeItem = {
      index: '0',
      dataItem: { type: 'FILE', id: '1' },
    }
    component.onFileSelectionChange(treeItem)

    // For reprocessing, selecting a file should emit selectedReplacementFile
    expect(component.selectedReplacementFile.emit).toHaveBeenCalledWith({
      type: 'FILE',
      id: '1',
    })
  })

  it('should not emit on folder selection', () => {
    jest.spyOn(component.selectedReplacementFile, 'emit')
    fixture.componentRef.setInput('isForReprocessing', true)

    const treeItem: TreeItem = {
      index: '0',
      dataItem: { type: 'FOLDER', id: '1' },
    }
    component.onFileSelectionChange(treeItem)

    // For reprocessing, selecting a folder should not emit selectedReplacementFile
    expect(component.selectedReplacementFile.emit).not.toHaveBeenCalled()
  })

  it('should handle onSelectionChange add and remove', () => {
    const testRepo: RepositoryModel = {
      fsid: 1,
      fsDisplayName: 'Repo1',
      fsUserName: '',
      fsPassword: '',
      fsDomain: '',
      authenticate: false,
      sharedFolder: '',
      testConnection: '',
      testConnectionError: '',
      repositoryType: '',
    }
    component.selectedRepository = [testRepo]

    const event = {
      deselectedRows: [{ dataItem: testRepo }],
      selectedRows: [],
    }

    component.onSelectionChange(event)

    // if no repository are selected, we need to clear the selected repository heirarchy
    expect(mockReprocessingFacade.clearRepositoryHierarchy).toHaveBeenCalled()
  })

  it('should filter repositories on search', () => {
    component.repositoryList.set([
      {
        fsid: 1,
        fsDisplayName: 'Alpha',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
      {
        fsid: 2,
        fsDisplayName: 'Beta',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
    ])
    component.onRepositorySearch('Alpha')
    expect(component.filteredRepositoryList()).toEqual([
      {
        fsid: 1,
        fsDisplayName: 'Alpha',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
    ])
  })

  it('should reset repository search', () => {
    component.repositoryList.set([
      {
        fsid: 1,
        fsDisplayName: 'Alpha',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
      {
        fsid: 2,
        fsDisplayName: 'Beta',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
    ])
    component.onRepositorySearch('')
    expect(component.filteredRepositoryList()).toEqual([
      {
        fsid: 1,
        fsDisplayName: 'Alpha',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
      {
        fsid: 2,
        fsDisplayName: 'Beta',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
    ])
  })

  it('should refresh repository list', () => {
    component.refreshRepositoryList()
    expect(mockReprocessingFacade.fetchReposirotyList).toHaveBeenCalledWith(
      true
    )
  })

  it('should return true if no records available', () => {
    component.selectedRepository = []
    expect(component.noRecordsAvailable()).toBeTruthy()
  })

  it('should return false if records available', () => {
    component.selectedRepository = [
      {
        fsid: 1,
        fsDisplayName: '',
        fsUserName: '',
        fsPassword: '',
        fsDomain: '',
        authenticate: false,
        sharedFolder: '',
        testConnection: '',
        testConnectionError: '',
        repositoryType: '',
      },
    ]
    expect(component.noRecordsAvailable()).toBeFalsy()
  })

  it('should call clearRepositoryHierarchy on destroy', () => {
    component.ngOnDestroy()
    expect(mockReprocessingFacade.clearRepositoryHierarchy).toHaveBeenCalled()
  })

  it('should call onFolderExpand', fakeAsync(() => {
    const item = { id: '1', fsid: 1 }

    mockReprocessingFacade.fetchSpecificRepositoryHeirarchy.mockReturnValue(
      of({
        data: [
          { id: 'child1', parentId: '-1', type: 'FILE', name: 'child' },
          { id: 'child2', parentId: 'child1', type: 'FOLDER', name: 'folder' },
        ],
      })
    )

    component.onFolderExpand({ dataItem: item })
    tick()

    expect(
      mockRepoStateService.createDummyNodesOfChildren
    ).toHaveBeenCalledWith(item, expect.any(Array))
  }))

  it('should handle getFileFolderHierarchyFromSelectedRepo', () => {
    const result = (component as any).getFileFolderHierarchyFromSelectedRepo([
      { id: '1', parentId: '-1', type: 'FOLDER', name: 'test' },
    ])
    expect(result.length).toBeGreaterThan(0)
  })
})
