import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { UploadFileFolderInfoComponent } from './upload-file-folder-info.component'
import { UploadFacade } from '@venio/data-access/common'
import { VenioNotificationService } from '@venio/feature/notification'
import { BehaviorSubject } from 'rxjs'
import {
  QueuedRepositoryModel,
  MediaSourceType,
} from '@venio/shared/models/interfaces'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('UploadFileFolderInfoComponent', () => {
  let component: UploadFileFolderInfoComponent
  let fixture: ComponentFixture<UploadFileFolderInfoComponent>
  let mockUploadFacade: jest.Mocked<Partial<UploadFacade>>
  let mockNotificationService: jest.Mocked<Partial<VenioNotificationService>>
  let fileFolderSubject: BehaviorSubject<QueuedRepositoryModel[]>
  let validationMessageSubject: BehaviorSubject<any>
  let caseInfoSubject: BehaviorSubject<any>
  let processSuccessSubject: BehaviorSubject<any>
  let processErrorSubject: BehaviorSubject<any>

  beforeEach(async () => {
    // GIVEN mocked dependencies
    fileFolderSubject = new BehaviorSubject<QueuedRepositoryModel[]>([])
    validationMessageSubject = new BehaviorSubject<any>(undefined)
    caseInfoSubject = new BehaviorSubject<any>({ projectId: 123 })
    processSuccessSubject = new BehaviorSubject<any>(undefined)
    processErrorSubject = new BehaviorSubject<any>(undefined)

    mockUploadFacade = {
      selectFileFolderForUpload$: fileFolderSubject.asObservable(),
      validationMessage$: validationMessageSubject,
      selectSelectedCaseInfo$: caseInfoSubject.asObservable(),
      selectProcessFromRepositorySuccessResponse$:
        processSuccessSubject.asObservable(),
      selectProcessFromRepositoryErrorResponse$:
        processErrorSubject.asObservable(),
      selectCustodians$: new BehaviorSubject<string[]>([
        'Custodian 1',
      ]).asObservable(),
      removeQueuedMedia: jest.fn(),
      processFromRepository: jest.fn(),
      clearFileFolder: jest.fn(),
    } as unknown as jest.Mocked<Partial<UploadFacade>>

    mockNotificationService = {
      showSuccess: jest.fn(),
      showError: jest.fn(),
      showWarning: jest.fn(),
    } as unknown as jest.Mocked<Partial<VenioNotificationService>>

    await TestBed.configureTestingModule({
      imports: [UploadFileFolderInfoComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore(),
        {
          provide: UploadFacade,
          useValue: mockUploadFacade,
        },
        {
          provide: VenioNotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadFileFolderInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    // GIVEN the component is set up
    // THEN it should exist
    expect(component).toBeTruthy()
  })

  it('should remove queued media when repository hierarchies are empty', fakeAsync(() => {
    // GIVEN a file folder with empty repository hierarchies
    const mockFileFolder: QueuedRepositoryModel = {
      id: 'test-id',
      custodianName: 'Test Custodian',
      mediaName: 'Test Media',
      mediaSourceType: MediaSourceType.SOURCE_FILE,
      repositoryHierarchies: [],
    }

    // WHEN the file folder is added to the subject
    fileFolderSubject.next([mockFileFolder])

    // Allow time for the effect to run
    tick()
    fixture.detectChanges()

    // THEN removeQueuedMedia should be called with the file folder id
    expect(mockUploadFacade.removeQueuedMedia).toHaveBeenCalledWith('test-id')
  }))

  it('should not remove queued media when repository hierarchies are not empty', fakeAsync(() => {
    // GIVEN a file folder with non-empty repository hierarchies
    const mockFileFolder: QueuedRepositoryModel = {
      id: 'test-id',
      custodianName: 'Test Custodian',
      mediaName: 'Test Media',
      mediaSourceType: MediaSourceType.SOURCE_FILE,
      repositoryHierarchies: [
        {
          id: 'repo-1',
          name: 'Repo 1',
          type: 'FOLDER',
          fsid: '123',
          repositoryDisplayName: 'Test Repo',
          repositoryRootFolderName: 'Root',
          relativePath: '/path/to/repo',
          isLoading: false,
          isLoaded: true,
          isLoadedAll: true,
          isExpanded: false,
          isCancelling: false,
          showNodeMenuOptions: false,
          hasSuccesfulConnection: true,
          errorMessage: '',
          fullPath: '/full/path/to/repo',
          nsfUserIdFiles: null as unknown as File,
        },
      ],
    }

    // WHEN the file folder is added to the subject
    fileFolderSubject.next([mockFileFolder])

    // Allow time for the effect to run
    tick()
    fixture.detectChanges()

    // THEN removeQueuedMedia should not be called
    expect(mockUploadFacade.removeQueuedMedia).not.toHaveBeenCalled()
  }))

  it('should show success notification and clear file folder when process is successful', fakeAsync(() => {
    // GIVEN a successful process response
    const successResponse = { status: 'success', data: [] }

    // WHEN the success response is emitted
    processSuccessSubject.next(successResponse)

    // Allow time for the effect to run
    tick()
    fixture.detectChanges()

    // THEN showSuccess should be called and clearFileFolder should be called
    expect(mockNotificationService.showSuccess).toHaveBeenCalledWith(
      'Selected file/folders queued for processing successfully'
    )
    expect(mockUploadFacade.clearFileFolder).toHaveBeenCalled()
  }))

  it('should show error notification when process fails', fakeAsync(() => {
    // GIVEN an error response
    const errorResponse = { status: 'error', message: 'Failed' }

    // WHEN the error response is emitted
    processErrorSubject.next(errorResponse)

    // Allow time for the effect to run
    tick()
    fixture.detectChanges()

    // THEN showError should be called
    expect(mockNotificationService.showError).toHaveBeenCalledWith(
      'Failed to queue selected files/folders for processing'
    )
  }))

  it('should validate and process added folders when valid', fakeAsync(() => {
    // GIVEN valid file folders
    const mockFileFolders: QueuedRepositoryModel[] = [
      {
        id: 'test-id',
        custodianName: 'Test Custodian',
        mediaName: 'Test Media',
        mediaSourceType: MediaSourceType.SOURCE_FILE,
        repositoryHierarchies: [
          {
            id: 'repo-1',
            name: 'Repo 1',
            type: 'FOLDER',
            fsid: '123',
            repositoryDisplayName: 'Test Repo',
            repositoryRootFolderName: 'Root',
            relativePath: '/path/to/repo',
            isLoading: false,
            isLoaded: true,
            isLoadedAll: true,
            isExpanded: false,
            isCancelling: false,
            showNodeMenuOptions: false,
            hasSuccesfulConnection: true,
            errorMessage: '',
            fullPath: '/full/path/to/repo',
            nsfUserIdFiles: null as unknown as File,
          },
        ],
      },
    ]
    fileFolderSubject.next(mockFileFolders)

    // WHEN processAddedFolders is called
    component.processAddedFolders()

    // THEN processFromRepository should be called with the correct parameters
    expect(mockUploadFacade.processFromRepository).toHaveBeenCalledWith(
      123, // projectId
      mockFileFolders,
      []
    )
  }))

  it('should show error when trying to process with no file folders', fakeAsync(() => {
    // GIVEN no file folders
    fileFolderSubject.next([])

    // WHEN processAddedFolders is called
    component.processAddedFolders()

    // THEN showError should be called and processFromRepository should not be called
    expect(mockNotificationService.showError).toHaveBeenCalledWith(
      'Select file or folder to process'
    )
    expect(mockUploadFacade.processFromRepository).not.toHaveBeenCalled()
  }))

  it('should update validation message when validating', fakeAsync(() => {
    // GIVEN no file folders
    fileFolderSubject.next([])

    // WHEN processAddedFolders is called
    component.processAddedFolders()

    // THEN validationMessage$ should be updated
    expect(validationMessageSubject.value).toEqual({})
  }))
})
