import { ComponentFix<PERSON>, TestBed } from '@angular/core/testing'
import { LayoutContainerComponent } from './layout-container.component'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID, signal } from '@angular/core'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import {
  CaseInfoFacade,
  FieldFacade,
  SearchFacade,
  StartupsFacade,
  ViewFacade,
  CompositeLayoutFacade,
  CompositeLayoutState,
  ReviewsetFacade,
  ReviewSetStateService,
} from '@venio/data-access/review'
import { ActivatedRoute, Router } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import { BehaviorSubject, of, Subject, throwError } from 'rxjs'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
  IframeMessengerFacade,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { VenioNotificationService } from '@venio/feature/notification'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  UserFacade,
  ValidatorService,
  ModuleLoginFacade,
  ModuleLoginStateService,
} from '@venio/data-access/common'
import {
  DocumentTagService,
  ReviewPanelViewState,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'

describe('LayoutContainerComponent', () => {
  let component: LayoutContainerComponent
  let fixture: ComponentFixture<LayoutContainerComponent>
  let queryParamsSubject: BehaviorSubject<any>
  let mockIframeMessengerFacade: any
  let mockIframeMessengerService: any
  let mockSearchFacade: any
  let mockViewFacade: any
  let mockReviewsetFacade: any
  let mockModuleLoginFacade: any
  let mockModuleLoginStateService: any
  let mockReviewSetStateService: any
  let mockRouter: any

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({
      projectId: 123,
      reviewSetId: 456,
    })

    // Create comprehensive mocks
    mockIframeMessengerFacade = {
      selectIframeMessengerContent$: jest.fn().mockReturnValue(of(null)),
    }

    mockIframeMessengerService = {
      sendMessage: jest.fn(),
    }

    mockSearchFacade = {
      selectSearchFormValues$: of({}),
      getMainSearchBreadcrumbs$: of([]),
      getConditionSearchBreadcrumbs$: of([]),
      getFilterSearchBreadcrumbs$: of([]),
      getReviewSetBatchId$: of(0),
      search: jest.fn(),
      resetSearchState: jest.fn(),
      setSearchDupOption: jest.fn(),
      setIncludePC: jest.fn(),
      triggerSearch$: of({}),
    }

    mockViewFacade = {
      selectUserDefaultView$: of(null),
      selectIsUserDefaultViewLoading$: of(false),
      fetchUserDefaultView: jest.fn(),
      resetView: jest.fn(),
      isViewManuallyChanged: jest.fn().mockReturnValue(false),
    }

    mockReviewsetFacade = {
      fetchReviewSetBasicInfo$: jest
        .fn()
        .mockReturnValue(of({ data: { reviewSetId: 456 } })),
      checkBatchReviewCompletedAction$: of(0),
      checkoutBatchReviewSetAction: Object.assign(new Subject(), {
        next: jest.fn(),
      }),
      batchCheckInAction$: of(null),
      fetchReviewSetBatchInfo$: jest
        .fn()
        .mockReturnValue(of({ data: { remainingFiles: 0, batchId: 1 } })),
      checkInReviewBatch$: jest
        .fn()
        .mockReturnValue(of({ message: 'Success' })),
    }

    mockModuleLoginFacade = {
      createNewModuleLogin: jest.fn().mockReturnValue(of({})),
      updateExistingModuleLogin: jest.fn().mockReturnValue(of({})),
    }

    mockModuleLoginStateService = {
      moduleLoginId: jest.fn().mockReturnValue(1),
      previousReviewSetId: jest.fn().mockReturnValue(456),
      updateProjectId: jest.fn(),
      updatePreviousReviewSetId: jest.fn(),
    }

    mockReviewSetStateService = {
      reviewSetId: jest.fn().mockReturnValue(456),
      reviewSetBasicInfo: { set: jest.fn() },
      reset: jest.fn(),
      isBatchReview: jest.fn().mockReturnValue(false),
      batchId: { set: jest.fn() },
      reviewsetBatchInfo: { set: jest.fn() },
    }

    mockRouter = {
      parseUrl: jest.fn().mockReturnValue({
        root: {
          children: {
            primary: {
              segments: [{ path: 'documents' }],
            },
          },
        },
      }),
      url: '/documents',
    }

    await TestBed.configureTestingModule({
      imports: [LayoutContainerComponent, BrowserAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: FieldFacade,
          useValue: {
            fetchAllVenioFields: jest.fn(),
            fetchReviewPanelDefaltFields: jest.fn(),
            fetchAllPermittedFields: jest.fn(),
            fetchAllCustomWithPredefinedFields: jest.fn(),
            fetchAllCustomFields: jest.fn(),
            fetchUserLayoutId: jest.fn(),
            resetField: jest.fn(),
          },
        },
        {
          provide: CaseInfoFacade,
          useValue: {
            isProjectManuallyChanged: jest.fn().mockReturnValue(false),
          },
        },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: ViewFacade, useValue: mockViewFacade },
        {
          provide: StartupsFacade,
          useValue: {
            fetchUserRights: jest.fn(),
            fetchDefaultGroups: jest.fn(),
          },
        },
        {
          provide: CompositeLayoutFacade,
          useValue: {
            fetchDefaultLayout$: jest.fn().mockReturnValue(of({ data: {} })),
            fetchDefaultReviewSetLayout$: jest
              .fn()
              .mockReturnValue(of({ data: {} })),
          },
        },
        {
          provide: CompositeLayoutState,
          useValue: {
            userLayouts: { set: jest.fn() },
            userSelectedLayout: jest.fn().mockReturnValue(null),
          },
        },
        { provide: ReviewsetFacade, useValue: mockReviewsetFacade },
        { provide: ReviewSetStateService, useValue: mockReviewSetStateService },
        { provide: ModuleLoginFacade, useValue: mockModuleLoginFacade },
        {
          provide: ModuleLoginStateService,
          useValue: mockModuleLoginStateService,
        },
        { provide: IframeMessengerFacade, useValue: mockIframeMessengerFacade },
        {
          provide: IframeMessengerService,
          useValue: mockIframeMessengerService,
        },
        { provide: Router, useValue: mockRouter },
        {
          provide: BreadcrumbFacade,
          useValue: {
            selectBreadcrumbStack$: of([]),
            selectCompleteBreadcrumbSyntax$: of(''),
            resetBreadcrumbState: jest.fn(),
            storeBreadcrumbs: jest.fn(),
            updateBreadcrumb: jest.fn(),
          },
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserSuccessResponse$: of({ data: { userId: 1 } }),
          },
        },
        { provide: ValidatorService, useValue: {} },
        {
          provide: DocumentTagService,
          useValue: { getTagRuleData: jest.fn().mockReturnValue({}) },
        },
        {
          provide: ReviewPanelViewState,
          useValue: { resetDocuemntTagSelection: jest.fn() },
        },
        {
          provide: UtilityPanelFacade,
          useValue: { resetUtilityPanelState: jest.fn() },
        },
        {
          provide: ConfirmationDialogService,
          useValue: {
            showConfirmationDialog: jest.fn().mockReturnValue(of(true)),
          },
        },
        NotificationService,
        VenioNotificationService,
        provideMockStore({}),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: { projectId: 123, reviewSetId: 456 } },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(LayoutContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('ngOnInit', () => {
    it('should call initial setup methods', () => {
      const sendMessageSpy = jest.spyOn(
        mockIframeMessengerService,
        'sendMessage'
      )
      const updateProjectIdSpy = jest.spyOn(
        mockModuleLoginStateService,
        'updateProjectId'
      )

      component.ngOnInit()

      expect(sendMessageSpy).toHaveBeenCalledWith({
        iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        eventTriggeredFor: 'PARENT_WINDOW',
        eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
        payload: {
          type: MessageType.LAYOUT_CHANGE,
          content: {
            layoutReady: true,
          },
        },
      })
      expect(updateProjectIdSpy).toHaveBeenCalledWith(123)
    })
  })

  describe('Query Parameter Handling', () => {
    it('should initialize with correct project and review set IDs', () => {
      // The component should be initialized with the query params from the mock
      expect(component['projectId']).toBe(123)
      expect(mockModuleLoginStateService.updateProjectId).toHaveBeenCalledWith(
        123
      )
    })

    it('should handle project ID changes correctly', () => {
      const resetViewSpy = jest.spyOn(mockViewFacade, 'resetView')

      // Simulate project ID change by creating a new component with different params
      queryParamsSubject.next({ projectId: 999, reviewSetId: 456 })

      // The component should handle project changes
      expect(resetViewSpy).toHaveBeenCalled()
    })
  })

  describe('Module Login Periodic Updates', () => {
    it('should setup periodic updates for batch review', () => {
      mockReviewSetStateService.isBatchReview.mockReturnValue(true)
      mockModuleLoginStateService.moduleLoginId.mockReturnValue(1)

      const updateSpy = jest.spyOn(
        mockModuleLoginFacade,
        'updateExistingModuleLogin'
      )

      component.ngAfterViewInit()

      // The periodic update should be set up (we can't easily test the interval without fakeAsync)
      expect(updateSpy).toHaveBeenCalled()
    })
  })

  describe('Project ID Changes', () => {
    it('should handle project ID changes and reset states', () => {
      const resetViewSpy = jest.spyOn(mockViewFacade, 'resetView')
      const resetFieldSpy = jest.spyOn(
        TestBed.inject(FieldFacade),
        'resetField'
      )

      queryParamsSubject.next({ projectId: 999, reviewSetId: 456 })

      expect(resetViewSpy).toHaveBeenCalledWith([
        'userDefaultView',
        'selectedViewDefaultExpression',
      ])
      expect(resetFieldSpy).toHaveBeenCalledWith([
        'allCustomFields',
        'fieldPanelMap',
        'venioFieldsPanelMap',
        'customFieldsPanelMap',
      ])
    })
  })

  describe('#handleBatchCheckoutComplete', () => {
    let mockNotificationFacade: any
    let mockConfirmationDialogService: any

    beforeEach(() => {
      mockNotificationFacade = TestBed.inject(VenioNotificationService)
      mockConfirmationDialogService = TestBed.inject(ConfirmationDialogService)

      // Reset mocks
      jest.clearAllMocks()
    })

    describe('when not on review page', () => {
      beforeEach(() => {
        // Mock router to return non-documents path
        mockRouter.parseUrl.mockReturnValue({
          root: {
            children: {
              primary: {
                segments: [{ path: 'other-page' }],
              },
            },
          },
        })
      })

      it('should not process batch completion when not on review page', () => {
        const fetchBatchInfoSpy = jest.spyOn(
          mockReviewsetFacade,
          'fetchReviewSetBatchInfo$'
        )

        // Trigger batch ID emission
        mockSearchFacade.getReviewSetBatchId$ = of(1)

        component.ngOnInit()

        expect(fetchBatchInfoSpy).not.toHaveBeenCalled()
      })
    })

    describe('when on review page', () => {
      beforeEach(() => {
        // Ensure we're on review page
        mockRouter.parseUrl.mockReturnValue({
          root: {
            children: {
              primary: {
                segments: [{ path: 'documents' }],
              },
            },
          },
        })
      })

      it('should ignore batch IDs <= 0', () => {
        const fetchBatchInfoSpy = jest.spyOn(
          mockReviewsetFacade,
          'fetchReviewSetBatchInfo$'
        )

        // Trigger with invalid batch ID
        mockSearchFacade.getReviewSetBatchId$ = of(0)

        component.ngOnInit()

        expect(fetchBatchInfoSpy).not.toHaveBeenCalled()
      })

      it('should process valid batch ID and fetch batch info', () => {
        const batchInfo = { remainingFiles: 5, batchId: 1 }
        const fetchBatchInfoSpy = jest
          .spyOn(mockReviewsetFacade, 'fetchReviewSetBatchInfo$')
          .mockReturnValue(of({ data: batchInfo }))
        const setBatchIdSpy = jest.spyOn(
          mockReviewSetStateService.batchId,
          'set'
        )
        const setBatchInfoSpy = jest.spyOn(
          mockReviewSetStateService.reviewsetBatchInfo,
          'set'
        )

        // Trigger with valid batch ID
        mockSearchFacade.getReviewSetBatchId$ = of(1)

        component.ngOnInit()

        expect(setBatchIdSpy).toHaveBeenCalledWith(1)
        expect(fetchBatchInfoSpy).toHaveBeenCalledWith(123, 456, 1)
        expect(setBatchInfoSpy).toHaveBeenCalledWith(batchInfo)
      })

      describe('when batch has remaining files > 0', () => {
        it('should not show confirmation dialog', () => {
          const batchInfo = { remainingFiles: 5, batchId: 1 }
          mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
            of({ data: batchInfo })
          )
          const confirmationSpy = jest.spyOn(
            mockConfirmationDialogService,
            'showConfirmationDialog'
          )

          mockSearchFacade.getReviewSetBatchId$ = of(1)

          component.ngOnInit()

          expect(confirmationSpy).not.toHaveBeenCalled()
        })
      })

      describe('when batch has no remaining files (remainingFiles = 0)', () => {
        let batchInfo: any

        beforeEach(() => {
          batchInfo = { remainingFiles: 0, batchId: 1 }
          mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
            of({ data: batchInfo })
          )
        })

        it('should show confirmation dialog with correct message', () => {
          const confirmationSpy = jest
            .spyOn(mockConfirmationDialogService, 'showConfirmationDialog')
            .mockReturnValue(of(true))

          mockSearchFacade.getReviewSetBatchId$ = of(1)

          component.ngOnInit()

          expect(confirmationSpy).toHaveBeenCalledWith(
            'Check In',
            'Review of all documents in batch is completed. Do you want to check in and open new batch?',
            component['viewContainerRef']
          )
        })

        it('should not proceed when user cancels confirmation', () => {
          const confirmationSpy = jest
            .spyOn(mockConfirmationDialogService, 'showConfirmationDialog')
            .mockReturnValue(of(false))
          const checkInSpy = jest.spyOn(
            mockReviewsetFacade,
            'checkInReviewBatch$'
          )

          mockSearchFacade.getReviewSetBatchId$ = of(1)

          component.ngOnInit()

          expect(confirmationSpy).toHaveBeenCalled()
          expect(checkInSpy).not.toHaveBeenCalled()
        })

        describe('when user confirms check-in', () => {
          beforeEach(() => {
            jest
              .spyOn(mockConfirmationDialogService, 'showConfirmationDialog')
              .mockReturnValue(of(true))
          })

          it('should call check-in API with correct parameters', () => {
            const checkInSpy = jest
              .spyOn(mockReviewsetFacade, 'checkInReviewBatch$')
              .mockReturnValue(of({ message: 'Batch checked in successfully' }))

            mockSearchFacade.getReviewSetBatchId$ = of(1)

            component.ngOnInit()

            expect(checkInSpy).toHaveBeenCalledWith(123, 456, 1)
          })

          it('should show success notification on successful check-in', () => {
            const successMessage = 'Batch checked in successfully'
            jest
              .spyOn(mockReviewsetFacade, 'checkInReviewBatch$')
              .mockReturnValue(of({ message: successMessage }))
            const showSuccessSpy = jest.spyOn(
              mockNotificationFacade,
              'showSuccess'
            )

            mockSearchFacade.getReviewSetBatchId$ = of(1)

            component.ngOnInit()

            expect(showSuccessSpy).toHaveBeenCalledWith(successMessage)
          })

          it('should have checkout action available for triggering new batch', () => {
            // Verify that the checkout action is properly set up
            expect(
              mockReviewsetFacade.checkoutBatchReviewSetAction
            ).toBeDefined()
            expect(
              mockReviewsetFacade.checkoutBatchReviewSetAction.next
            ).toBeDefined()
          })

          it('should not proceed if check-in API returns falsy response', () => {
            const checkInSpy = jest
              .spyOn(mockReviewsetFacade, 'checkInReviewBatch$')
              .mockReturnValue(of(null))
            const showSuccessSpy = jest.spyOn(
              mockNotificationFacade,
              'showSuccess'
            )

            mockSearchFacade.getReviewSetBatchId$ = of(1)

            component.ngOnInit()

            expect(checkInSpy).toHaveBeenCalled()
            expect(showSuccessSpy).not.toHaveBeenCalled()
          })

          it('should handle check-in API errors and show error notification', () => {
            const errorMessage = 'Failed to check in batch'
            const httpError = { error: { message: errorMessage } }
            const checkInSpy = jest
              .spyOn(mockReviewsetFacade, 'checkInReviewBatch$')
              .mockReturnValue(throwError(() => httpError))
            const showErrorSpy = jest.spyOn(mockNotificationFacade, 'showError')

            mockSearchFacade.getReviewSetBatchId$ = of(1)

            component.ngOnInit()

            expect(checkInSpy).toHaveBeenCalled()
            expect(showErrorSpy).toHaveBeenCalledWith(errorMessage)
          })
        })
      })

      describe('multiple batch sources', () => {
        it('should handle batch completion from searchFacade.getReviewSetBatchId$', () => {
          const batchInfo = { remainingFiles: 0, batchId: 2 }
          mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
            of({ data: batchInfo })
          )
          const setBatchIdSpy = jest.spyOn(
            mockReviewSetStateService.batchId,
            'set'
          )

          mockSearchFacade.getReviewSetBatchId$ = of(2)

          component.ngOnInit()

          expect(setBatchIdSpy).toHaveBeenCalledWith(2)
        })

        it('should handle batch completion from reviewsetFacade.checkBatchReviewCompletedAction$', () => {
          const batchInfo = { remainingFiles: 0, batchId: 3 }
          mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
            of({ data: batchInfo })
          )
          mockReviewsetFacade.checkBatchReviewCompletedAction$ = of(3)
          const setBatchIdSpy = jest.spyOn(
            mockReviewSetStateService.batchId,
            'set'
          )

          component.ngOnInit()

          expect(setBatchIdSpy).toHaveBeenCalledWith(3)
        })
      })

      describe('subscription management', () => {
        it('should unsubscribe when component is destroyed', () => {
          const batchInfo = { remainingFiles: 5, batchId: 1 }
          mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
            of({ data: batchInfo })
          )
          mockSearchFacade.getReviewSetBatchId$ = of(1)

          component.ngOnInit()

          // Trigger destroy
          component.ngOnDestroy()

          // Emit another batch ID - should not be processed
          const fetchBatchInfoSpy = jest.spyOn(
            mockReviewsetFacade,
            'fetchReviewSetBatchInfo$'
          )
          mockSearchFacade.getReviewSetBatchId$ = of(2)

          expect(fetchBatchInfoSpy).not.toHaveBeenCalledWith(123, 456, 2)
        })
      })
    })
  })

  describe('Search and Breadcrumb Handling', () => {
    it('should handle search form values correctly', () => {
      const searchFormData = { searchDuplicateOption: 'all', includePC: true }
      mockSearchFacade.selectSearchFormValues$ = of(searchFormData)

      component.ngOnInit()

      expect(component['currentSearchFormData']).toEqual(searchFormData)
    })
  })

  describe('Layout and State Management', () => {
    it('should have correct project ID from route params', () => {
      expect(component['projectId']).toBe(123)
    })

    it('should have correct review set ID from state service', () => {
      expect(component['reviewSetId']).toBe(456)
    })

    it('should determine if on review page correctly', () => {
      expect(component['isReviewPage']).toBeTruthy()
    })
  })

  describe('Component Lifecycle', () => {
    it('should clean up subscriptions on destroy', () => {
      const resetSearchSpy = jest.spyOn(mockSearchFacade, 'resetSearchState')

      component.ngOnDestroy()

      expect(resetSearchSpy).toHaveBeenCalledWith([
        'searchFormValues',
        'shouldResetSearchInputControls',
      ])
    })

    it('should handle ngAfterViewInit correctly', () => {
      // Test that ngAfterViewInit executes without errors
      expect(() => component.ngAfterViewInit()).not.toThrow()
    })
  })

  describe('#handleQueryParams', () => {
    it('should update reviewSetId and fetch review set info when reviewSetId is present', () => {
      // GIVEN query params with reviewSetId
      const queryParams = { projectId: '123', reviewSetId: '456' }
      queryParamsSubject.next(queryParams)

      // THEN reviewSetId should be updated in the state
      // Note: We can't directly test the signal.set call, but we can verify the facade was called

      // AND fetchReviewSetBasicInfo$ should be called with correct parameters
      expect(
        mockModuleLoginFacade.fetchReviewSetBasicInfo$
      ).toHaveBeenCalledWith('123', '456')

      // AND reviewSetBasicInfo should be updated with the response data
      // Note: We can't directly test the signal.set call
    })

    it('should create new module login when moduleLoginId is not present', () => {
      // GIVEN moduleLoginId is not present
      mockReviewSetStateService.moduleLoginId = signal(0)

      // AND query params with reviewSetId
      const queryParams = { projectId: '123', reviewSetId: '456' }
      queryParamsSubject.next(queryParams)

      // THEN createNewModuleLogin should be called with correct parameters
      expect(mockModuleLoginFacade.createNewModuleLogin).toHaveBeenCalledWith(
        '123',
        '456'
      )
    })

    it('should update existing module login when moduleLoginId is present and reviewSetId matches previousReviewSetId', () => {
      // GIVEN moduleLoginId is present
      mockReviewSetStateService.moduleLoginId = signal(789)

      // AND previousReviewSetId matches the new reviewSetId
      mockReviewSetStateService.previousReviewSetId = signal(456)

      // AND query params with reviewSetId
      const queryParams = { projectId: '123', reviewSetId: '456' }
      queryParamsSubject.next(queryParams)

      // THEN updateExistingModuleLogin should be called with correct parameters
      expect(
        mockModuleLoginFacade.updateExistingModuleLogin
      ).toHaveBeenCalledWith('123', '456')
    })

    it('should reset reviewset state when reviewSetId is not present', () => {
      // GIVEN query params without reviewSetId
      const queryParams = { projectId: '123' }
      queryParamsSubject.next(queryParams)

      // THEN reviewset state should be reset
      expect(mockReviewSetStateService.reset).toHaveBeenCalled()
    })
  })
})
