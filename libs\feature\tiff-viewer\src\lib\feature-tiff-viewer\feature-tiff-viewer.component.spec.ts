import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureTiffViewerComponent } from './feature-tiff-viewer.component'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  TiffViewerPayload,
  Viewer,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { Subject, of } from 'rxjs'
import { ReviewPanelFacade } from '@venio/data-access/document-utility'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { UserFacade } from '@venio/data-access/common'
import { ControlSettingService } from '@venio/data-access/control-settings'

describe('FeatureTiffViewerComponent', () => {
  let component: FeatureTiffViewerComponent
  let fixture: ComponentFixture<FeatureTiffViewerComponent>

  // Mock for DocumentsFacade
  const mockDocumentsFacade = {
    onLoadTiffViewer: new Subject<TiffViewerPayload>(),
    viewerComponentReady: null as Viewer,
  }

  // Mock for SearchFacade
  const mockSearchFacade = {
    getSearchTempTables$: of({
      searchGuid: 'test-search-guid-123',
      searchResultTempTable: 'temp_table_123',
    }),
  }

  // Mock for ReviewPanelFacade
  const mockReviewPanelFacade = {
    refrehDocumentHistory: jest.fn(),
  }

  // Mock for UserFacade
  const mockUserFacade = {
    selectCurrentUserDetails$: of({
      userId: 123,
      userName: 'testuser',
    }),
  }

  // Mock for ControlSettingService
  const mockControlSettingService = {
    getControlSetting: {
      WEB_BASE_URL: 'https://test-base-url.com',
    },
  }

  // Mock for IframeMessengerService
  const mockIframeMessengerService = {
    messageReceived: new Subject(),
    sendMessage: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureTiffViewerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: DocumentsFacade, useValue: mockDocumentsFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: FieldFacade, useValue: {} },
        { provide: ReviewPanelFacade, useValue: mockReviewPanelFacade },
        { provide: UserFacade, useValue: mockUserFacade },
        { provide: ControlSettingService, useValue: mockControlSettingService },
        {
          provide: IframeMessengerService,
          useValue: mockIframeMessengerService,
        },
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
            snapshot: {
              queryParams: {},
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureTiffViewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should load tiff viewer when onLoadTiffViewer emits', () => {
    // Arrange
    const tiffViewerPayload: TiffViewerPayload = {
      fileId: 456,
      projectId: 789,
    }

    // Act
    mockDocumentsFacade.onLoadTiffViewer.next(tiffViewerPayload)

    // Assert
    expect(component.iframeSrc).toBeDefined()
  })

  it('should set viewerComponentReady to Tiff after view init', () => {
    // Assert
    expect(mockDocumentsFacade.viewerComponentReady).toBe(Viewer.Tiff)
  })
})
