import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { UploadService } from '../../services'
import * as UploadActions from './upload.actions'
import { HttpErrorResponse } from '@angular/common/http'
import { fetch } from '@ngrx/router-store/data-persistence'
import { map, switchMap } from 'rxjs'
import { MediaSourceType, ResponseModel } from '@venio/shared/models/interfaces'

@Injectable()
export class UploadEffects {
  private readonly actions$ = inject(Actions)

  private readonly uploadService = inject(UploadService)

  public processFromRepository$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UploadActions.processFromRepository),
      fetch({
        run: ({ projectId, repositoryFiles, nsfUserIdFiles }) => {
          return this.uploadService
            .processRepository$(projectId, repositoryFiles, nsfUserIdFiles)
            .pipe(
              switchMap((response: ResponseModel) => {
                const sessionId = response.data.sessionId
                const payload = response.data.collectedRepositoryUploadInfo
                return this.uploadService.startRepositoryProcessing(
                  projectId,
                  sessionId,
                  payload,
                  -1,
                  false,
                  MediaSourceType.SOURCE_FILE,
                  false,
                  true,
                  false,
                  null,
                  [],
                  false,
                  false
                )
              }),
              map((result) =>
                UploadActions.processFromRepositorySuccess({
                  processSuccess: {
                    status: 'Success',
                    message: 'Successfully Queued',
                    data: {
                      result,
                    },
                  },
                })
              )
            )
        },

        onError: (_, error: HttpErrorResponse) => {
          const processError = error.error as ResponseModel
          return UploadActions.processFromRepositoryFailure({
            processError,
          })
        },
      })
    )
  )

  public fetchCustodians$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UploadActions.fetchCustodians),
      fetch({
        id: ({ projectId }) => projectId,
        run: ({ projectId }) => {
          return this.uploadService.fetchCustodians$(projectId).pipe(
            map((custodiansSuccess: ResponseModel) =>
              UploadActions.FetchCustodiansSuccess({
                custodiansSuccess,
              })
            )
          )
        },

        onError: (_, error: HttpErrorResponse) => {
          const custodiansError = error.error as ResponseModel
          return UploadActions.FetchCustodiansFailure({
            custodiansError,
          })
        },
      })
    )
  )
}
