import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { UploadFileFolderInfoItemComponent } from './upload-file-folder-info-item.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { UploadFacade } from '@venio/data-access/common'
import { BehaviorSubject, of } from 'rxjs'
import {
  MediaSourceType,
  QueuedRepositoryModel,
  RepositoryHierarchyModel,
  UploadMediaTypes,
} from '@venio/shared/models/interfaces'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'

describe('UploadFileFolderInfoItemComponent', () => {
  let component: UploadFileFolderInfoItemComponent
  let fixture: ComponentFixture<UploadFileFolderInfoItemComponent>
  let mockUploadFacade: Partial<UploadFacade>

  // Mock data
  const mockRepositoryHierarchy: RepositoryHierarchyModel = {
    id: 'repo-1',
    name: 'test.zip',
    type: 'FILE',
    fsid: '123',
    repositoryDisplayName: 'Test Repo',
    repositoryRootFolderName: 'Root',
    relativePath: '/path/to/repo',
    isLoading: false,
    isLoaded: true,
    isLoadedAll: true,
    isExpanded: false,
    isCancelling: false,
    showNodeMenuOptions: false,
    hasSuccesfulConnection: true,
    errorMessage: '',
    fullPath: '/full/path/to/repo',
    password: '',
    nsfUserIdFiles: null as unknown as File,
  }

  const mockRepositoryFileFolder: QueuedRepositoryModel = {
    id: 'folder-1',
    custodianName: 'Test Custodian',
    mediaName: 'Test Media',
    mediaSourceType: MediaSourceType.SOURCE_FILE,
    repositoryHierarchies: [mockRepositoryHierarchy],
    //s3ItemsHierarchies: [],
  }

  beforeEach(async () => {
    mockUploadFacade = {
      selectCustodians$: of(['Test Custodian', 'Another Custodian']),
      validationMessage$: new BehaviorSubject(undefined),
      updateFileFolderForUpload: jest.fn(),
      removeFileFolder: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [UploadFileFolderInfoItemComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore(),
        provideNoopAnimations(),
        {
          provide: UploadFacade,
          useValue: mockUploadFacade,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadFileFolderInfoItemComponent)
    component = fixture.componentInstance

    // Add debug logs
    console.log('Before setting input:', component.fileFolder())

    // Initialize the component with default values
    fixture.componentRef.setInput(
      'uploadMediaType',
      UploadMediaTypes.REPOSITORY
    )
    fixture.componentRef.setInput('fileFolder', {
      id: 'test-id',
      custodianName: 'Test',
      mediaName: '',
      mediaSourceType: MediaSourceType.SOURCE_FILE,
      repositoryHierarchies: [mockRepositoryHierarchy],
      s3ItemsHierarchies: [],
    })

    console.log('After setting input:', component.fileFolder())

    fixture.detectChanges()

    console.log('After detectChanges:', component.fileFolder())
    console.log('Form value:', component.form.get('custodianName')?.value)
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should initialize form with default values', () => {
    expect(component.form).toBeDefined()
    expect(component.form.get('custodianName')).toBeDefined()
    expect(component.form.get('mediaName')).toBeDefined()
    expect(component.repositories).toBeDefined()
    //expect(component.s3Repostories).toBeDefined()
  })

  it('should have access to the uploadFacade', () => {
    expect(component.validationMessage).toBeDefined()
    expect(component.custodians).toBeDefined()
  })

  it('should call removeFileFolder when deleteFileFolder is called', () => {
    // GIVEN a fileFolder with repositories
    fixture.componentRef.setInput('fileFolder', mockRepositoryFileFolder)
    fixture.componentRef.setInput(
      'uploadMediaType',
      UploadMediaTypes.REPOSITORY
    )
    fixture.detectChanges()

    // WHEN deleteFileFolder is called
    component.deleteFileFolder('repo-1')

    // THEN removeFileFolder should be called with the correct ID
    expect(mockUploadFacade.removeFileFolder).toHaveBeenCalledWith('repo-1')
  })

  it('should identify encryptable repository files correctly', () => {
    // GIVEN the component is initialized
    fixture.detectChanges()

    // WHEN checking different file types
    const zipFile = { ...mockRepositoryHierarchy, name: 'test.zip' }
    const pdfFile = { ...mockRepositoryHierarchy, name: 'test.pdf' }
    const txtFile = { ...mockRepositoryHierarchy, name: 'test.txt' }

    // THEN it should correctly identify encryptable files
    expect(component.checkIfAnyRepositoryFileisEncryptable(zipFile)).toBe(true)
    expect(component.checkIfAnyRepositoryFileisEncryptable(pdfFile)).toBe(true)
    expect(component.checkIfAnyRepositoryFileisEncryptable(txtFile)).toBe(false)
  })

  it('should identify Lotus Notes files correctly', () => {
    // GIVEN the component is initialized
    fixture.detectChanges()

    // WHEN checking different file types
    const nsfFile = { ...mockRepositoryHierarchy, name: 'test.nsf' }
    const txtFile = { ...mockRepositoryHierarchy, name: 'test.txt' }

    // THEN it should correctly identify NSF files
    expect(component.isLotusNotesFile(nsfFile)).toBe(true)
    expect(component.isLotusNotesFile(txtFile)).toBe(false)
  })

  it('should update validation message signal when validation messages change', () => {
    // GIVEN a component with default setup
    // We'll use the existing component instance that's already set up in beforeEach
    // This avoids issues with form initialization

    // WHEN validation messages are updated
    const validationMessages = {
      'test-id': {
        // Use the ID that matches the fileFolder ID from beforeEach
        custodianName: 'Custodian name is required',
        mediaName: 'Media name is required',
      },
    }
    mockUploadFacade.validationMessage$.next(validationMessages)
    fixture.detectChanges()

    // THEN the validationMessage signal should be updated
    expect(component.validationMessage()).toEqual(validationMessages)
  })

  it('should attempt to trigger file selection when onBrowseClick is called', () => {
    // GIVEN a fileFolder with an NSF file
    fixture.componentRef.setInput('fileFolder', mockRepositoryFileFolder)
    fixture.componentRef.setInput(
      'uploadMediaType',
      UploadMediaTypes.REPOSITORY
    )
    fixture.detectChanges()

    // Create a spy on the component's onBrowseClick method
    const onBrowseClickSpy = jest.spyOn(component, 'onBrowseClick')

    // WHEN onBrowseClick is called
    // This might not actually click a button in the test environment,
    // but we can verify the method was called
    component.onBrowseClick(0)

    // THEN the onBrowseClick method should be called
    expect(onBrowseClickSpy).toHaveBeenCalledWith(0)
  })

  it('should update fileFolder when form values change', fakeAsync(() => {
    // GIVEN a fileFolder with repositories
    fixture.componentRef.setInput('fileFolder', mockRepositoryFileFolder)
    fixture.componentRef.setInput(
      'uploadMediaType',
      UploadMediaTypes.REPOSITORY
    )
    fixture.detectChanges()

    // WHEN the form values are changed
    component.form.get('custodianName')?.setValue('Updated Custodian')
    component.form.get('mediaName')?.setValue('Updated Media')
    component.repositories.at(0).get('password')?.setValue('password123')

    // Allow time for debounce
    tick(300)

    // THEN updateFileFolderForUpload should be called with updated data
    expect(mockUploadFacade.updateFileFolderForUpload).toHaveBeenCalled()
    const updateCall = (mockUploadFacade.updateFileFolderForUpload as jest.Mock)
      .mock.calls[0][0]
    expect(updateCall.custodianName).toBe('Updated Custodian')
    expect(updateCall.mediaName).toBe('Updated Media')
    expect(updateCall.repositoryHierarchies[0].password).toBe('password123')
  }))
})
