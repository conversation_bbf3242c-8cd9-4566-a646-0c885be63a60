import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadContainerComponent } from './upload-container.component'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { UploadFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  CaseDetailModel,
  MediaSourceType,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'

describe('UploadContainerComponent', () => {
  let component: UploadContainerComponent
  let fixture: ComponentFixture<UploadContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadContainerComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: WINDOW,
          useValue: windowFactory(PLATFORM_ID),
        },
        {
          provide: UploadFacade,
          useValue: {
            updateActiveStepperIndex: jest.fn(),
            resetUploadState: jest.fn(),
            updateUploadSourceSelection: jest.fn(),
            selectStepperActiveIndex$: of(0),
            selectSelectedCaseInfo$: of({} as CaseDetailModel),
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
            selectSelectedFileMetaInfo$: of([]),
            fetchCustodians: jest.fn(),
            selectMediaSourceType: jest
              .fn()
              .mockReturnValue(of(MediaSourceType.SOURCE_FILE)),
          } satisfies Partial<UploadFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
