import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { RepositoryBrowserComponent } from '../reprocessing/repository-browser/repository-browser.component'
import { UploadFileFolderInfoComponent } from './upload-file-folder-info/upload-file-folder-info.component'
import {
  MediaSourceType,
  RepositoryHierarchyModel,
} from '@venio/shared/models/interfaces'
import {
  RepositoryBrowserStateService,
  UploadFacade,
} from '@venio/data-access/common'
import { UuidGenerator } from '@venio/util/uuid'
import { VenioNotificationService } from '@venio/feature/notification'

@Component({
  selector: 'venio-repository-upload',
  standalone: true,
  imports: [
    CommonModule,
    RepositoryBrowserComponent,
    UploadFileFolderInfoComponent,
  ],
  templateUrl: './repository-upload.component.html',
  styleUrl: './repository-upload.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RepositoryUploadComponent {
  public uploadFacade = inject(UploadFacade)

  private repoStateService = inject(RepositoryBrowserStateService)

  private noficationService = inject(VenioNotificationService)

  public onFileFolderSelected(data: RepositoryHierarchyModel[]): void {
    if (!data?.length) {
      this.noficationService.showWarning('Select at least one file or folder')
      return
    }
    const conflictedQueuedData =
      this.repoStateService.validateIfNewSourceAlreadyExists(data)
    if (
      conflictedQueuedData.childOfNewPaths.length > 0 ||
      conflictedQueuedData.parentOfNewPaths.length > 0
    ) {
      this.repoStateService.updateExistingFolders(data)
    } else {
      if (data.length > 0) {
        this.uploadFacade.addFileFolderForUpload({
          id: UuidGenerator.uuid,
          custodianName: '',
          mediaName: '',
          mediaSourceType:
            this.repoStateService.mediaSourceType() ||
            MediaSourceType.SOURCE_FILE,
          repositoryHierarchies: data,
        })
      }
    }
  }
}
