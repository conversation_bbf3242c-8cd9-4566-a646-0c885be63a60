<!-- upload-file-progress-statuses.component.html -->
<!-- (Content is identical to the previous response's version of this file) -->

<div class="t-flex t-flex-col t-gap-5 t-flex-1">
  <div
    class="t-flex t-flex-row t-flex-1 t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
    <p class="t-font-bold t-text-[17px] t-text-[#000000DE] t-self-center">
      {{ uploadStatusHeaderLabel() }}
    </p>

    <!-- Unprocessed files notification -->
    @if (unprocessedFiles().count > 0) {
    <div
      class="t-flex t-relative t-w-[370px] before:t-inset-0 before:t-absolute before:t-bg-[#ED7425] before:t-opacity-25 before:t-rounded-[4px] t-mx-auto t-px-5 t-py-[2px] t-text-center t-text-wrap">
      <p class="t-relative t-text-[#000000]">
        We found
        <span class="t-font-bold t-text-[13px] t-text-[#ED7428]">{{
          unprocessedFiles().count
        }}</span>
        files which are not processed
        <span
          class="t-font-bold t-text-[13px] t-text-[#ED7428] t-cursor-pointer"
          (click)="reprocessFiles()"
          >click here</span
        >
        to view and reprocess
      </p>
    </div>
    }
  </div>

  <!-- Status cards row: Upload, Processing, and Load -->
  <div class="t-flex t-flex-1 t-flex-row t-gap-5">
    @defer {
    <!-- Upload Card -->
    <ng-container
      *ngTemplateOutlet="
        statusCard;
        context: {
          card: uploadCard(),
          type: StatusCardType.UPLOAD
        }
      "></ng-container>

    <!-- Processing Card -->
    <ng-container
      *ngTemplateOutlet="
        statusCard;
        context: {
          card: processingCard(),
          type: StatusCardType.PROCESSING
        }
      "></ng-container>

    <!-- Load Card - Only show if there are active uploads -->
    @if (shouldShowLoadCard()) {
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-w-1/3 t-border t-border-[#E0E0E0] t-self-start t-border-1 t-rounded-[4px] t-w-[450px] t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">{{ StatusCardType.LOAD }}</p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        <!-- Uploaded By (Placeholder - derive if needed) -->
      </p>

      <!-- Progress bar section - Use the combined overallLoadProgress signal -->
      <div class="t-flex t-flex-row t-gap-2 t-items-end">
        <kendo-progressbar
          [animation]="true"
          [min]="0"
          [max]="100"
          [value]="overallLoadProgress().percentage"
          [progressCssStyle]="
            getProgressBarStyle(overallLoadProgress().percentage)
          "
          [label]="{ visible: false }"
          class="v-custom-progress-bar t-h-[9px] t-flex-1">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]">
          {{ overallLoadProgress().completed }}/{{
            overallLoadProgress().total
          }}
        </span>
      </div>

      <!-- Date/Last Updated (Placeholder - remove or adapt) -->

      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[20px] t-h-[20px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{
            't-rotate-180': !showLoadCardDetails(),
            't-ease-out': true
          }"
          (click)="toggleLoadCardDetails()">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>

      <!-- List of queued files -->
      <div
        class="t-flex t-flex-col t-overflow-hidden t-gap-2 t-transition-all t-duration-500"
        [style.max-height]="showLoadCardDetails() ? '500px' : '0px'"
        [style.opacity]="showLoadCardDetails() ? '1' : '0'">
        @for (queuedFile of currentQueuedUploads(); track queuedFile.fileId) {
        <div class="t-flex t-flex-col">
          <p
            class="t-text-[#9F9F9F] t-text-[12px] t-truncate"
            [title]="queuedFile.file.name">
            {{ queuedFile.file.name }}
          </p>
          <div class="t-flex t-flex-row t-gap-2 t-items-center">
            <kendo-progressbar
              [animation]="false"
              [min]="0"
              [max]="100"
              [value]="queuedFile.uploadProgress || 0"
              [progressCssStyle]="
                getProgressBarStyle(queuedFile.uploadProgress || 0)
              "
              [label]="{ visible: false }"
              class="v-custom-progress-bar t-h-[9px] t-flex-1">
            </kendo-progressbar>
            <span
              class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-w-[3em] t-text-right">
              {{ queuedFile.uploadProgress || 0 }}%
            </span>
            <span
              class="t-cursor-pointer t-relative t-flex t-items-center t-justify-center"
              (click)="cancelFile(queuedFile.fileId)">
              <kendo-svg-icon
                [icon]="closeIcon"
                class="t-text-[#ED7428] t-text-sm"></kendo-svg-icon>
            </span>
          </div>
        </div>
        } @empty {
        <p class="t-text-center t-text-gray-500 t-text-sm">
          No active uploads.
        </p>
        }
      </div>
    </div>
    } } @placeholder {
    <div class="t-p-5 t-text-gray-500">Loading status cards...</div>
    } @error {
    <div class="t-p-5 t-text-red-600">Failed to load status cards.</div>
    }
  </div>

  <!-- Processing Details Section -->
  <div class="t-my-4 t-flex t-items-center t-gap-4 t-flex-wrap">
    <h1 class="t-text-lg t-font-semibold">Processing Details</h1>
    <div
      class="t-flex t-items-center t-gap-4 t-text-xs t-font-medium t-flex-wrap">
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#9BD2A7]"></span> COMPLETED
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#FFBC3E]"></span> INPROGRESS
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#EDEBE9]"></span> NOT
        STARTED
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#ED7428]"></span> FAILED
      </div>
    </div>
  </div>

  <!-- Tasks Section -->
  <div class="t-flex t-flex-1 t-flex-row t-gap-5 t-flex-wrap">
    <!-- Completed Tasks -->
    @for (task of completedTasks(); track task.id) {
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p
        class="t-text-[#4A4B90DE] t-text-[16px] t-truncate"
        [title]="task.name">
        {{ task.name }}
      </p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name
        <span class="t-text-[#000000] t-font-medium">{{
          task.custodianName
        }}</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-items-end">
        <kendo-progressbar
          [animation]="false"
          [min]="0"
          [max]="100"
          [value]="
            getProgressPercentage(task.progress.completed, task.progress.total)
          "
          [progressCssStyle]="
            getProgressBarStyle(
              getProgressPercentage(
                task.progress.completed,
                task.progress.total
              )
            )
          "
          [label]="{ visible: false }"
          class="v-custom-progress-bar t-h-[9px] t-flex-1">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]">
          {{ task.progress.completed }}/{{ task.progress.total }}
        </span>
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">{{
            task.date | date : 'MM dd yyyy'
          }}</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">{{
            formatRelativeTime(task.lastUpdated)
          }}</span>
        </p>
      </div>
      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[20px] t-h-[20px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{ 't-rotate-180': !task.expanded, 't-ease-out': true }"
          (click)="toggleTaskExpanded(task.id)">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>
      <div
        class="t-flex t-flex-row t-flex-wrap t-gap-5 t-overflow-hidden t-transition-all t-duration-500"
        [style.max-height]="task.expanded ? '500px' : '0px'"
        [style.opacity]="task.expanded ? '1' : '0'">
        @if (task.expanded && task.processingStages &&
        task.processingStages.length > 0) {
        <div class="t-flex t-justify-around t-flex-wrap t-w-full">
          @for (stage of task.processingStages; track stage.type) {
          <div class="t-flex t-flex-col t-items-center t-mb-4">
            <kendo-circularprogressbar
              style="width: 80px; height: 80px"
              class="v-custom-progressbar"
              [animation]="true"
              [value]="stage.percentComplete"
              [progressColor]="getStageProgressColors(stage)">
              <ng-template
                kendoCircularProgressbarCenterTemplate
                let-value="value"
                ><span class="t-text-[#050505] t-text-[10px]"
                  >{{ value ?? 0 }}%</span
                ></ng-template
              >
            </kendo-circularprogressbar>
            <span class="t-text-xs t-mt-2 t-font-medium t-w-24 t-text-center">{{
              stage.type
            }}</span>
          </div>
          }
        </div>
        }
      </div>
    </div>
    }

    <!-- In Progress Tasks -->
    @for (task of inProgressTasks(); track task.id) {
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p
        class="t-text-[#4A4B90DE] t-text-[16px] t-truncate"
        [title]="task.name">
        {{ task.name }}
      </p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name
        <span class="t-text-[#000000] t-font-medium">{{
          task.custodianName
        }}</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-items-end">
        <kendo-progressbar
          [animation]="false"
          [min]="0"
          [max]="100"
          [value]="
            getProgressPercentage(task.progress.completed, task.progress.total)
          "
          [progressCssStyle]="
            getProgressBarStyle(
              getProgressPercentage(
                task.progress.completed,
                task.progress.total
              )
            )
          "
          [label]="{ visible: false }"
          class="v-custom-progress-bar t-h-[9px] t-flex-1"></kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >{{ task.progress.completed }} /{{ task.progress.total }}</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">{{
            task.date | date : 'MM dd yyyy'
          }}</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">{{
            formatRelativeTime(task.lastUpdated)
          }}</span>
        </p>
      </div>
      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[20px] t-h-[20px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{ 't-rotate-180': !task.expanded, 't-ease-out': true }"
          (click)="toggleTaskExpanded(task.id)">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>
      <div
        class="t-flex t-flex-row t-flex-wrap t-gap-5 t-overflow-hidden t-transition-all t-duration-500"
        [style.max-height]="task.expanded ? '500px' : '0px'"
        [style.opacity]="task.expanded ? '1' : '0'">
        @if (task.expanded && task.processingStages &&
        task.processingStages.length > 0) {
        <div class="t-flex t-justify-around t-flex-wrap t-w-full">
          @for (stage of task.processingStages; track stage.type) {
          <div class="t-flex t-flex-col t-items-center t-mb-4">
            <kendo-circularprogressbar
              style="width: 80px; height: 80px"
              class="v-custom-progressbar"
              [animation]="true"
              [value]="stage.percentComplete"
              [progressColor]="getStageProgressColors(stage)">
              <ng-template
                kendoCircularProgressbarCenterTemplate
                let-value="value"
                ><span class="t-text-[#050505] t-text-[10px]"
                  >{{ value ?? 0 }}%</span
                ></ng-template
              >
            </kendo-circularprogressbar>
            <span class="t-text-xs t-mt-2 t-font-medium t-w-24 t-text-center">{{
              stage.type
            }}</span>
          </div>
          }
        </div>
        }
      </div>
    </div>
    }

    <!-- Failed Tasks -->
    @for (task of failedTasks(); track task.id) {
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p
        class="t-text-[#4A4B90DE] t-text-[16px] t-truncate"
        [title]="task.name">
        {{ task.name }}
      </p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name
        <span class="t-text-[#000000] t-font-medium">{{
          task.custodianName
        }}</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-items-end">
        <kendo-progressbar
          [animation]="false"
          [min]="0"
          [max]="100"
          [value]="100"
          [progressCssStyle]="getFailedProgressBarStyle()"
          [label]="{ visible: false }"
          class="v-custom-progress-bar t-h-[9px] t-flex-1"></kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >{{ task.progress.completed }} /{{ task.progress.total }}</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">{{
            task.date | date : 'MM dd yyyy'
          }}</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">{{
            formatRelativeTime(task.lastUpdated)
          }}</span>
        </p>
      </div>
      @if (task.needsReprocessing) {
      <button
        (click)="reprocessFiles()"
        kendoButton
        size="small"
        class="v-custom-secondary-button !t-rounded-none t-self-start !t-h-[24px]"
        themeColor="secondary"
        fillMode="outline"
        data-qa="retry-button">
        <span class="t-text-[12px]">RETRY</span>
      </button>
      }
      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[20px] t-h-[20px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{ 't-rotate-180': !task.expanded, 't-ease-out': true }"
          (click)="toggleTaskExpanded(task.id)">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>
      <div
        class="t-flex t-flex-row t-flex-wrap t-gap-5 t-overflow-hidden t-transition-all t-duration-500"
        [style.max-height]="task.expanded ? '500px' : '0px'"
        [style.opacity]="task.expanded ? '1' : '0'">
        @if (task.expanded && task.processingStages &&
        task.processingStages.length > 0) {
        <div class="t-flex t-justify-around t-flex-wrap t-w-full">
          @for (stage of task.processingStages; track stage.type) {
          <div class="t-flex t-flex-col t-items-center t-mb-4">
            <kendo-circularprogressbar
              style="width: 80px; height: 80px"
              class="v-custom-progressbar"
              [animation]="true"
              [value]="stage.percentComplete"
              [progressColor]="getStageProgressColors(stage)">
              <ng-template
                kendoCircularProgressbarCenterTemplate
                let-value="value"
                ><span class="t-text-[#050505] t-text-[10px]"
                  >{{ value ?? 0 }}%</span
                ></ng-template
              >
            </kendo-circularprogressbar>
            <span class="t-text-xs t-mt-2 t-font-medium t-w-24 t-text-center">{{
              stage.type
            }}</span>
          </div>
          }
        </div>
        }
      </div>
    </div>
    }

    <!-- Not Started Tasks -->
    @for (task of notStartedTasks(); track task.id) {
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p
        class="t-text-[#4A4B90DE] t-text-[16px] t-truncate"
        [title]="task.name">
        {{ task.name }}
      </p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name
        <span class="t-text-[#000000] t-font-medium">{{
          task.custodianName
        }}</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-items-end">
        <kendo-progressbar
          [animation]="false"
          [min]="0"
          [max]="100"
          [value]="0"
          [progressCssStyle]="getProgressBarStyle(0)"
          [label]="{ visible: false }"
          class="v-custom-progress-bar t-h-[9px] t-flex-1"></kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >{{ task.progress.completed }} /{{ task.progress.total }}</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">{{
            task.date | date : 'MM dd yyyy'
          }}</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">{{
            formatRelativeTime(task.lastUpdated)
          }}</span>
        </p>
      </div>
      @if (task.canImportData) {
      <button
        (click)="openImportDataDialog(task.id, $event)"
        kendoButton
        size="small"
        class="v-custom-secondary-button !t-rounded-none t-self-start !t-h-[24px]"
        themeColor="secondary"
        fillMode="outline"
        data-qa="upload-button">
        <span class="t-text-[12px]">IMPORT DATA</span>
      </button>
      }
      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[20px] t-h-[20px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{ 't-rotate-180': !task.expanded, 't-ease-out': true }"
          (click)="toggleTaskExpanded(task.id)">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>
      <div
        class="t-flex t-flex-row t-flex-wrap t-gap-5 t-overflow-hidden t-transition-all t-duration-500"
        [style.max-height]="task.expanded ? '500px' : '0px'"
        [style.opacity]="task.expanded ? '1' : '0'">
        @if (task.expanded && task.processingStages &&
        task.processingStages.length > 0) {
        <div class="t-flex t-justify-around t-flex-wrap t-w-full">
          @for (stage of task.processingStages; track stage.type) {
          <div class="t-flex t-flex-col t-items-center t-mb-4">
            <kendo-circularprogressbar
              style="width: 80px; height: 80px"
              class="v-custom-progressbar"
              [animation]="true"
              [value]="stage.percentComplete"
              [progressColor]="getStageProgressColors(stage)">
              <ng-template
                kendoCircularProgressbarCenterTemplate
                let-value="value"
                ><span class="t-text-[#050505] t-text-[10px]"
                  >{{ value ?? 0 }}%</span
                ></ng-template
              >
            </kendo-circularprogressbar>
            <span class="t-text-xs t-mt-2 t-font-medium t-w-24 t-text-center">{{
              stage.type
            }}</span>
          </div>
          }
        </div>
        }
      </div>
    </div>
    } @empty {
    <p
      *ngIf="
        !completedTasks().length &&
        !inProgressTasks().length &&
        !failedTasks().length &&
        !notStartedTasks().length
      "
      class="t-text-center t-text-gray-500 t-col-span-full">
      No processing tasks found.
    </p>
    }
  </div>
</div>

<!-- Status Card Template -->
<ng-template #statusCard let-card="card" let-type="type">
  @if (card) {
  <div
    class="t-flex t-flex-col t-gap-2 t-p-5 t-w-1/3 t-border t-border-[#E0E0E0] t-self-start t-border-1 t-rounded-[4px] t-w-[450px] t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
    <p class="t-text-[#4A4B90DE] t-text-[16px]">{{ type }}</p>
    <p class="t-text-[#9F9F9F] t-text-[12px]">
      Uploaded By
      <span class="t-text-[#000000] t-font-medium">{{
        card.uploadedBy || 'Unknown User'
      }}</span>
    </p>
    @if (card.progress) {
    <div class="t-flex t-flex-row t-gap-2 t-items-end">
      <kendo-progressbar
        [animation]="false"
        [min]="0"
        [max]="100"
        [value]="
          getProgressPercentage(
            card.progress.completed || 0,
            card.progress.total || 0
          )
        "
        [progressCssStyle]="
          getProgressBarStyle(
            getProgressPercentage(
              card.progress.completed || 0,
              card.progress.total || 0
            )
          )
        "
        [label]="{ visible: false }"
        class="v-custom-progress-bar t-h-[9px] t-flex-1"></kendo-progressbar>
      <span
        class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
        >{{ card.progress.completed || 0 }} /{{
          card.progress.total || 0
        }}</span
      >
    </div>
    }
    <div class="t-flex t-flex-row t-justify-between">
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Date
        <span class="t-text-[#000000] t-font-medium">{{
          card.date | date : 'MM dd yyyy'
        }}</span>
      </p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Last Updated
        <span class="t-text-[#000000] t-font-medium">{{
          formatRelativeTime(card.lastUpdated)
        }}</span>
      </p>
    </div>
  </div>
  } @else {
  <div
    class="t-flex t-flex-col t-gap-2 t-p-5 t-w-1/3 t-border t-border-[#E0E0E0] t-self-start t-border-1 t-rounded-[4px] t-w-[450px] t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)] t-opacity-50">
    <p class="t-text-[#4A4B90DE] t-text-[16px]">{{ type }}</p>
    <p class="t-text-gray-400 t-text-sm">Loading...</p>
  </div>
  }
</ng-template>
