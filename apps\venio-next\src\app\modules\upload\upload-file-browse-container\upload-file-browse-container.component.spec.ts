import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadFileBrowseContainerComponent } from './upload-file-browse-container.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { UploadFacade } from '@venio/data-access/common'
import { UploadSourceSelection } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'

describe('UploadFileBrowseContainerComponent', () => {
  let component: UploadFileBrowseContainerComponent
  let fixture: ComponentFixture<UploadFileBrowseContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadFileBrowseContainerComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: UploadFacade,
          useValue: {
            updateActiveStepperIndex: jest.fn(),
            resetUploadState: jest.fn(),
            updateUploadSourceSelection: jest.fn(),
            selectStepperActiveIndex$: of(0),
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
          } satisfies Partial<UploadFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadFileBrowseContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
