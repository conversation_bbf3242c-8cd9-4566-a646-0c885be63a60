import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadSelectedFileMetaInfoComponent } from './upload-selected-file-meta-info.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, signal } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { UploadManagerService, UploadFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  SelectedFileMetaInfo,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'

describe('UploadSourceSelectorItemsComponent', () => {
  let component: UploadSelectedFileMetaInfoComponent
  let fixture: ComponentFixture<UploadSelectedFileMetaInfoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadSelectedFileMetaInfoComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClientTesting(),
        provideHttpClient(),
        provideMockStore({}),
        {
          provide: UploadFacade,
          useValue: {
            updateActiveStepperIndex: jest.fn(),
            resetUploadState: jest.fn(),
            updateUploadSourceSelection: jest.fn(),
            selectStepperActiveIndex$: of(0),
            selectCustomCustodian$: of([]),
            selectCustodians$: of([]),
            selectSelectedFileMetaInfo$: of([] as SelectedFileMetaInfo[]),
            selectUploadSourceSelection$: of({} as UploadSourceSelection),
          } satisfies Partial<UploadFacade>,
        },
        {
          provide: UploadManagerService,
          useValue: {
            uploadMultipleFiles: jest.fn(),
            selectUploadSourceSelection: signal({} as UploadSourceSelection),
          } satisfies Partial<UploadManagerService>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadSelectedFileMetaInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
