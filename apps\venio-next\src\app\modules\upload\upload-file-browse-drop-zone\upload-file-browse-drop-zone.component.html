<div #container class="t-flex t-flex-row t-h-full t-flex-1 t-justify-between">
  <div
    class="v-custom-upload-container t-relative t-mt-3 t-flex t-flex-1 t-h-2/5 t-mr-[20px] t-relative"
    (dragenter)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)">
    <!-- Hidden Kendo Upload Component -->
    <kendo-upload
      [autoUpload]="false"
      [restrictions]="restrictions()"
      [chunkable]="false"
      zoneId="customDropZone"
      [multiple]="true"
      (select)="handleFileSelect($event)"
      [showFileList]="false"
      [concurrent]="false"
      class="t-hidden" />

    <!-- File Selection Trigger -->
    <kendo-fileselect
      class="t-hidden"
      [showFileList]="false"
      [accept]="restrictions().allowedExtensions.join(',')"
      [restrictions]="restrictions()"
      (select)="handleFileSelect($event)"
      zoneId="customDropZone"
      [multiple]="true" />

    <!-- Custom Drop Zone with proper directive and dynamic styling -->
    <div
      kendoUploadDropZone="customDropZone"
      [ngClass]="
        isDragOver()
          ? 't-border-[#1DBADC] v-border-animate'
          : 't-border-[#1DBADC]'
      "
      class="v-upload-area t-rounded t-flex t-flex-col t-border-2 t-border-dashed t-p-3 t-min-h-[214px] t-flex-1 t-items-center t-justify-center t-text-center t-relative t-transition-all t-duration-300 t-ease-in-out">
      <!-- Drop here message - visible only when dragging -->
      @if(isDragOver()){
      <div
        class="t-transition-opacity t-bg-white t-duration-300 t-ease-in-out t-absolute t-inset-0 t-flex t-items-center t-justify-center t-absolute t-top-0 t-left-0 t-right-0 t-bottom-0">
        <p class="t-text-[20px] t-font-bold t-text-[#1DBADC]">
          Drop here to add files
        </p>
      </div>
      }

      <!-- Additional instructions for click file selection -->
      <p class="t-text-[14px] t-font-normal t-text-[#999999] t-mt-[60px]">
        Drag & Drop files or Click
        <span
          class="t-font-bold t-text-[#1EBADC] t-cursor-pointer"
          (click)="triggerFileSelect()">
          here
        </span>
        to upload the file
      </p>

      <!-- Warning Message -->
      <p
        kendoTooltip
        title="Click to view supported file types"
        [showAfter]="1000"
        (click)="openFileTypeInfo()"
        class="t-flex t-flex-row t-text-[#FEB43C] t-font-normal t-text-[12px] t-mt-3 t-cursor-pointer">
        <span
          venioSvgLoader
          width="0.8rem"
          svgUrl="assets/svg/icon-warning.svg"
          class="t-mx-1"></span>
        Only container files are supported!
      </p>

      <!-- File List Display -->
      <div class="t-p-[10px] t-flex t-items-center t-w-full t-mt-5">
        <div
          class="t-flex t-flex-wrap t-justify-center t-gap-5 t-items-center t-w-full">
          <div
            *ngFor="let info of uploadManagerService.formattedFileInfo()"
            class="t-flex t-flex-col">
            <div class="t-flex t-flex-row t-gap-1 t-items-center">
              <div class="t-flex t-flex-col t-flex-1">
                <div class="t-flex t-flex-row t-gap-1 t-flex-1">
                  <span
                    venioSvgLoader
                    height="16px"
                    width="19px"
                    svgUrl="assets/svg/icon-zip-file.svg"></span>
                  <p
                    kendoTooltip
                    [showAfter]="600"
                    [title]="info.name.trim().length > 20 ? info.name : ''"
                    class="t-text-[12px] t-text-[#263238] t-font-bold t-truncate">
                    {{ getTruncatedFileName(info.name) }}
                  </p>
                </div>
                <p
                  class="t-text-[10px] t-w-full t-justify-center t-items-center t-mt-0.1 t-flex t-text-[#A9A9A9] t-font-medium">
                  {{ info.size }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    class="t-flex t-flex-col t-gap-5 t-bg-[#9BD2A70D] t-p-3 t-w-[304px] t-h-full t-overflow-hidden">
    <p class="t-text-[#263238] t-text-[14px] t-font-medium">
      Selected Files & Folders
    </p>
    <div class="t-flex t-flex-col t-justify-between t-flex-1 t-relative">
      <div
        [ngStyle]="{ 'max-height.px': container?.clientHeight - 120 }"
        class="t-flex t-flex-col t-max-h-[75%] t-overflow-y-auto t-p-[10px] t-gap-5 flex-1">
        @for(fileMetaInfo of selectSelectedFileMetaInfo(); track
        fileMetaInfo.fileId + fileMetaInfo.custodianName) {
        <venio-upload-selected-file-meta-info
          [id]="fileMetaInfo.fileId"
          [fileMetaInfo]="fileMetaInfo"
          (removeSelectedFile)="removeSelectedFile($event)" />
        }
      </div>
      <button
        (click)="uploadManagerService.startFileUpload()"
        kendoButton
        class="v-custom-secondary-button !t-rounded-none t-self-end"
        themeColor="secondary"
        fillMode="outline"
        data-qa="upload-button">
        PROCESS
      </button>
    </div>
  </div>
</div>
