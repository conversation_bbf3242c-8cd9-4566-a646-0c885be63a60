<div
  class="t-flex t-flex-row t-flex-1 t-justify-between t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
  <div class="t-flex t-flex-1 t-flex-row t-gap-4">
    <p
      class="t-font-medium t-text-[16px] t-text-[#000000DE] t-self-center t-capitalize">
      {{ selectedSourceText() }}
    </p>

    <kendo-dropdownlist
      [data]="mediaSourceItems()"
      textField="label"
      valueField="value"
      title="Select Document"
      class="t-w-48"
      (selectionChange)="mediaTypeSelectionChange($event)"
      [value]="selectedMediaTypeValue()"
      [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
      [valuePrimitive]="true">
      <ng-template kendoDropDownListHeaderTemplate>
        <div
          class="t-border-b-[#979797] t-border-b-[2px] t-border-dashed t-w-full t-pb-2">
          Source
        </div>
      </ng-template>
    </kendo-dropdownlist>

    @if (isBatchMediaType()) {
    <div class="t-flex t-flex-row t-gap-2 t-self-center">
      <p class="t-text-[12px] t-text-[#999999]">Download sample csv</p>
      <button
        kendoButton
        class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px] t-cursor-pointer t-self-center"
        rounded="full"
        fillMode="clear"
        title="Delete">
        <span
          venioSvgLoader
          class="t-w-[15px] t-h-[18px] t-rounded-full t-bg-[#ffffff]"
          svgUrl="assets/svg/icon-material-round-sim-card-download.svg"></span>
      </button>
    </div>
    }
  </div>
  @if (isBatchMediaType()) {
  <p class="t-text-[10px] t-text-[#232323] t-self-center">
    <span class="t-font-bold t-text-[#ED7428]">NOTE: </span>Click on grid cell
    to edit custodian name, Media, Source
  </p>
  }
</div>
