import {
  computed,
  effect,
  inject,
  Injectable,
  Injector,
  OnDestroy,
  signal,
  untracked,
} from '@angular/core'
import {
  HttpClient,
  HttpHeaders,
  HttpEventType,
  HttpRequest,
  HttpEvent,
} from '@angular/common/http'
import {
  BehaviorSubject,
  EMPTY,
  filter,
  map,
  merge,
  Observable,
  retry,
  Subject,
  takeUntil,
  timer,
} from 'rxjs'
import {
  ChunkMetadata,
  ChunkStatus,
  ChunkUpdateResult,
  FileMetadata,
  MediaSourceType,
  SelectedFileMetaInfo,
  UploadMediaTypes,
  UploadOptions,
  UploadProgress,
  UploadState,
  WorkerStats,
  WorkerInitResult,
  ResponseModel,
  UploadSourceTypes,
  UploadLocalStorageKeys,
} from '@venio/shared/models/interfaces'
import { UploadFacade, UserFacade } from '../+state'
import { toSignal } from '@angular/core/rxjs-interop'
import { FileInfo } from '@progress/kendo-angular-upload'
import { environment } from '@venio/shared/environments'
import { UuidGenerator } from '@venio/util/uuid'
import {
  FileTypeCategory,
  FileValidationService,
} from './upload-file-validation.service'
import { LocalStorage } from '@venio/shared/storage'

/**
 * Represents an upload task with all necessary metadata and state
 */
export interface UploadTask {
  /** Unique identifier for the task */
  taskId: string
  /** Current state of the upload */
  state: UploadState
  /** File being uploaded */
  file: File
  /** Configuration options for the upload */
  options: UploadOptions
  /** Server-assigned session ID (primary) */
  sessionId: string | null
  /** All server-assigned session IDs */
  sessionIds: string[]
  /** Full response data from registration */
  responseData: any
  sessionStatuses?: any
  /** Subject for emitting progress updates */
  progressSubject: BehaviorSubject<UploadProgress>
  /** Interval reference for status checking */
  statusCheckInterval: ReturnType<typeof setInterval> | null
  /** Metadata about the file being uploaded */
  fileMetadata: FileMetadata
  /** Array of chunks for the file */
  chunks: ChunkMetadata[]
  /** Size of each chunk in bytes */
  chunkSize: number
  /** Number of concurrent uploads */
  concurrency: number
  /** Number of retry attempts */
  retryCount: number
  /** Delay between retries in ms */
  retryDelay: number
  /** Maximum number of retries */
  maxRetries?: number
  /** Maximum delay between retries */
  maxRetryDelay?: number
  /** Response data from the last chunk upload */
  chunkUploadResponse?: any
  /** Timeout ID for retry mechanism */
  retryTimeoutId?: number
}

/**
 * Represents a file managed by the upload service
 */
export interface ManagedFile {
  /** Unique identifier for the file */
  fileId: string
  /** The file itself */
  file: File

  /** Represents how much data has been transferred to the server */
  uploadProgress?: number
}

/** Minimum chunk size in bytes (4MB) */
const MIN_CHUNK_SIZE = 4 * 1024 * 1024
/** Maximum chunk size in bytes (8MB, constrained by server limit of 16MB) */
const MAX_CHUNK_SIZE = 8 * 1024 * 1024
/** Maximum number of concurrent chunk uploads */
const MAX_CONCURRENCY = 2
/** Maximum retry delay in milliseconds (2 minutes) */
const MAX_RETRY_DELAY = 2 * 60 * 1000
/** Default retry count */
const DEFAULT_RETRY_COUNT = 5
/** Default retry delay in milliseconds (5 seconds) */
const DEFAULT_RETRY_DELAY = 5000

/**
 * Service for handling file uploads with chunking and progress tracking.
 *
 * @remarks
 * DO NOT use this service at the root level for injection.
 * Instead, provide it within the component level in the provider array
 * to create a scoped instance of the service.
 *
 * This service implements chunked file uploads with retry logic, progress tracking,
 * and dynamic chunk size calculation based on file size.
 */
@Injectable()
export class UploadManagerService implements OnDestroy {
  /**
   * Subject used to trigger unsubscription from observables when the service is destroyed
   * @private
   */
  private readonly toDestroy$ = new Subject<void>()

  /**
   * Angular injector for accessing dependencies
   * @private
   */
  private readonly injector = inject(Injector)

  /**
   * Facade for upload state management
   * @private
   */
  private readonly uploadFacade = inject(UploadFacade)

  /**
   * Service for validating files before upload.
   *
   * @private
   */
  private readonly fileValidationService = inject(FileValidationService)

  /**
   * Facade for user state management
   * @private
   */
  private readonly userFacade = inject(UserFacade)

  /**
   * HTTP client for making upload requests
   * @private
   */
  private readonly httpClient = inject(HttpClient)

  public mediaSourceType = toSignal(this.uploadFacade.selectMediaSourceType())

  /**
   * Signal containing the selected upload source
   * @private
   */
  public readonly selectUploadSourceSelection = toSignal(
    this.uploadFacade.selectUploadSourceSelection$.pipe(
      filter((source) => Boolean(source))
    )
  )

  /**
   * Signal containing the selected project information
   * @private
   */
  private readonly selectedProjectInfo = toSignal(
    this.uploadFacade.selectSelectedCaseInfo$.pipe(
      filter((selectedCaseInfo) => Boolean(selectedCaseInfo))
    )
  )

  /**
   * Signal containing the current user information
   * @private
   */
  private readonly currentUserInfo = toSignal(
    this.userFacade.selectCurrentUserDetails$.pipe(
      filter((userInfo) => Boolean(userInfo))
    )
  )

  /**
   * Signal containing metadata for selected files
   */
  public readonly selectSelectedFileMetaInfo = toSignal(
    this.uploadFacade.selectSelectedFileMetaInfo$.pipe(
      map((info) => info || [])
    ),
    { initialValue: [] }
  )

  /**
   * Map of selected ID files indexed by fileId
   * The key is the fileId of selected .nsf file and the value is .id file associated with .nsf file
   * @example
   * const nsfFile = new FIle() // indicates user selected file with .nsf extension
   * this.selectedIdFiles.set(nsfFile.fileId, idFile) // indicates user selected .id file for the .nsf file
   */
  public readonly selectedIdFiles = new Map<string, File>()

  /**
   * Map of active upload tasks indexed by taskId
   * @private
   */
  private tasks = new Map<string, UploadTask>()

  /**
   * Signal containing the currently selected files for upload
   */
  public readonly selectedFiles = signal<ManagedFile[]>([])

  /**
   * Signal containing the total number of selected files
   */
  public readonly totalSelectedFileCount = signal(0)

  /**
   * Signal containing the total number of queued uploaded files which is increments upon each queued file uploads.
   */
  public readonly totalQueuedUploadedFileCount = signal(0)

  /**
   * Computed signal that formats file information for display
   */
  public readonly formattedFileInfo = computed(() => {
    const files = this.selectedFiles()
    return files.map((f) => ({
      size: this.formatBytes(f.file.size),
      name: f.file.name,
      fileId: f.fileId,
    }))
  })

  /**
   * Cleans up resources when the service is destroyed
   * @returns {void}
   */
  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()

    // Cancel all active tasks and clean up
    const taskIds = Array.from(this.tasks.keys())
    taskIds.forEach((taskId) => {
      this.cancelUpload(taskId)
    })
    this.tasks.clear()
  }

  /**
   * Adds new files to the upload queue, avoiding duplicates
   *
   * @param {FileInfo[] | null} newFilesList - List of files to add
   * @returns {void}
   */
  public addFiles(newFilesList: FileInfo[] | null): void {
    if (!newFilesList || newFilesList.length === 0) {
      return
    }

    const currentManagedFiles: ManagedFile[] = this.selectedFiles()
    const existingFileDuplicateCheckIds = new Set<string>(
      currentManagedFiles.map((mf) => this.generateDuplicateCheckId(mf.file))
    )

    const newManagedFilesToAdd: ManagedFile[] = newFilesList
      .filter((newFile) => {
        if (!newFile.rawFile) return false
        const duplicateCheckId = this.generateDuplicateCheckId(newFile.rawFile)
        if (!existingFileDuplicateCheckIds.has(duplicateCheckId)) {
          existingFileDuplicateCheckIds.add(duplicateCheckId)
          return true
        }
        return false
      })
      .map(
        (uniqueNewFile): ManagedFile => ({
          fileId: uniqueNewFile.uid,
          file: uniqueNewFile.rawFile,
        })
      )

    if (newManagedFilesToAdd.length > 0) {
      this.selectedFiles.update((current) => [
        ...current,
        ...newManagedFilesToAdd,
      ])

      const metaInfo = newManagedFilesToAdd.map(
        (m) =>
          ({
            fileId: m.fileId,
            name: m.file.name,
            custodianName: '',
            mediaName: '',
            password: '',
            idFileName: '',
          } as SelectedFileMetaInfo)
      )
      this.uploadFacade.storeSelectedFileMetaInfo(metaInfo)
      this.totalSelectedFileCount.set(this.selectedFiles().length)
    }
  }

  /**
   * Generates a unique identifier for a file to check for duplicates
   *
   * @param {File} file - The file to generate an ID for
   * @returns {string} A unique identifier based on name, size, and modification date
   * @private
   */
  private generateDuplicateCheckId(file: File): string {
    return `${file.name}-${file.size}-${file.lastModified}`
  }

  /**
   * Clears all selected files
   * @returns {void}
   */
  public clearFiles(): void {
    this.selectedFiles.set([])
  }

  /**
   * Removes a file from the selection by its ID
   *
   * @param {string} fileId - The ID of the file to remove
   * @returns {void}
   */
  public removeFileById(fileId: string): void {
    this.selectedFiles.update((current) =>
      current.filter((mf) => mf.fileId !== fileId)
    )
    this.uploadFacade.removeSelectedFileMetaInfo(fileId)
  }

  /**
   * Calculates the optimal chunk size based on file size
   *
   * @param {number} fileSize - The size of the file in bytes
   * @returns {number} The calculated chunk size in bytes
   * @private
   *
   * @remarks
   * Uses a heuristic approach to determine an ideal chunk size between
   * MIN_CHUNK_SIZE and MAX_CHUNK_SIZE, targeting between 100-1000 chunks
   * for optimal upload performance.
   */
  private calculateDynamicChunkSize(fileSize: number): number {
    if (fileSize <= 0) return MIN_CHUNK_SIZE

    // Target between 100-1000 chunks for optimal performance
    const targetChunks = Math.max(
      100,
      Math.min(1000, fileSize / (50 * 1024 * 1024))
    )

    let idealChunkSize = Math.ceil(fileSize / targetChunks)

    // Ensure chunk size is within bounds
    idealChunkSize = Math.max(MIN_CHUNK_SIZE, idealChunkSize)
    idealChunkSize = Math.min(MAX_CHUNK_SIZE, idealChunkSize)

    return idealChunkSize
  }

  /**
   * Calculates the optimal concurrency level for uploads
   *
   * @returns {number} The number of concurrent uploads to use
   * @private
   */
  private calculateDynamicConcurrency(): number {
    // Currently returns a fixed value but could be enhanced to adjust
    // based on network conditions or file characteristics
    return MAX_CONCURRENCY
  }

  /**
   * Validates a file against the provided upload options
   *
   * @param {FileMetadata} fileMetadata - Metadata for the file to validate
   * @param {UploadOptions} options - Upload options including supported MIME types
   * @returns {WorkerInitResult} Validation result with file information
   * @private
   */
  private validateFile(
    fileMetadata: FileMetadata,
    options: UploadOptions
  ): WorkerInitResult {
    const supportedMimeTypes = options.supportedMimeTypes || []

    // If no MIME types are specified, allow all files
    if (supportedMimeTypes.length === 0) {
      return {
        valid: true,
        chunks: 0,
        totalSize: fileMetadata.size,
        fileId: options.fileId,
      }
    }

    // Validate file type
    const isValid = this.isFiletypeSupported(fileMetadata, supportedMimeTypes)

    if (!isValid) {
      return {
        valid: false,
        message: `File type not supported. Supported types: ${supportedMimeTypes.join(
          ', '
        )}`,
        chunks: 0,
        totalSize: fileMetadata.size,
        fileId: options.fileId,
      }
    }

    // Calculate number of chunks
    const chunkSize = options.chunkSize || MIN_CHUNK_SIZE
    const totalChunks = Math.ceil(fileMetadata.size / chunkSize)

    return {
      valid: true,
      chunks: totalChunks,
      totalSize: fileMetadata.size,
      fileId: options.fileId,
    }
  }

  /**
   * Checks if a file's type is supported based on MIME type or extension
   *
   * @param {FileMetadata} fileMetadata - The file metadata to check
   * @param {string[]} supportedMimeTypes - List of supported MIME types or extensions
   * @returns {boolean} Whether the file type is supported
   * @private
   */
  private isFiletypeSupported(
    fileMetadata: FileMetadata,
    supportedMimeTypes: string[]
  ): boolean {
    // Check direct MIME type match
    if (supportedMimeTypes.includes(fileMetadata.type)) {
      return true
    }

    // Check extension and wildcard matches
    const fileExtension = this.getFileExtension(fileMetadata.name).toLowerCase()

    return supportedMimeTypes.some((supportedType) => {
      // Extension match (e.g., ".pdf")
      if (supportedType.startsWith('.')) {
        return fileExtension === supportedType.substring(1).toLowerCase()
      }

      // Wildcard match (e.g., "image/*")
      if (supportedType.endsWith('/*')) {
        const typeCategory = supportedType.split('/')[0]
        return fileMetadata.type.startsWith(`${typeCategory}/`)
      }

      return false
    })
  }

  /**
   * Divides a file into chunks for upload
   *
   * @param {UploadTask} task - The upload task to calculate chunks for
   * @private
   * @returns {void}
   */
  private calculateChunks(task: UploadTask): void {
    const totalChunks = Math.ceil(task.fileMetadata.size / task.chunkSize)
    task.chunks = []

    for (let i = 0; i < totalChunks; i++) {
      const start = i * task.chunkSize
      const end = Math.min(task.fileMetadata.size, start + task.chunkSize)

      task.chunks.push({
        index: i,
        start,
        end,
        attempts: 0,
        status: ChunkStatus.Pending,
      })
    }
  }

  /**
   * Gets the next batch of chunks to upload for a task
   *
   * @param {string} taskId - The ID of the task to get chunks for
   * @returns {ChunkMetadata[]} Array of chunks ready for upload
   * @private
   */
  private getNextChunksForTask(taskId: string): ChunkMetadata[] {
    const task = this.tasks.get(taskId)
    if (!task || !task.chunks) return []

    // Count chunks currently being processed
    const processingCount = task.chunks.filter(
      (c) => c.status === ChunkStatus.Processing
    ).length

    // Calculate how many more chunks we can process concurrently
    const availableSlots = task.concurrency - processingCount
    if (availableSlots <= 0) {
      return []
    }

    // Find pending chunks that haven't exceeded retry limits
    const chunksToProcess = task.chunks
      .filter(
        (chunk) =>
          chunk.status === ChunkStatus.Pending &&
          chunk.attempts < task.retryCount
      )
      .slice(0, availableSlots)

    // Mark selected chunks as processing
    chunksToProcess.forEach((chunk) => {
      const taskChunk = task.chunks.find((c) => c.index === chunk.index)
      if (taskChunk) {
        taskChunk.status = ChunkStatus.Processing
      }
    })

    return chunksToProcess.map((chunk) => ({
      ...chunk,
      status: ChunkStatus.Processing,
    }))
  }

  /**
   * Updates the status of a chunk after an upload attempt
   *
   * @param {string} taskId - The ID of the task containing the chunk
   * @param {number} index - The index of the chunk to update
   * @param {boolean} success - Whether the chunk upload was successful
   * @returns {ChunkUpdateResult} Result of the update including progress information
   * @private
   */
  private updateChunkStatusForTask(
    taskId: string,
    index: number,
    success: boolean
  ): ChunkUpdateResult {
    const task = this.tasks.get(taskId)
    if (!task || !task.chunks) {
      return { completed: false, progress: 0, retriesExhausted: false }
    }

    const chunkIndex = task.chunks.findIndex((c) => c.index === index)
    if (chunkIndex === -1) {
      return {
        completed: false,
        progress: this.calculateProgressForTask(task),
        retriesExhausted: false,
      }
    }

    const chunk = task.chunks[chunkIndex]

    if (success) {
      // Success case - mark as completed
      chunk.status = ChunkStatus.Completed
      chunk.attempts = 0
    } else {
      // Failure case - increment attempts or mark as failed
      chunk.attempts++

      if (chunk.attempts >= task.retryCount) {
        // Exceed max retries - mark as failed
        chunk.status = ChunkStatus.Failed
        const progress = this.calculateProgressForTask(task)
        this.updateProgress(taskId, progress, task.state)
        return { completed: false, progress: progress, retriesExhausted: true }
      }

      // Calculate backoff with upper bound
      const backoffTime = Math.min(
        task.retryDelay * Math.pow(2, chunk.attempts - 1),
        MAX_RETRY_DELAY
      )

      chunk.status = ChunkStatus.Pending
      const progress = this.calculateProgressForTask(task)
      this.updateProgress(taskId, progress, task.state)

      return {
        completed: false,
        progress: progress,
        retriesExhausted: false,
        retryAfter: backoffTime,
      }
    }

    // Calculate new progress
    const progress = this.calculateProgressForTask(task)
    this.updateProgress(taskId, progress, task.state)

    // Check if all chunks are completed
    const isCompleted = task.chunks.every(
      (c) => c.status === ChunkStatus.Completed
    )

    return {
      completed: isCompleted,
      progress: progress,
      retriesExhausted: false,
    }
  }

  /**
   * Calculates the overall progress percentage for an upload task
   *
   * @param {UploadTask} task - The task to calculate progress for
   * @returns {number} Progress percentage from 0-100
   * @private
   */
  private calculateProgressForTask(task: UploadTask): number {
    if (
      !task.fileMetadata ||
      !task.chunks ||
      task.chunks.length === 0 ||
      task.fileMetadata.size === 0
    ) {
      return 0
    }

    // Sum up the bytes in completed chunks
    const completedBytes = task.chunks
      .filter((c) => c.status === ChunkStatus.Completed)
      .reduce((sum, chunk) => sum + (chunk.end - chunk.start), 0)

    return Math.min(
      100,
      Math.floor((completedBytes / task.fileMetadata.size) * 100)
    )
  }

  /**
   * Retrieves statistics about an upload task
   *
   * @param {string} taskId - The ID of the task to get statistics for
   * @returns {WorkerStats} Statistics including counts of chunks in different states
   * @private
   */
  private getTaskStats(taskId: string): WorkerStats {
    const task = this.tasks.get(taskId)
    if (!task || !task.chunks) {
      return {
        totalChunks: 0,
        completedChunks: 0,
        failedChunks: 0,
        pendingChunks: 0,
        processingChunks: 0,
        progress: 0,
      }
    }

    const totalChunks = task.chunks.length
    const completedChunks = task.chunks.filter(
      (c) => c.status === ChunkStatus.Completed
    ).length
    const failedChunks = task.chunks.filter(
      (c) => c.status === ChunkStatus.Failed
    ).length
    const pendingChunks = task.chunks.filter(
      (c) => c.status === ChunkStatus.Pending
    ).length
    const processingChunks = task.chunks.filter(
      (c) => c.status === ChunkStatus.Processing
    ).length

    return {
      totalChunks,
      completedChunks,
      failedChunks,
      pendingChunks,
      processingChunks,
      progress: this.calculateProgressForTask(task),
    }
  }

  /**
   * Resets failed chunks so they can be retried
   *
   * @param {string} taskId - The ID of the task to reset chunks for
   * @returns {number} The number of chunks that were reset
   * @private
   */
  private resetFailedChunksForTask(taskId: string): number {
    const task = this.tasks.get(taskId)
    if (!task || !task.chunks) return 0

    let resetCount = 0

    task.chunks.forEach((chunk) => {
      if (chunk.status === ChunkStatus.Failed) {
        chunk.status = ChunkStatus.Pending
        chunk.attempts = 0
        resetCount++
      }
    })

    // If any chunks were reset and task was in error state, restart upload
    if (resetCount > 0 && task.state === UploadState.Error) {
      task.state = UploadState.Uploading
      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Uploading
      )

      this.processUpload(taskId).catch((err: unknown) => {
        console.error(`[${taskId}] Error restarting upload after reset:`, err)
      })
    }

    return resetCount
  }

  /**
   * Starts a new file upload process
   *
   * @param {UploadOptions} options - Configuration options for the upload
   * @returns {Observable<UploadProgress>} Observable that emits upload progress updates
   */
  public startUpload(options: UploadOptions): Observable<UploadProgress> {
    const taskId = UuidGenerator.uuid

    // Create initial progress object
    const initialProgress: UploadProgress = {
      taskId,
      progress: 0,
      loaded: 0,
      total: options.file.size,
      status: UploadState.Initializing,
    }
    const progressSubject = new BehaviorSubject<UploadProgress>(initialProgress)

    // Initialize uploadProgress in the ManagedFile
    this.updateManagedFileProgress(options.fileId, 0)

    try {
      // Calculate optimal parameters
      const calculatedChunkSize =
        options.chunkSize ?? this.calculateDynamicChunkSize(options.file.size)
      const calculatedConcurrency =
        options.concurrency ?? this.calculateDynamicConcurrency()
      const retryCount = options.retryCount ?? DEFAULT_RETRY_COUNT
      const retryDelay = options.retryDelay ?? DEFAULT_RETRY_DELAY

      // Create enhanced options with defaults
      const enhancedOptions: UploadOptions = {
        ...options,
        chunkSize: calculatedChunkSize,
        concurrency: calculatedConcurrency,
        retryCount: retryCount,
        retryDelay: retryDelay,
        fileId:
          options.fileId ||
          `venio_${Date.now()}_${Math.random().toString(16).substring(2)}`,
        headers: {
          ...options.headers,
        },
        mediaSourceType: options.mediaSourceType || MediaSourceType.SOURCE_FILE,
      }

      // Create file metadata
      const fileMetadata: FileMetadata = {
        name: options.file.name,
        size: options.file.size,
        type: options.file.type,
        lastModified: options.file.lastModified,
      }

      // Validate file
      const validationResult = this.validateFile(fileMetadata, enhancedOptions)
      if (!validationResult.valid) {
        const errorMessage =
          validationResult.message || 'Invalid file or configuration'
        console.error(`[${taskId}] Validation failed:`, errorMessage)

        progressSubject.error(new Error(errorMessage))
        return progressSubject.asObservable()
      }

      // Create upload task
      const newTask: UploadTask = {
        taskId,
        state: UploadState.Initializing,
        file: options.file,
        options: enhancedOptions,
        sessionId: null,
        progressSubject,
        statusCheckInterval: null,
        fileMetadata: fileMetadata,
        chunks: [],
        chunkSize: enhancedOptions.chunkSize,
        concurrency: enhancedOptions.concurrency,
        retryCount: enhancedOptions.retryCount,
        retryDelay: enhancedOptions.retryDelay,
        maxRetries: options.maxRetries || 3,
        maxRetryDelay: options.maxRetryDelay || 30000,
        sessionIds: null,
        responseData: null,
      }

      // Calculate chunks
      this.calculateChunks(newTask)

      // Store task
      this.tasks.set(taskId, newTask)

      // Begin processing
      this.processUpload(taskId).catch((error: unknown) => {
        console.error(
          `[${taskId}] Upload failed during initialization or processing:`,
          error
        )

        const task = this.tasks.get(taskId)
        if (task && task.state !== UploadState.Canceled) {
          task.state = UploadState.Error

          if (!task.progressSubject.closed) {
            this.updateProgress(
              taskId,
              this.calculateProgressForTask(task),
              UploadState.Error
            )
            task.progressSubject.error(error)
          }
        }
      })
    } catch (error: unknown) {
      console.error(`[${taskId}] Error during upload setup:`, error)

      if (!progressSubject.closed) {
        this.updateProgress(taskId, 0, UploadState.Error)
        progressSubject.error(error)
      }
    }

    return progressSubject.asObservable()
  }

  /**
   * Processes an upload task through its lifecycle
   *
   * @param {string} taskId - The ID of the task to process
   * @returns {Promise<void>} Promise that resolves when processing is complete
   * @private
   */
  private async processUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task) {
      throw new Error(`[${taskId}] Task not found for processing`)
    }

    // Skip if task is in a terminal state
    if (
      task.state === UploadState.Completed ||
      task.state === UploadState.Error ||
      task.state === UploadState.Canceled
    ) {
      console.warn(
        `[${taskId}] processUpload called in terminal state: ${task.state}`
      )
      return
    }

    try {
      // First register the upload if needed
      if (!task.sessionId && task.options.uploadRegisterEndpoint) {
        task.state = UploadState.Initializing
        this.updateProgress(taskId, 0, UploadState.Initializing)

        await this.registerUpload(taskId)

        // Check if task state changed during registration
        const taskAfterRegister = this.tasks.get(taskId)
        if (
          !taskAfterRegister ||
          taskAfterRegister.state === UploadState.Error ||
          taskAfterRegister.state === UploadState.Canceled
        ) {
          return
        }
      }

      // Begin uploading chunks
      task.state = UploadState.Uploading
      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Uploading
      )

      await this.processChunks(taskId)

      // Mark as completed if still in uploading state
      const finalTask = this.tasks.get(taskId)
      const stats = this.getTaskStats(taskId)
      const { totalChunks, completedChunks } = stats

      // Track how many files have been uploaded successfully
      if (totalChunks === completedChunks) {
        this.totalQueuedUploadedFileCount.update((count) => count + 1)
      }

      if (finalTask?.state === UploadState.Uploading) {
        finalTask.state = UploadState.Completed
        this.updateProgress(taskId, 100, UploadState.Completed)

        if (!finalTask.progressSubject.closed) {
          finalTask.progressSubject.complete()
        }
      }
    } catch (error: unknown) {
      console.error(`[${taskId}] Error during upload processing:`, error)

      const task = this.tasks.get(taskId)
      if (!task || task.state === UploadState.Canceled) {
        return
      }

      task.state = UploadState.Error
      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Error
      )

      // Check if all chunks are either completed or failed (none pending or processing)
      const stats = this.getTaskStats(taskId)
      const allChunksProcessed =
        stats.pendingChunks === 0 && stats.processingChunks === 0

      if (allChunksProcessed && stats.failedChunks > 0) {
        // Start automatic retry process
        this.retryFailedUpload(taskId, 0).catch((e: unknown) =>
          console.error(`[${taskId}] Failed to initiate automatic retry:`, e)
        )
      } else if (!task.progressSubject.closed) {
        task.progressSubject.error(error)
      }
    }
  }

  /**
   * Processes chunks for an upload task
   *
   * @param {string} taskId - The ID of the task to process chunks for
   * @returns {Promise<void>} Promise that resolves when all chunks are processed
   * @private
   */
  private async processChunks(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task || task.state !== UploadState.Uploading) {
      console.warn(
        `[${taskId}] processChunks started in non-uploading state: ${task?.state}`
      )
      return
    }

    let processing = true

    while (processing) {
      // Check if task state has changed
      const currentTask = this.tasks.get(taskId)
      if (!currentTask || currentTask.state !== UploadState.Uploading) {
        processing = false
        break
      }

      // Get next batch of chunks to process
      const chunksToProcess = this.getNextChunksForTask(taskId)

      if (chunksToProcess.length === 0) {
        // No chunks to process - check upload status
        const stats = this.getTaskStats(taskId)

        if (stats.completedChunks === stats.totalChunks) {
          // All chunks completed - start processing
          processing = false
          this.startProcessing(taskId)
          break
        } else if (
          stats.failedChunks > 0 &&
          stats.pendingChunks === 0 &&
          stats.processingChunks === 0
        ) {
          // Some chunks failed, none pending or processing
          throw new Error(
            `[${taskId}] Upload failed: ${stats.failedChunks} chunks failed after retries`
          )
        } else if (stats.processingChunks > 0 || stats.pendingChunks > 0) {
          // Wait for in-progress chunks to complete
          await new Promise((resolve) => setTimeout(resolve, 200))
          continue
        } else {
          // Unexpected state
          processing = false
          break
        }
      }

      // Process chunks in parallel
      const chunkPromises = chunksToProcess.map(async (chunk) => {
        try {
          // Check task state before processing chunk
          const taskBeforeUpload = this.tasks.get(taskId)
          if (
            !taskBeforeUpload ||
            taskBeforeUpload.state !== UploadState.Uploading
          ) {
            return
          }

          // Extract chunk data and upload
          const chunkBlob = task.file.slice(chunk.start, chunk.end)
          await this.uploadChunk(
            taskId,
            chunk.index,
            chunkBlob,
            task.chunks.length
          )

          // Update chunk status
          this.updateChunkStatusForTask(taskId, chunk.index, true)
        } catch (error: unknown) {
          console.error(`[${taskId}] Chunk ${chunk.index} failed:`, error)

          // Handle failure
          const updateResult = this.updateChunkStatusForTask(
            taskId,
            chunk.index,
            false
          )

          if (updateResult.retriesExhausted) {
            console.error(
              `[${taskId}] Retries exhausted for chunk ${chunk.index}`
            )
          } else if (updateResult.retryAfter) {
            await new Promise((resolve) =>
              setTimeout(resolve, updateResult.retryAfter)
            )
          }
        }
      })

      // Wait for all chunks in this batch to complete
      await Promise.all(chunkPromises)
    }
  }

  /**
   * Registers an upload with the server to obtain a session ID
   *
   * @param {string} taskId - The ID of the task to register
   * @returns {Promise<void>} Promise that resolves when registration is complete
   * @private
   */
  private registerUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task) {
      return Promise.reject(
        new Error(`[${taskId}] Task not found for registration`)
      )
    }

    if (!task.options.uploadRegisterEndpoint || !task.options.projectId) {
      return Promise.reject(
        new Error(`[${taskId}] Missing uploadRegisterEndpoint or projectId`)
      )
    }

    if (task.sessionId) return Promise.resolve()

    // Prepare query parameters
    const queryParams = new URLSearchParams({
      isTranscript: String(task.options.isTranscript ?? false),
      isStructured: String(task.options.isStructured ?? false),
    })

    // Construct URL
    const url = `${task.options.uploadRegisterEndpoint}/${
      task.options.projectId
    }?${queryParams.toString()}`

    // Get all selected files metadata
    const allSelectedFilesMeta = this.selectSelectedFileMetaInfo()

    // Create FileInfoList with all selected files
    const fileInfoList = allSelectedFilesMeta
      .map((metaInfo) => {
        // Find the corresponding managed file to get the raw File object
        const managedFile = this.selectedFiles().find(
          (mf) => mf.fileId === metaInfo.fileId
        )
        if (!managedFile) return null

        return {
          FileId: metaInfo.fileId,
          FileName: metaInfo.name,
          FileSizeFormatted: managedFile.file
            ? this.formatBytes(managedFile.file.size)
            : '0 B',
          FileSize: managedFile.file ? managedFile.file.size : 0,
          CustodianName:
            metaInfo.custodianName ||
            task.options.custodianName ||
            task.file.name.slice(0, task.file.name.lastIndexOf('.')),
          IsStructured: task.options.isStructured ?? false,
          mediaSourceType: task.options.mediaSourceType,
          FullName: metaInfo.name,
          Extension: managedFile.file
            ? this.getFileExtension(managedFile.file.name)
            : '',
          Name: metaInfo.name,
          IsForensicImageMultipart:
            task.options.isForensicImageMultipart ?? false ? 'Y' : 'N',
          MediaName: metaInfo.mediaName || '',
          Password: metaInfo.password || '',
        }
      })
      .filter(Boolean)

    // Prepare payload and form data
    const payload = { FileInfoList: fileInfoList }
    const formData = new FormData()
    formData.append('QueuedFilesModel', JSON.stringify(payload))

    // append id files if it exists associated with fileId which are same for all selected files.
    if (this.selectedIdFiles.size > 0) {
      this.selectedIdFiles.forEach((idFile, fileId) => {
        formData.append(fileId, idFile)
      })
    }

    return new Promise<void>((resolve, reject) => {
      this.httpClient
        .post<any[]>(url, formData)
        .pipe(takeUntil(this.toDestroy$))
        .subscribe({
          next: (response) => {
            // Validate response
            if (
              !response ||
              !Array.isArray(response) ||
              response.length === 0
            ) {
              reject(new Error('Invalid response format from server'))
              return
            }

            // Store all session IDs and response data
            task.sessionIds = response
              .map((session) => session.SessionId)
              .filter(Boolean)
            task.sessionId = task.sessionIds[0] || null
            task.responseData = response
            // Store the session ID in local storage
            // It will be accessed from status service as well as within this service
            // Once the ongoing jobs after upload are done, we can easily reset it
            LocalStorage.set(
              UploadLocalStorageKeys.UploadRegisteredSessionId,
              task.sessionId
            )

            if (task.sessionIds.length === 0) {
              reject(new Error('No valid SessionId found in server response'))
              return
            }

            resolve()
          },
          error: (error: unknown) => {
            console.error(`[${taskId}] Registration error:`, error)
            reject(error)
          },
        })
    })
  }

  /**
   * Uploads a chunk of a file
   *
   * @param {string} taskId - The ID of the task
   * @param {number} chunkIndex - The index of the chunk
   * @param {Blob} chunkBlob - The chunk data
   * @param {number} totalChunks - Total number of chunks
   * @returns {Promise<any>} Promise that resolves with the server response
   * @private
   */
  private uploadChunk(
    taskId: string,
    chunkIndex: number,
    chunkBlob: Blob,
    totalChunks: number
  ): Promise<any> {
    const task = this.tasks.get(taskId)
    if (!task) {
      return Promise.reject(
        new Error(`[${taskId}] Task not found for chunk upload`)
      )
    }

    if (!task.options.uploadEndpoint) {
      return Promise.reject(new Error(`[${taskId}] Missing uploadEndpoint`))
    }

    // Check if service is being destroyed
    if (this.toDestroy$.closed) {
      return Promise.reject(new Error(`[${taskId}] Service is being destroyed`))
    }

    // Prepare form data
    const formData = new FormData()
    formData.append('name', task.file.name)
    formData.append('fileId', task.options.fileId)
    formData.append('chunk', chunkIndex.toString())
    formData.append('chunks', totalChunks.toString())
    formData.append('file', chunkBlob, `${task.file.name}.chunk${chunkIndex}`)

    // Create request
    const req = new HttpRequest('POST', task.options.uploadEndpoint, formData, {
      headers: this.createHeaders(task.options),
    })

    return new Promise<any>((resolve, reject) => {
      // Create a local subject to handle this specific request's cancellation
      const localDestroy$ = new Subject<void>()

      // Merge the service-level destroy with the local one
      const destroy$ = merge(this.toDestroy$, localDestroy$)

      this.httpClient
        .request(req)
        .pipe(
          retry({
            count: task.retryCount,
            delay: (error, retryCount) => {
              // Check if service is being destroyed before scheduling retry
              if (this.toDestroy$.closed) {
                return EMPTY
              }

              const delay = task.retryDelay * Math.pow(2, retryCount - 1)
              console.log(
                `[${taskId}] Chunk ${chunkIndex} retry ${retryCount} in ${delay}ms`
              )
              return timer(delay).pipe(takeUntil(destroy$))
            },
          }),
          takeUntil(destroy$)
        )
        .subscribe({
          next: (event: HttpEvent<ResponseModel>) => {
            if (event.type === HttpEventType.Response) {
              // Store the response data for the last chunk
              if (chunkIndex === totalChunks - 1) {
                task.chunkUploadResponse = event.body.data
              }
              resolve(event.body.data)
              localDestroy$.next()
              localDestroy$.complete()
            }
          },
          error: (error: unknown) => {
            console.error(
              `[${taskId}] Chunk ${chunkIndex} upload failed after retries:`,
              error
            )
            reject(error)
            localDestroy$.next()
            localDestroy$.complete()
          },
          complete: () => {
            localDestroy$.next()
            localDestroy$.complete()
          },
        })
    })
  }

  /**
   * Creates HTTP headers for upload requests
   *
   * @param {UploadOptions} options - Upload options containing header information
   * @returns {HttpHeaders} The created headers
   * @private
   */
  private createHeaders(options: UploadOptions): HttpHeaders {
    let headers = new HttpHeaders()

    // Add authorization if provided
    if (options.authToken) {
      headers = headers.set('Authorization', `Bearer ${options.authToken}`)
    }

    // Add custom headers if provided
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          headers = headers.set(key, value)
        }
      })
    }

    return headers
  }

  /**
   * Updates the progress information for an upload task
   *
   * @param {string} taskId - The ID of the task to update progress for
   * @param {number} progress - The progress percentage (0-100)
   * @param {UploadState} state - The current state of the upload
   * @param {string} [serverStatus] - Optional status reported by the server
   * @private
   * @returns {void}
   */
  private updateProgress(
    taskId: string,
    progress: number,
    state: UploadState,
    serverStatus?: string
  ): void {
    const task = this.tasks.get(taskId)
    if (!task || task.progressSubject.closed) return

    // Calculate loaded bytes
    const loaded = Math.floor(task.file.size * (progress / 100))
    const currentSubjectValue = task.progressSubject.getValue()

    // Use server status if provided, otherwise use state
    const reportStatus = serverStatus ?? state ?? currentSubjectValue.status

    // Emit progress update
    task.progressSubject.next({
      taskId,
      progress: Math.min(100, progress),
      loaded,
      total: task.file.size,
      sessionId: task.sessionId,
      status: reportStatus,
    })

    // Update the uploadProgress in the corresponding ManagedFile
    this.updateManagedFileProgress(task.options.fileId, progress)
  }

  /**
   * Updates the uploadProgress property of a ManagedFile
   *
   * @param {string} fileId - The ID of the file to update
   * @param {number} progress - The progress percentage (0-100)
   * @private
   */
  private updateManagedFileProgress(fileId: string, progress: number): void {
    this.selectedFiles.update((files) =>
      files.map((file) =>
        file.fileId === fileId ? { ...file, uploadProgress: progress } : file
      )
    )
  }

  /**
   * Formats a byte count into a human-readable string
   *
   * @param {number} bytes - The number of bytes
   * @param {number} decimals - The number of decimal places to include
   * @returns {string} Formatted string (e.g., "1.5 MB")
   * @private
   */
  private formatBytes(bytes: number, decimals = 2): string {
    if (bytes <= 0) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return (
      parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) +
      ' ' +
      sizes[Math.min(i, sizes.length - 1)]
    )
  }

  /**
   * Extracts the file extension from a filename
   *
   * @param {string} filename - The filename to extract the extension from
   * @returns {string} The file extension (without the dot) or empty string if none
   * @private
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex === -1 ? '' : filename.substring(lastDotIndex + 1)
  }

  /**
   * Starts uploads for all selected files
   * @returns {void}
   */
  public startFileUpload(): void {
    const metadataList = this.selectSelectedFileMetaInfo()
    const projectInfo = this.selectedProjectInfo()
    const { sourceType } = this.selectUploadSourceSelection()

    if (!projectInfo || !projectInfo.projectId) {
      console.error('Cannot start upload: Project info or ID missing.')
      return
    }

    if (!metadataList || metadataList.length === 0) {
      console.warn(
        'Cannot start upload: No files selected or metadata missing.'
      )
      return
    }

    const { projectId } = projectInfo
    const currentUser = this.currentUserInfo()

    // Prepare base options shared by all uploads
    const baseOptions: Omit<
      UploadOptions,
      'file' | 'fileId' | 'custodianName' | 'mediaName' | 'password'
    > = {
      uploadEndpoint: `${environment.apiUrl}upload/project/${projectId}/chunk-upload`,
      uploadRegisterEndpoint: `${environment.apiUrl}upload/project`,
      initializeEndpoint: '',
      statusEndpoint: `${environment.apiUrl}process`,
      processEndpoint: `${environment.apiUrl}process`,
      projectId: projectId,
      mediaSourceType: this.mediaSourceType() || MediaSourceType.SOURCE_FILE,
      retryCount: DEFAULT_RETRY_COUNT,
      retryDelay: DEFAULT_RETRY_DELAY,
      externalUserId: currentUser?.userId,
      isStructured: sourceType === UploadSourceTypes.STRUCTURED,
      isTranscript: sourceType === UploadSourceTypes.TRANSCRIPT,
    }

    // Start multiple file upload
    this.uploadMultipleFiles(metadataList, baseOptions).subscribe({
      next: () => {
        const statusStepIndex = 2
        this.uploadFacade.updateActiveStepperIndex(statusStepIndex)
      },
      error: (err: unknown) => {
        console.error('Error during multiple file upload initiation:', err)
      },
      complete: () => {
        console.log('All file uploads initiated.')
      },
    })
  }

  /**
   * Pauses an active upload
   *
   * @param {string} taskId - The ID of the task to pause
   * @returns {Promise<void>} Promise that resolves when the upload is paused
   */
  public pauseUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)

    if (task && task.state === UploadState.Uploading) {
      task.state = UploadState.Paused

      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Paused
      )
    }

    return Promise.resolve()
  }

  /**
   * Resumes a paused upload
   *
   * @param {string} taskId - The ID of the task to resume
   * @returns {Promise<void>} Promise that resolves when the upload is resumed
   */
  public resumeUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)

    if (task && task.state === UploadState.Paused) {
      task.state = UploadState.Uploading

      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Uploading
      )

      this.processUpload(taskId).catch((err: unknown) => {
        console.error(`[${taskId}] Error resuming upload:`, err)
      })
    }

    return Promise.resolve()
  }

  /**
   * Cancels an upload
   *
   * @param {string} taskId - The ID of the task to cancel
   * @returns {Promise<void>} Promise that resolves when the upload is canceled
   */
  public cancelUpload(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task) return Promise.resolve()

    // Mark as canceled
    task.state = UploadState.Canceled

    // Complete progress subject if not already closed
    if (!task.progressSubject.closed) {
      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Canceled
      )
      task.progressSubject.complete()
    }

    // Remove from task map
    this.tasks.delete(taskId)

    return Promise.resolve()
  }

  /**
   * Removes an upload from the service
   *
   * @param {string} taskId - The ID of the task to remove
   * @returns {Promise<void>} Promise that resolves when the upload is removed
   */
  public removeUpload(taskId: string): Promise<void> {
    return this.cancelUpload(taskId)
  }

  /**
   * Uploads multiple files with shared base options
   *
   * @param {SelectedFileMetaInfo[]} fileMetadataList - Metadata for files to upload
   * @param {Omit<UploadOptions, 'file' | 'fileId' | 'custodianName' | 'mediaName' | 'password'>} baseOptions - Shared upload options
   * @returns {Observable<UploadProgress[]>} Observable that emits progress updates for all files
   */
  public uploadMultipleFiles(
    fileMetadataList: SelectedFileMetaInfo[],
    baseOptions: Omit<
      UploadOptions,
      'file' | 'fileId' | 'custodianName' | 'mediaName' | 'password'
    >
  ): Observable<UploadProgress[]> {
    const aggregateSubject = new BehaviorSubject<UploadProgress[]>([])
    const progressMap = new Map<string, UploadProgress>()
    let completedOrErroredCount = 0
    const totalFiles = fileMetadataList.length

    // Create map of file IDs to file objects
    const fileMap = new Map<string, File>()
    this.selectedFiles().forEach(({ fileId, file }) =>
      fileMap.set(fileId, file)
    )

    // Helper function to update aggregate progress
    const updateAggregateProgress = (): void => {
      aggregateSubject.next(Array.from(progressMap.values()))
    }

    // Helper function to check if all uploads are completed
    const checkCompletion = (): void => {
      if (completedOrErroredCount === totalFiles && !aggregateSubject.closed) {
        aggregateSubject.complete()
      }
    }

    // Start uploads sequentially
    const startSequentially = async (): Promise<void> => {
      for (const metadata of fileMetadataList) {
        const file = fileMap.get(metadata.fileId)

        if (!file) {
          console.warn(
            `[${metadata.fileId}] File not found for metadata, skipping.`
          )

          // Create error progress
          progressMap.set(metadata.fileId, {
            taskId: metadata.fileId,
            progress: 0,
            loaded: 0,
            total: 0,
            status: UploadState.Error,
          })

          updateAggregateProgress()
          completedOrErroredCount++
          continue
        }

        // Create options for this specific file
        const options: UploadOptions = {
          ...baseOptions,
          file: file,
          fileId: metadata.fileId,
          custodianName:
            metadata.custodianName ||
            file.name.slice(0, file.name.lastIndexOf('.')),
          mediaName: metadata.mediaName,
          password: metadata.password || '',
          mediaSourceType: baseOptions.mediaSourceType,
          isTranscript: baseOptions.isTranscript,
          isForensicImageMultipart: baseOptions.isForensicImageMultipart,
          isStructured: baseOptions.isStructured,
        }

        // Wait for this upload to initialize before starting the next
        await new Promise<void>((resolve) => {
          const subscription = this.startUpload(options)
            .pipe(takeUntil(this.toDestroy$))
            .subscribe({
              next: (progress: UploadProgress) => {
                if (progress?.taskId) {
                  progressMap.set(progress.taskId, progress)
                  updateAggregateProgress()
                }
              },
              error: (error: unknown) => {
                console.error(`[${metadata.fileId}] Upload error:`, error)

                // Create error progress
                const errorProgress: UploadProgress = {
                  taskId: options.fileId,
                  progress: progressMap.get(options.fileId)?.progress ?? 0,
                  loaded: progressMap.get(options.fileId)?.loaded ?? 0,
                  total: file.size,
                  status: UploadState.Error,
                }

                progressMap.set(options.fileId, errorProgress)
                updateAggregateProgress()
                completedOrErroredCount++
                resolve()
                checkCompletion()
              },
              complete: () => {
                // Update final progress
                const finalProgress = progressMap.get(options.fileId)

                if (
                  finalProgress &&
                  finalProgress.status !== UploadState.Error
                ) {
                  finalProgress.progress = 100
                  finalProgress.loaded = finalProgress.total
                  finalProgress.status = UploadState.Completed
                  progressMap.set(options.fileId, finalProgress)
                  updateAggregateProgress()
                }

                completedOrErroredCount++
                resolve()
                checkCompletion()
              },
            })

          // Ensure the subscription is added to toDestroy$ to be cleaned up
          this.toDestroy$.subscribe(() => {
            if (subscription && !subscription.closed) {
              subscription.unsubscribe()
            }
          })
        })
      }

      checkCompletion()
    }

    // Start sequential uploads and handle errors
    startSequentially().catch((error: unknown) => {
      console.error('Error starting sequential uploads:', error)

      if (!aggregateSubject.closed) {
        aggregateSubject.error(error)
      }
    })

    return aggregateSubject.asObservable()
  }

  /**
   * Sets up an effect to clear files when the upload source changes
   * @returns {void}
   */
  public clearFileWhenSourceChange(): void {
    effect(
      () => {
        const sourceSelection = this.selectUploadSourceSelection()

        if (
          sourceSelection?.mediaSourceType?.value !==
          UploadMediaTypes.LOCAL_UPLOAD
        ) {
          untracked(() => {
            this.clearFiles()
            this.uploadFacade.resetUploadState('selectedFileMetaInfo')
          })
        }
      },
      { injector: this.injector }
    )
  }

  /**
   * Retries failed chunks in an upload and restarts the upload process
   *
   * @param {string} taskId - The ID of the task to retry
   * @param {number} retryAttempt - Current retry attempt number
   * @returns {Promise<number>} Promise that resolves with the number of chunks reset
   * @private - This is an internal method used for automatic retries
   */
  private retryFailedUpload(taskId: string, retryAttempt = 0): Promise<number> {
    // Check if service is being destroyed
    if (this.toDestroy$.closed) {
      return Promise.resolve(0)
    }

    const task = this.tasks.get(taskId)
    if (!task) {
      return Promise.resolve(0)
    }

    // Reset all failed chunks
    const resetCount = this.resetFailedChunksForTask(taskId)

    // Only restart upload if chunks were actually reset and task is in error state
    if (resetCount > 0 && task.state === UploadState.Error) {
      console.log(
        `[${taskId}] Automatic retry attempt ${
          retryAttempt + 1
        }: Resetting ${resetCount} failed chunks`
      )

      // Update task state
      task.state = UploadState.Uploading

      // Update progress to show we're retrying
      this.updateProgress(
        taskId,
        this.calculateProgressForTask(task),
        UploadState.Uploading
      )

      // Restart the upload process
      this.processUpload(taskId).catch((error: unknown) => {
        // Check if service is being destroyed
        if (this.toDestroy$.closed) {
          return
        }

        console.error(
          `[${taskId}] Error during automatic retry attempt ${
            retryAttempt + 1
          }:`,
          error
        )

        // Check if we should attempt another retry
        if (retryAttempt < task.maxRetries) {
          const nextRetryAttempt = retryAttempt + 1
          const backoffTime = Math.min(
            task.retryDelay * Math.pow(2, retryAttempt),
            task.maxRetryDelay
          )

          console.log(
            `[${taskId}] Scheduling next automatic retry in ${backoffTime}ms`
          )

          // Store the timeout ID so we can clear it if needed
          const timeoutId = setTimeout(() => {
            // Check again if service is being destroyed before executing retry
            if (!this.toDestroy$.closed) {
              this.retryFailedUpload(taskId, nextRetryAttempt).catch(
                (e: unknown) =>
                  console.error(
                    `[${taskId}] Failed to execute automatic retry:`,
                    e
                  )
              )
            }
          }, backoffTime)

          // Store the timeout ID in the task for cleanup
          task.retryTimeoutId = timeoutId as unknown as number
        } else {
          console.error(
            `[${taskId}] Maximum retry attempts (${task.maxRetries}) reached, giving up`
          )

          // Update final error state
          task.state = UploadState.Error
          this.updateProgress(
            taskId,
            this.calculateProgressForTask(task),
            UploadState.Error
          )

          if (!task.progressSubject.closed) {
            task.progressSubject.error(
              new Error(`Upload failed after ${task.maxRetries} retry attempts`)
            )
          }
        }
      })
    }

    return Promise.resolve(resetCount)
  }

  /**
   * Starts processing of uploaded files
   *
   * @param {string} taskId - The ID of the completed upload task
   * @private
   * @returns {void}
   */
  private startProcessing(taskId: string): void {
    const task = this.tasks.get(taskId)
    if (!task || !task.options.processEndpoint) {
      console.warn(
        `[${taskId}] Cannot start processing: missing data or endpoint`
      )
      return
    }

    // Update task state to indicate processing
    task.state = UploadState.Processing
    this.updateProgress(taskId, 100, UploadState.Processing)

    // Get the upload response data from the last chunk upload
    const uploadResponse = task.chunkUploadResponse || {}

    // Construct process URL
    const url = `${task.options.processEndpoint}/project/${
      task.options.projectId
    }?sessionId=${
      task.sessionId
    }&externalUserId=-1&isTranscript=false&mediaSourceType=${
      this.mediaSourceType() || MediaSourceType.SOURCE_FILE
    }&isOverLay=false&isRepository=false&isMSTeam=false&isRepositoryUpload=false&isAwsS3Upload=false`
    const idFile = this.selectedIdFiles.get(task.options.fileId)
    const fileCategory = this.fileValidationService.getFileTypeCategory(
      task.file
    )
    const isForensicImageMultipart =
      fileCategory === FileTypeCategory.Forensic ? 'Y' : 'N'

    // Prepare payload for processing using the chunk upload response
    const payload = {
      uploadId: uploadResponse.uploadId,
      fullName: uploadResponse.fullName,
      fileName: task.file.name,
      fileSize: this.formatBytes(task.file.size),
      fileSizeInBytes: task.file.size,
      extension: this.getFileExtension(task.file.name),
      fileId: task.options.fileId,
      custodianName:
        task.options.custodianName ||
        task.file.name.slice(0, task.file.name.lastIndexOf('.')),
      nsfUserIdFileName: idFile?.name || '',
      // nsfUserIdFile: idFile || null,
      awsS3ConfiguartionId: uploadResponse.AwsS3ConfiguartionId || null,
      mediaSourceType: MediaSourceType.SOURCE_FILE,
      isForensicImageMultipart,
    }

    this.httpClient
      .post<any>(url, {
        uploadedFileModel: [payload],
        // Values should be populated if the social medai is MS team
        msTeamModel: null,
        // List of MS team hierarchies
        msTeamHierarchies: [],
      })
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: () => {
          // Clean up file info from arrays if needed
          // TODO: we may need sessionId to monitor the distributed job status
          // which will be used for status UI to show different job statuses.
          // We might clear the task after job is 100% done.
          // We may need to store the session ID somewhere.
          this.cleanupFileInfo(task.options.fileId)

          // Update task state
          task.state = UploadState.Processed
          this.updateProgress(taskId, 100, UploadState.Processed)

          // Complete the progress subject
          if (!task.progressSubject.closed) {
            task.progressSubject.complete()
          }
        },
        error: (error: unknown) => {
          console.error(`[${taskId}] Error starting processing:`, error)
          task.state = UploadState.ProcessingFailed
          this.updateProgress(taskId, 100, UploadState.ProcessingFailed)

          // Notify of error
          if (!task.progressSubject.closed) {
            task.progressSubject.error(error)
          }
        },
      })
  }

  /**
   * Cleans up file information after processing
   *
   * @param {string} fileId - The ID of the file to clean up
   * @private
   * @returns {void}
   */
  private cleanupFileInfo(fileId: string): void {
    // Remove file from selected files if present
    this.selectedFiles.update((files) =>
      files.filter((file) => file.fileId !== fileId)
    )

    if (this.selectedIdFiles.has(fileId)) {
      this.selectedIdFiles.delete(fileId)
    }

    // Notify the facade to clean up as well
    this.uploadFacade.removeSelectedFileMetaInfo(fileId)
  }

  /**
   * Checks if there are any active upload tasks in progress
   *
   * @returns {boolean} True if there are active uploads, false otherwise
   */
  public hasActiveUploads(): boolean {
    const hasAnyFiles = this.selectedFiles().length > 0
    if (hasAnyFiles) return true

    // If there are no tasks, return false
    if (!this.tasks || this.tasks.size === 0) {
      return false
    }

    // Check if any task is in a non-completed state (uploading, processing, paused, etc.)
    return (
      hasAnyFiles ||
      Array.from(this.tasks.values()).some(
        (task) =>
          task.state === UploadState.Initializing ||
          task.state === UploadState.Uploading ||
          task.state === UploadState.Paused ||
          task.state === UploadState.Processing
      )
    )
  }

  /**
   * Cancels all active uploads
   *
   * @returns {Promise<void>} Promise that resolves when all uploads are canceled
   */
  public cancelAllUploads(): Promise<void> {
    const taskIds = Array.from(this.tasks.keys())
    const cancelPromises = taskIds.map((taskId) => this.cancelUpload(taskId))
    this.selectedIdFiles.clear()

    return Promise.all(cancelPromises)
      .then(() => {
        console.warn('All uploads canceled successfully')
      })
      .catch((error: unknown) => {
        console.error('Error canceling uploads:', error)
      })
  }
}
