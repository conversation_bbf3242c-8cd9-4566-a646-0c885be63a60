import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { UploadFileFolderInfoItemComponent } from '../upload-file-folder-info-item.component'
import { UploadFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { VenioNotificationService } from '@venio/feature/notification'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-upload-file-folder-info',
  standalone: true,
  imports: [CommonModule, UploadFileFolderInfoItemComponent, KENDO_BUTTONS],
  templateUrl: './upload-file-folder-info.component.html',
  styleUrl: './upload-file-folder-info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadFileFolderInfoComponent {
  public uploadFacade = inject(UploadFacade)

  private notificationService = inject(VenioNotificationService)

  public selectedFileFolderForUpload = toSignal(
    this.uploadFacade.selectFileFolderForUpload$,
    { initialValue: [] }
  )

  public validationMessage = toSignal(this.uploadFacade.validationMessage$)

  private caseInfo = toSignal(this.uploadFacade.selectSelectedCaseInfo$)

  private processFromRepositorySuccess = toSignal(
    this.uploadFacade.selectProcessFromRepositorySuccessResponse$
  )

  private processFromRepositoryError = toSignal(
    this.uploadFacade.selectProcessFromRepositoryErrorResponse$
  )

  constructor() {
    this.selectFileFoldersEffect()
    this.processFromRepositorySuccessEffect()
    this.processFromRepositoryErrorEffect()
  }

  public processAddedFolders(): void {
    const isValid = this.validateBeforeSumbmit()
    if (isValid) {
      this.uploadFacade.processFromRepository(
        this.caseInfo().projectId,
        this.selectedFileFolderForUpload(),
        []
      )
    }
  }

  public selectFileFoldersEffect(): void {
    effect(
      () => {
        const fileFolders = this.selectedFileFolderForUpload()
        for (const fileFolder of fileFolders) {
          if (!fileFolder.repositoryHierarchies.length)
            this.uploadFacade.removeQueuedMedia(fileFolder.id)
        }
      },
      { allowSignalWrites: true }
    )
  }

  public processFromRepositorySuccessEffect(): void {
    effect(
      () => {
        const successReponse = this.processFromRepositorySuccess()
        if (successReponse) {
          this.notificationService.showSuccess(
            'Selected file/folders queued for processing successfully'
          )
          this.uploadFacade.clearFileFolder()
        }
      },
      { allowSignalWrites: true }
    )
  }

  public processFromRepositoryErrorEffect(): void {
    effect(
      () => {
        const errorResponse = this.processFromRepositoryError()
        if (errorResponse)
          this.notificationService.showError(
            'Failed to queue selected files/folders for processing'
          )
      },
      { allowSignalWrites: true }
    )
  }

  private validateBeforeSumbmit(): boolean {
    const validationMessage = {}
    let isValid = true
    if (!this.selectedFileFolderForUpload().length) {
      this.notificationService.showError('Select file or folder to process')
      isValid = false
    }

    /*
    * Add this validation if required later.

    this.selectedFileFolderForUpload().forEach((element) => {
      if (!validationMessage[element.id]) {
        validationMessage[element.id] = {}
      }
      if (!element.custodianName) {
        validationMessage[element.id].custodianName =
          preValidationMessage.custodianName.required
        isValid = false
      }
      if (!element.mediaName) {
        validationMessage[element.id].mediaName =
          preValidationMessage.mediaName.required
        isValid = false
      }
    })
      */
    this.uploadFacade.validationMessage$.next(validationMessage)
    return isValid
  }
}
