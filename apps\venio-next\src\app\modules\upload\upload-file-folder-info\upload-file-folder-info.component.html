<div
  class="t-flex t-flex-col t-gap-5 t-bg-[#9BD2A70D] t-p-3 t-w-[304px] t-h-[78vh] t-overflow-auto t-justify-between">
  <div class="t-flex t-flex-col t-flex-start t-overflow-hidden">
    <p class="t-text-[#263238] t-text-[14px] t-font-medium">
      Selected Files & Folders
    </p>
    <div class="t-overflow-auto">
      @for(fileFolder of selectedFileFolderForUpload(); track fileFolder.id){
      <venio-upload-file-folder-info-item
        [fileFolder]="fileFolder"></venio-upload-file-folder-info-item>
      }
    </div>
  </div>

  <button
    kendoButton
    class="v-custom-secondary-button !t-rounded-none t-self-end"
    themeColor="secondary"
    fillMode="outline"
    (click)="processAddedFolders()"
    data-qa="upload-button">
    PROCESS
  </button>
</div>
