import { createAction, props } from '@ngrx/store'

import { UploadState } from './upload.reducer'
import {
  CaseDetailModel,
  NsfUserIdFile,
  QueuedRepositoryModel,
  ResponseModel,
  SelectedFileMetaInfo,
  UploadSourceSelection,
} from '@venio/shared/models/interfaces'

export enum UploadActionTypes {
  // Resetting Users State
  ResetUploadState = '[Upload] Reset State',

  // Actions for storing/updating upload source selection
  UpdateUploadSourceSelection = '[Upload] Store Upload Source Selection',

  // Actions to update stepper index
  UpdateActiveStepperIndex = '[Upload] Update Active Stepper Index',

  StoreSelectedCaseInfo = '[Upload] Update Selected Case Info',

  AddFileFolder = '[Upload] Add File Folder',

  UpdateFileFolder = '[Upload] Update File Folder',

  UpdateFileFolderInBulk = '[Upload] Update File Folder In Bulk',

  RemoveFileFolder = '[Upload] Remove File Folder',

  RemoveQueuedMedia = '[Upload] Remove Queued Media',

  ClearFileFolder = '[Upload] Clear File Folder',

  ProcessFromRepository = '[Upload] Process From Repository',

  ProcessFromRepositorySuccess = '[Upload] Process From Repository Success',

  ProcessFromRepositoryFailure = '[Upload] Process From Repository Failure',

  FetchCustodians = '[Upload] Fetch Custodians',

  FetchCustodiansSuccess = '[Upload] Fetch Custodians Success',

  FetchCustodiansFailure = '[Upload] Fetch Custodians Failure',
  AddCustomCustodian = '[Upload] Add Custom Custodian',

  StoreCustomCustodian = '[Upload] Store Custom Custodian',
  StoreSelectedFileMetaInfo = '[Upload] Store Selected File Meta Info',
  UpdateSelectedFileMetaInfo = '[Upload] Update Selected File Meta Info',
  RemoveSelectedFileMetaInfo = '[Upload] Remove Selected File Meta Info',
}

export const resetUploadState = createAction(
  UploadActionTypes.ResetUploadState,
  props<{
    stateKey: keyof UploadState | Array<keyof UploadState>
  }>()
)

export const updateUploadSourceSelection = createAction(
  UploadActionTypes.UpdateUploadSourceSelection,
  props<{
    uploadSourceSelection: Partial<UploadSourceSelection>
  }>()
)

export const updateActiveStepperIndex = createAction(
  UploadActionTypes.UpdateActiveStepperIndex,
  props<{
    activeStepperIndex: number
  }>()
)

export const StoreSelectedCaseInfo = createAction(
  UploadActionTypes.StoreSelectedCaseInfo,
  props<{
    selectedCaseInfo: CaseDetailModel
  }>()
)

export const addFileFolder = createAction(
  UploadActionTypes.AddFileFolder,
  props<{ fileFolder: QueuedRepositoryModel }>()
)

export const updateFileFolder = createAction(
  UploadActionTypes.UpdateFileFolder,
  props<{ fileFolder: QueuedRepositoryModel }>()
)

export const updateFileFolderInBulk = createAction(
  UploadActionTypes.UpdateFileFolderInBulk,
  props<{ fileFolders: QueuedRepositoryModel[] }>()
)

export const removeFileFolder = createAction(
  UploadActionTypes.RemoveFileFolder,
  props<{ folderId: string }>()
)

export const removeQueuedMedia = createAction(
  UploadActionTypes.RemoveQueuedMedia,
  props<{ queuedMediaId: string }>()
)

export const clearFileFolder = createAction(UploadActionTypes.ClearFileFolder)

export const processFromRepository = createAction(
  UploadActionTypes.ProcessFromRepository,
  props<{
    projectId: number
    repositoryFiles: QueuedRepositoryModel[]
    nsfUserIdFiles: NsfUserIdFile[]
  }>()
)
export const processFromRepositorySuccess = createAction(
  UploadActionTypes.ProcessFromRepositorySuccess,
  props<{ processSuccess: ResponseModel }>()
)
export const processFromRepositoryFailure = createAction(
  UploadActionTypes.ProcessFromRepositoryFailure,
  props<{ processError: ResponseModel }>()
)
export const fetchCustodians = createAction(
  UploadActionTypes.FetchCustodians,
  props<{
    projectId: number
  }>()
)
export const FetchCustodiansSuccess = createAction(
  UploadActionTypes.FetchCustodiansSuccess,
  props<{
    custodiansSuccess: ResponseModel
  }>()
)
export const FetchCustodiansFailure = createAction(
  UploadActionTypes.FetchCustodiansFailure,
  props<{
    custodiansError: ResponseModel
  }>()
)

export const addCustomCustodian = createAction(
  UploadActionTypes.AddCustomCustodian,
  props<{ custodianName: string }>()
)

export const storeSelectedFileMetaInfo = createAction(
  UploadActionTypes.StoreSelectedFileMetaInfo,
  props<{
    selectedFileMetaInfo: SelectedFileMetaInfo | SelectedFileMetaInfo[]
  }>()
)

export const removeSelectedFileMetaInfo = createAction(
  UploadActionTypes.RemoveSelectedFileMetaInfo,
  props<{ fileId: string | string[] }>()
)

export const updateSelectedFileMetaInfo = createAction(
  UploadActionTypes.UpdateSelectedFileMetaInfo,
  props<{
    selectedFileMetaInfo: SelectedFileMetaInfo
  }>()
)

export const storeCustomCustodian = createAction(
  UploadActionTypes.StoreCustomCustodian,
  props<{ customCustodianName: string }>()
)
