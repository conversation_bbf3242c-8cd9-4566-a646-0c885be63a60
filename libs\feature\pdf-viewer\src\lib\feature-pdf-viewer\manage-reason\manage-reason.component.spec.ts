import { ComponentFixture, TestBed } from '@angular/core/testing'
import {
  ManageReasonComponent,
  ManageReasonModule,
} from './manage-reason.component'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import {
  FieldFacade,
  PdfViewerFacade,
  PdfViewerService,
  ReasonModel,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { BehaviorSubject, of } from 'rxjs'
import { signal } from '@angular/core'
import { ResponseModel } from '@venio/shared/models/interfaces'

describe('ManageReasonComponent', () => {
  let component: ManageReasonComponent
  let fixture: ComponentFixture<ManageReasonComponent>

  const mockPdfViewerFacade = {
    fetchRedactionTypes: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: [{ id: 1, redactionTypeName: 'Rectangle' }],
      } as ResponseModel)
    ),
    fetchRedactionReasons: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: [{ id: 1, reason: 'Confidential', isInUse: true }],
      } as ResponseModel)
    ),
    setRedactionReasons: jest.fn(),
    getRedactionReasons: jest
      .fn()
      .mockReturnValue(
        of([{ id: 1, reason: 'Confidential', isInUse: true }] as ReasonModel[])
      ),
    fetchProjectGroups: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: [{ groupId: 1, groupName: 'Group 1' }],
      } as ResponseModel)
    ),
    addRedactionSet: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        message: 'Redaction set created successfully',
      })
    ),
    lookupRedactionSet: jest.fn().mockReturnValue(
      of({
        status: 'Success',
        data: false,
      } as ResponseModel)
    ),
    isAnnotationChanged: signal(false),
  }

  // Mock for PdfViewerService
  const mockPdfViewerService = {
    redactionReasons$: new BehaviorSubject<Array<ReasonModel>>([
      { id: 1, reason: 'Confidential', isInUse: true },
    ]),
  }

  // Mock for SearchFacade
  const mockSearchFacade = {
    getSearchTempTables$: of({
      searchResultTempTable: 'temp_table_123',
    }),
  }

  // Mock for FieldFacade
  const mockFieldFacade = {}
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ManageReasonModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: PdfViewerFacade, useValue: mockPdfViewerFacade },
        { provide: PdfViewerService, useValue: mockPdfViewerService },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: FieldFacade, useValue: mockFieldFacade },
        provideNoopAnimations(),
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ManageReasonComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
