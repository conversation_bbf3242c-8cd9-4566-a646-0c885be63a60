<!-- NOTE: The z-index were 10002 which seems push dialog behind so we reduce it to 10000 -->
<!-- So the dialog will be on top of the kendo window -->
<kendo-window
  [left]="0"
  [top]="-200"
  [resizable]="false"
  [draggable]="false"
  class="v-custom-window !t-z-[10000]"
  state="maximized">
  <kendo-window-titlebar>
    <span class="k-window-title t-items-center t-text-primary t-text-lg">
      <button
        class="t-flex t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-items-center"
        fillMode="clear"
        kendoButton>
        <span
          class="t-flex t-justify-center t-mx-auto"
          venioSvgLoader
          height="70%"
          width="70%"
          svgUrl="assets/svg/process-svgrepo-com.svg">
        </span>
      </button>
      New Upload
    </span>
    <button
      kendoWindowCloseAction
      (click)="closeDialog($event)"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
  </kendo-window-titlebar>
  <div
    class="t-flex t-flex-row t-flex-1 t-gap-[30px] t-my-[10px] t-h-[calc(100%_-_20px)]">
    <div class="t-flex t-h-[300px] t-relative">
      <div class="t-flex t-self-stretch t-flex-1 t-relative">
        <kendo-stepper
          class="v-custom-upload-stepper"
          [style.width.px]="150"
          [steps]="steps"
          [currentStep]="currentStepIndex()"
          stepType="full"
          orientation="vertical">
          <ng-template kendoStepperStepTemplate let-step let-index="index">
            <div class="t-flex t-items-center">
              <!-- Icon Container -->
              <div
                class="k-step-indicator t-w-8 t-h-8 t-rounded-full t-flex t-items-center t-justify-center">
                <kendo-svg-icon
                  *ngIf="index === currentStepIndex()"
                  [icon]="pencilIcon"
                  class="t-text-white"></kendo-svg-icon>

                <kendo-svg-icon
                  *ngIf="index < currentStepIndex()"
                  [icon]="checkIcon"
                  class="t-text-white"></kendo-svg-icon>
              </div>

              <!-- Step Label -->
              <span
                class="t-ml-1"
                [ngClass]="{
                  't-text-[#4A4B90]': index === currentStepIndex(),
                  't-text-[#9BD2A7]': index < currentStepIndex(),
                  't-text-[#C7C7C7]': index > currentStepIndex()
                }">
                {{ step.label }}
              </span>
            </div>
          </ng-template>
        </kendo-stepper>
        <div
          class="t-absolute t-top-0 t-right-0 t-bottom-0 t-left-0 t-z-50"></div>
      </div>
    </div>
    <div class="t-flex t-h-full t-flex-col t-flex-1">
      @defer{ @switch (currentStepIndex()) { @case (0) {
      <venio-upload-source-selector class="t-flex" />
      } @case (1) {
      <venio-upload-file-browse-toolbar class="t-flex t-w-full" />
      } } @if (currentStepIndex() === 1) {
      <venio-upload-file-browse-container
        class="t-flex t-flex-1 t-flex-row t-relative" />
      } @if(currentStepIndex() === 2){
      <venio-upload-file-progress-statuses
        (uploadStatusAction)="handleUploadStatusAction($event)" />
      } } @placeholder {
      <div
        class="t-flex t-flex-row t-flex-wrap t-gap-5 t-grow t-items-stretch t-justify-start t-px-0 sm:t-px-4 md:t-px-6 lg:t-px-20 xl:t-px-24 2xl:t-px-20">
        @for(n of [1,2,3,4]; track n) {
        <kendo-skeleton
          height="200px"
          width="24%"
          shape="rectangle"
          class="t-flex-col t-rounded" />
        }
      </div>
      }
    </div>
  </div>
</kendo-window>
