import { ComponentFixture, TestBed } from '@angular/core/testing'
import { RepositoryUploadComponent } from './repository-upload.component'
import { VenioNotificationService } from '@venio/feature/notification'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import {
  RepositoryBrowserStateService,
  UploadFacade,
} from '@venio/data-access/common'
import { BehaviorSubject, of } from 'rxjs'
import {
  MediaSourceType,
  RepositoryHierarchyModel,
} from '@venio/shared/models/interfaces'
import { signal } from '@angular/core'
import { UuidGenerator } from '@venio/util/uuid'

describe('RepositoryUploadComponent', () => {
  let component: RepositoryUploadComponent
  let fixture: ComponentFixture<RepositoryUploadComponent>
  let mockVenioNotificationService: jest.Mocked<VenioNotificationService>
  let mockUploadFacade: Partial<UploadFacade>
  let mockRepoStateService: Partial<RepositoryBrowserStateService>

  beforeEach(async () => {
    // Mock UuidGenerator.uuid to return a predictable value
    jest.spyOn(UuidGenerator, 'uuid', 'get').mockReturnValue('test-uuid-123')

    // GIVEN mocked dependencies
    mockVenioNotificationService = {
      showSuccess: jest.fn(),
      showError: jest.fn(),
      showWarning: jest.fn(),
    } as unknown as jest.Mocked<VenioNotificationService>

    mockUploadFacade = {
      selectCustodians$: of(['abc']),
      validationMessage$: new BehaviorSubject(undefined),
      selectFileFolderForUpload$: of([]),
      selectSelectedCaseInfo$: of(undefined),
      selectProcessFromRepositorySuccessResponse$: of({
        data: [],
        status: 'Success',
        message: '',
      }),
      selectProcessFromRepositoryErrorResponse$: of({
        data: [],
        status: 'Success',
        message: '',
      }),
      addFileFolderForUpload: jest.fn(),
    }

    mockRepoStateService = {
      validateIfNewSourceAlreadyExists: jest.fn(),
      updateExistingFolders: jest.fn(),
      mediaSourceType: signal(MediaSourceType.SOURCE_FILE),
    }

    await TestBed.configureTestingModule({
      imports: [RepositoryUploadComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore(),
        {
          provide: VenioNotificationService,
          useValue: mockVenioNotificationService,
        },
        {
          provide: UploadFacade,
          useValue: mockUploadFacade,
        },
        {
          provide: RepositoryBrowserStateService,
          useValue: mockRepoStateService,
        },
      ],
    }).compileComponents()
    fixture = TestBed.createComponent(RepositoryUploadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    // GIVEN the component is set up
    // THEN it should exist
    expect(component).toBeTruthy()
  })

  it('should show warning when no files or folders are selected', () => {
    // GIVEN empty data array
    const emptyData: RepositoryHierarchyModel[] = []

    // WHEN onFileFolderSelected is called with empty data
    component.onFileFolderSelected(emptyData)

    // THEN notification service should show warning
    expect(mockVenioNotificationService.showWarning).toHaveBeenCalledWith(
      'Select at least one file or folder'
    )
    expect(mockUploadFacade.addFileFolderForUpload).not.toHaveBeenCalled()
  })

  it('should update existing folders when there are conflicts', () => {
    // GIVEN data with conflicts
    const mockData: RepositoryHierarchyModel[] = [
      {
        id: '1',
        name: 'Folder1',
        type: 'FOLDER',
        relativePath: 'path/to/folder1',
        fsid: '123',
        repositoryDisplayName: 'Repo1',
        repositoryRootFolderName: 'Root',
        isLoading: false,
        isLoaded: true,
        isLoadedAll: true,
        isExpanded: true,
        isCancelling: false,
        showNodeMenuOptions: false,
        hasSuccesfulConnection: true,
        errorMessage: '',
        fullPath: 'full/path/to/folder1',
        nsfUserIdFiles: null as unknown as File,
      },
    ]

    // WHEN validation returns conflicts
    jest
      .mocked(mockRepoStateService.validateIfNewSourceAlreadyExists)
      .mockReturnValue({
        childOfNewPaths: ['path/child'],
        parentOfNewPaths: ['path/parent'],
      })

    component.onFileFolderSelected(mockData)

    // THEN updateExistingFolders should be called
    expect(
      mockRepoStateService.validateIfNewSourceAlreadyExists
    ).toHaveBeenCalledWith(mockData)
    expect(mockRepoStateService.updateExistingFolders).toHaveBeenCalledWith(
      mockData
    )
    expect(mockUploadFacade.addFileFolderForUpload).not.toHaveBeenCalled()
  })

  it('should add files/folders for upload when there are no conflicts', () => {
    // GIVEN data with no conflicts
    const mockData: RepositoryHierarchyModel[] = [
      {
        id: '1',
        name: 'Folder1',
        type: 'FOLDER',
        relativePath: 'path/to/folder1',
        fsid: '123',
        repositoryDisplayName: 'Repo1',
        repositoryRootFolderName: 'Root',
        isLoading: false,
        isLoaded: true,
        isLoadedAll: true,
        isExpanded: true,
        isCancelling: false,
        showNodeMenuOptions: false,
        hasSuccesfulConnection: true,
        errorMessage: '',
        fullPath: 'full/path/to/folder1',
        nsfUserIdFiles: null as unknown as File,
      },
    ]

    // WHEN validation returns no conflicts
    jest
      .mocked(mockRepoStateService.validateIfNewSourceAlreadyExists)
      .mockReturnValue({
        childOfNewPaths: [],
        parentOfNewPaths: [],
      })

    component.onFileFolderSelected(mockData)

    // THEN addFileFolderForUpload should be called with correct parameters
    expect(
      mockRepoStateService.validateIfNewSourceAlreadyExists
    ).toHaveBeenCalledWith(mockData)
    expect(mockUploadFacade.addFileFolderForUpload).toHaveBeenCalledWith(
      expect.objectContaining({
        custodianName: '',
        mediaName: '',
        mediaSourceType: MediaSourceType.SOURCE_FILE,
        repositoryHierarchies: mockData,
      })
    )
    expect(mockRepoStateService.updateExistingFolders).not.toHaveBeenCalled()
  })

  it('should use mediaSourceType from repoStateService when available', () => {
    // GIVEN data with no conflicts and a specific mediaSourceType
    const mockData: RepositoryHierarchyModel[] = [
      {
        id: '1',
        name: 'File1',
        type: 'FILE',
        relativePath: 'path/to/file1',
        fsid: '123',
        repositoryDisplayName: 'Repo1',
        repositoryRootFolderName: 'Root',
        isLoading: false,
        isLoaded: true,
        isLoadedAll: true,
        isExpanded: false,
        isCancelling: false,
        showNodeMenuOptions: false,
        hasSuccesfulConnection: true,
        errorMessage: '',
        fullPath: 'full/path/to/file1',
        nsfUserIdFiles: null as unknown as File,
      },
    ]

    // WHEN mediaSourceType is set to a specific value
    mockRepoStateService.mediaSourceType = signal(MediaSourceType.AWSS3_FILE)

    // AND validation returns no conflicts
    jest
      .mocked(mockRepoStateService.validateIfNewSourceAlreadyExists)
      .mockReturnValue({
        childOfNewPaths: [],
        parentOfNewPaths: [],
      })

    component.onFileFolderSelected(mockData)

    // THEN addFileFolderForUpload should be called with the correct mediaSourceType
    expect(mockUploadFacade.addFileFolderForUpload).toHaveBeenCalledWith(
      expect.objectContaining({
        mediaSourceType: MediaSourceType.AWSS3_FILE,
      })
    )
  })
})
