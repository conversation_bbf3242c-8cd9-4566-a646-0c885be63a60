<div
  class="t-flex t-gap-4 t-bg-[#F9F9F9] t-pt-4"
  [ngClass]="{
    't-h-[45vh]': isForReprocessing(),
    't-h-[78vh]': !isForReprocessing()
  }">
  <div class="t-relative t-flex t-flex-col t-gap-2 t-w-1/4">
    <kendo-textbox
      class="!t-border-[#ccc] t-w-full"
      placeholder="Search"
      (valueChange)="onRepositorySearch($event)"
      [clearButton]="true">
      <ng-template kendoTextBoxSuffixTemplate>
        <button
          kendoButton
          fillMode="clear"
          class="t-text-[#1EBADC]"
          imageUrl="assets/svg/icon-updated-search.svg"></button>
      </ng-template>
    </kendo-textbox>

    @defer(when !isRepositoryListLoading()){
    <kendo-grid
      class="t-w-full t-h-full !t-border t-border-[#DADADA] t-relative t-overflow-y-auto v-custom-repro-setting"
      [kendoGridBinding]="filteredRepositoryList()"
      [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
      (selectionChange)="onSelectionChange($event)"
      kendoGridSelectBy="fsid"
      [(selectedKeys)]="selectedRepositoryKeys"
      [sortable]="true"
      [groupable]="false"
      [reorderable]="true"
      [resizable]="true">
      <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
      </kendo-grid-checkbox-column>
      <kendo-grid-column
        field="fsDisplayName"
        title="Select All"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate>
          <div class="t-flex t-items-center t-gap-2 t-w-full t-justify-between">
            <span>Select All</span>
            <button
              kendoButton
              themeColor="secondary"
              class="v-custom-secondary-button t-mr-[-10px] t-w-9 t-h-9 hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
              fillMode="outline"
              (click)="refreshRepositoryList()"
              #actionGrid>
              <span
                venioSvgLoader
                [parentElement]="actionGrid.element"
                applyEffectsTo="stroke"
                hoverColor="#FFFFFF"
                color="#9BD2A7"
                svgUrl="assets/svg/icon-refresh-twoway.svg"
                height="1.1rem"
                width="1.1rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
          </div>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <span>{{ dataItem.fsDisplayName }}</span>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
    } @placeholder{
    <kendo-skeleton
      *ngFor="let n of [1, 2, 3, 4, 5]"
      height="25px"
      width="100%"
      shape="rectangle"
      class="t-rounded-md t-mt-1" />
    }
  </div>
  <div class="t-relative t-w-3/4">
    <h3
      class="t-font-semibold t-text-base t-text-primary t-my-2 t-border-[#CCCCCC] t-border-b-[1px] t-pb-2">
      Repository Hierarchy
    </h3>

    <div class="t-grid t-grid-cols-1 t-gap-4 t-bg-[#F9F9F9] t-pt-2">
      <div class="t-flex t-flex-col">
        <h3 class="t-font-semibold t-text-base t-text-primary t-px-3">
          File Folder Name
        </h3>
        @defer(when !isHierarchyLoading()) { @if (!isHierarchyLoading()) {
        <div class="t-flex t-justify-between t-m-4 t-gap-4">
          <kendo-textbox
            #customInput
            (valueChange)="filterTerm = $event"
            [clearButton]="true"
            placeholder="Filter...">
            <ng-template kendoTextBoxPrefixTemplate>
              <span class="k-input-icon k-font-icon k-icon k-i-search"></span>
            </ng-template>
          </kendo-textbox>
          <button
            *ngIf="!isForReprocessing()"
            kendoButton
            (click)="addFileFolder()"
            fillMode="outline"
            class="v-custom-secondary-button"
            themeColor="secondary">
            Add
          </button>
        </div>
        <kendo-treeview
          [nodes]="repoStateService.filteredRepositoryHierarchy()"
          textField="name"
          class="t-overflow-y-auto"
          [ngClass]="{
            't-h-[14.5rem]': isForReprocessing(),
            't-h-[32.18rem]': !isForReprocessing()
          }"
          kendoTreeViewExpandable
          kendoTreeViewFlatDataBinding
          kendoTreeViewSelectable
          idField="id"
          parentIdField="parentId"
          (expand)="onFolderExpand($event)"
          [filter]="filterTerm"
          [(checkedKeys)]="checkedFileFoldersIds"
          (selectionChange)="onFileSelectionChange($event)"
          [checkBy]="'id'"
          [kendoTreeViewCheckable]="checkableSettings">
          <ng-template
            kendoTreeViewNodeTemplate
            let-item
            let-isExpanded="isExpanded">
            <div class="t-flex t-gap-2 t-justify-start t-text-[#263238]">
              <span
                venioSvgLoader
                height="1rem"
                width="1rem"
                *ngIf="item.type === 'FOLDER'"
                svgUrl="assets/svg/icon-folder-fclv-fill.svg">
                <kendo-loader size="small"></kendo-loader>
              </span>
              <kendo-svgicon
                *ngIf="!(item.type === 'FOLDER')"
                [icon]="getFileIcon()"
                class="t-text-[#8f8f8f]"></kendo-svgicon>
              <span
                class="t-inline-block t-absolute t-ml-7 t-text-[#263238]"
                [ngClass]="{
                  't-font-semibold': item.type === 'FOLDER'
                }">
                {{ item.name }}
              </span>
            </div>
          </ng-template>
        </kendo-treeview>

        }@else {
        <kendo-skeleton
          height="50px"
          width="100%"
          shape="rectangle"
          class="t-rounded-md t-mt-1" />
        <kendo-skeleton
          *ngFor="let n of [1, 2, 3, 4, 5]"
          height="25px"
          width="100%"
          shape="rectangle"
          class="t-rounded-md t-mt-1" />
        } } @placeholder {
        <kendo-skeleton
          height="50px"
          width="100%"
          shape="rectangle"
          class="t-rounded-md t-mt-1" />
        <kendo-skeleton
          *ngFor="let n of [1, 2, 3, 4, 5]"
          height="25px"
          width="100%"
          shape="rectangle"
          class="t-rounded-md t-mt-1" />
        }
      </div>
    </div>
  </div>
</div>
