import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TabsContainerComponent } from './tabs-container.component'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { StoreModule } from '@ngrx/store'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import {
  ModuleLoginStateService,
  ProjectFacade,
  UserFacade,
} from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  DocumentShareFacade,
  RightModel,
  StartupsFacade,
} from '@venio/data-access/review'
import {
  CaseDetailResponseModel,
  ReviewSetSummary,
} from '@venio/shared/models/interfaces'
import { ActivatedRoute } from '@angular/router'
import { CommonActionTypes } from '@venio/shared/models/constants'

describe('TabsContainerComponent', () => {
  let component: TabsContainerComponent
  let fixture: ComponentFixture<TabsContainerComponent>
  let moduleLoginState: ModuleLoginStateService

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TabsContainerComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                popup: 'reprocessing',
                projectId: '1',
              },
            },
            queryParams: of({
              popup: 'reprocessing',
              projectId: '1',
            }),
          },
        },
        provideMockStore({
          initialState: {
            project: {
              isCaseDetailLoading: false,
            },
            usersStore: {
              currentUserSuccessResponse: {},
            },
          },
        }),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ProjectFacade,
          useValue: {
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({
              caseDetailEntries: [],
            } as CaseDetailResponseModel),
            updateCaseDetailRequestInfo: jest.fn(),
            fetchCaseDetail: jest.fn(),
            selectReviewSetSummaryDetail$: of({} as ReviewSetSummary),
            fetchReviewSetSummaryDetail: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserDetails$: of({}),
            selectCurrentUserSuccessResponse$: of({}),
            selectCurrentUsername$: of(''),
          },
        },
        {
          provide: ModuleLoginStateService,
          useValue: {
            updateProjectId: jest.fn(),
          },
        },
        {
          provide: DocumentShareFacade,
          useValue: {
            selectSharedDocumentList$: of({}),
            fetchSharedDocuments: jest.fn(),
          },
        },
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({} as RightModel),
          } satisfies Partial<StartupsFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TabsContainerComponent)
    moduleLoginState = TestBed.inject(ModuleLoginStateService)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should update project ID when case grid action is clicked', () => {
    const mockEvent = {
      actionType: CommonActionTypes.REVIEW,
      content: {
        projectId: 123,
      },
    }

    component.caseGridActionClick(mockEvent)

    expect(moduleLoginState.updateProjectId).toHaveBeenCalledWith(123)
  })
})
