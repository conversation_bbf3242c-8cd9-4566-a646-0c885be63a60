import { Injectable } from '@angular/core'
import { RedactionSet } from '@venio/data-access/review'
import NutrientViewer, { Annotation, Color, Rect } from '@nutrient-sdk/viewer'

@Injectable({
  providedIn: 'root',
})
export class AnnotationHelperService {
  constructor() {}

  public getAnnotationByRedactionSet(
    redactionSet: RedactionSet,
    pageIndex: number,
    boundingBox: Rect,
    isWholePageRedaction: boolean
  ): Annotation {
    const customData = isWholePageRedaction
      ? { wholePageRedaction: true }
      : { objectId: redactionSet.id }

    if (!redactionSet.caption) {
      const annotation = new NutrientViewer.Annotations.RectangleAnnotation({
        strokeColor: NutrientViewer.Color.TRANSPARENT,
        strokeWidth: 0,
        font: 'Helvetica',
        isBold: true,
        fillColor: this.getPSPPDFKitColor(redactionSet.backColor),
        color: this.getPSPPDFKitColor(redactionSet.backColor),
        pageIndex: pageIndex,
        opacity: redactionSet.type === 'Highlight' ? 0.5 : 1,
        boundingBox: boundingBox,
        customData: customData,
      })
      return annotation
    }
    const annotation = new NutrientViewer.Annotations.StampAnnotation({
      stampType: 'Custom',
      title: redactionSet.caption,
      boundingBox: boundingBox,
      pageIndex: pageIndex,
      color: this.getPSPPDFKitColor(redactionSet.backColor),
      customData: customData,
    })
    return annotation
  }

  public getPSPPDFKitColor(color: string): Color {
    color = color.toLowerCase()
    if (color === 'white') return NutrientViewer.Color.WHITE
    else if (color === 'yellow') return NutrientViewer.Color.YELLOW
    return NutrientViewer.Color.BLACK
  }
}
