import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { HttpErrorResponse } from '@angular/common/http'
import { CommonModule, Location, isPlatformBrowser } from '@angular/common'
import NutrientViewer, {
  Annotation,
  Color,
  HighlightAnnotation,
  Instance,
  List,
  Rect,
  SearchResult,
  StampAnnotation,
} from '@nutrient-sdk/viewer'
import {
  EMPTY,
  Observable,
  Subject,
  catchError,
  filter,
  finalize,
  forkJoin,
  from,
  of,
  switchMap,
  take,
  takeUntil,
  tap,
  throwError,
} from 'rxjs'
import { environment } from '@venio/shared/environments'
import {
  DataAccessReviewModule,
  DocumentsFacade,
  InitialSearchResultParameter,
  ReviewParamService,
  SearchFacade,
  UserRights,
  Viewer,
  ActionModel,
  DefaultHighlightGroupInfo,
  FulltextFacade,
  HighlightGroupModel,
  HighlightGroupTermModel,
  PDFLoadingArgs,
  ImageType,
  ImageExportFormat,
  svgIconForPageDetail,
  svgIconForToolbar,
  svgIconForPageControls,
  PageControlActionType,
  PdfAnnotationProperties,
  BulkCopyRedaction,
  CreateRedactionSet,
  WholePageRedaction,
  IconModel,
  PageSelectionOption,
  PdfPageInfo,
  PdfSaveAnnotationPayload,
  RedactionSet,
  WholePageRedactionOptions,
  WholePageRedactionPayload,
  ExportDetail,
  PdfViewerFacade,
  IndexedDBHandlerService,
  SearchResultFacade,
} from '@venio/data-access/review'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import {
  DocumentPrefetchWorkerModel,
  NativePrefetchDocumentModel,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { Remote, releaseProxy, wrap } from 'comlink'
import { AnnotationHelperService } from '../services/annotation-helper.service'
import {
  ReviewPanelFacade,
  UpdateDocumentHistoryPage,
} from '@venio/data-access/document-utility'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'

import { FormsModule } from '@angular/forms'
import {
  VenioNotificationModule,
  VenioNotificationService,
} from '@venio/feature/notification'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { Collision, PopupModule } from '@progress/kendo-angular-popup'

import { uniq } from 'lodash'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  ShortcutKeyBindings,
  ShortcutKeyDescriptions,
} from '@venio/shared/models/constants'
import { ShortcutManager } from '@venio/util/utilities'
import { LocalStorage } from '@venio/shared/storage'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { AuthStorageKeys } from '@venio/data-access/auth'

declare type AnnotationExtractor = (xfdf: string) => string
declare type AnnotationFilter = (xfdf: string) => string
declare type PdfDocumentPrefetchAction = (
  data: DocumentPrefetchWorkerModel
) => Array<number>

@Component({
  selector: 'venio-feature-pdf-viewer',
  standalone: true,
  templateUrl: './feature-pdf-viewer.component.html',
  styleUrls: ['./feature-pdf-viewer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    DataAccessReviewModule,
    ButtonsModule,
    DialogsModule,
    SvgLoaderDirective,
    DropDownsModule,
    FormsModule,
    VenioNotificationModule,
    TooltipsModule,
    UserGroupRightCheckDirective,
    PopupModule,
    LoaderModule,
    InputsModule,
    LabelModule,
  ],
  providers: [VenioNotificationService],
})
export class FeaturePdfViewerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  private reviewParamService = inject(ReviewParamService)

  public pdfViewerFacade = inject(PdfViewerFacade)

  private documentFacade = inject(DocumentsFacade)

  private searchFacade = inject(SearchFacade)

  private searchResultFacade = inject(SearchResultFacade)

  private fulltextFacade = inject(FulltextFacade)

  private reviewPanelFacade = inject(ReviewPanelFacade)

  private annotationHelper = inject(AnnotationHelperService)

  private dialogService = inject(DialogService)

  private notification = inject(VenioNotificationService)

  private iframeMessengerService = inject(IframeMessengerService)

  private cdr = inject(ChangeDetectorRef)

  private location = inject(Location)

  private platformId = inject(PLATFORM_ID)

  private indexDb = inject(IndexedDBHandlerService)

  @ViewChild('pdfContainer') public pdfContainer: ElementRef<HTMLDivElement>

  private isAnnotationHidden = false

  public isRedactedPagesOnly: boolean

  private selectedHighlightGroupsTerms: HighlightGroupTermModel[] = []

  private similarTerms: string[]

  private isOpaque = true

  private stampAnnotationMap: { [key: string]: StampAnnotation } = {}

  /**
   * Getter for the full application URL.
   * This method computes the URL by combining the current location's protocol and host
   * with the deployment URL specified in the environment configuration.
   * The deployment URL typically represents where the application is hosted,
   * and includes the relative path of the application on the server.
   * @returns {string} The full application URL.
   */
  private get applicationUrl(): string {
    if (isPlatformBrowser(this.platformId)) {
      const url = new URL(this.location.path(), window.location.href)
      return `${url.protocol}//${url.host}${environment.deployUrl}`
    }
  }

  public viewerInstance: Instance

  private toDestroy$: Subject<void> = new Subject<void>()

  private wholePageRedactionSet: RedactionSet

  private pagesInfo: { [key: number]: PdfPageInfo }

  private annotationData: PdfAnnotationProperties

  private currentPdf: Blob

  public redactionSets: RedactionSet[]

  public highlightSets: RedactionSet[]

  private currentFileId: number

  private previousFileId: number

  private shortcutManager: ShortcutManager

  private tiffPageNumber = 1

  public get isViewerPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isViewerPanelPopout')
  }

  public get currentPageNumber(): number {
    if (
      this.selectedImage === 'ProducedImage' &&
      this.selectedExportDetail?.imageFormat === ImageExportFormat.Tiff
    )
      return this.tiffPageNumber
    return this.currentPageIndex + 1
  }

  public set currentPageNumber(value: number) {
    this.currentPageIndex = value - 1
  }

  public currentPageIndex = 0 as number

  private projectId: number

  public svgIconForPageDetail: Array<IconModel> = svgIconForPageDetail

  public svgIconForToolbar: Array<IconModel> = svgIconForToolbar

  public svgIconForPageControls: Array<IconModel> = svgIconForPageControls

  public pageControlActionType = PageControlActionType

  public showToolbars = false

  public viewerErrorMessage: string

  public pageNumberList: Array<number>

  public UserRights = UserRights

  private searchParameters: InitialSearchResultParameter

  public isLoading = false

  public showHighlightGroup: boolean

  public selectedHighlightGroups: any

  @ViewChild('highlightGroupComponent', { read: ViewContainerRef })
  private highlightGroupVCR: ViewContainerRef

  public collision: Collision = { horizontal: 'flip', vertical: 'fit' }

  public imageTypes = [
    { label: 'Original Image', value: 'OriginalImage' },
    { label: 'Produced Image', value: 'ProducedImage' },
  ]

  public highlightGroups: HighlightGroupModel[] = []

  public highlightGroupSettingInfo: DefaultHighlightGroupInfo

  public exportDetails: ExportDetail[]

  public selectedExportId: number | null = null

  public selectedImage = 'OriginalImage' // Default selection

  public hasProducedImage = false

  public get selectedExportDetail(): ExportDetail {
    const exportDetail = this.exportDetails.find(
      (exp) => exp.exportId === this.selectedExportId
    )
    return exportDetail
  }

  private currentResultIndex = -1

  private searchResults: List<SearchResult> = null

  private previousSearchHighlight = null

  public get showNavigationButton(): boolean {
    return this.searchResults?.count() > 0
  }

  private activeRedactionSet: RedactionSet

  constructor() {}

  public ngAfterViewInit(): void {
    this.documentFacade.viewerComponentReady = Viewer.PDF
    this.#initPDFPaginationShortcutKeys()
    this.pdfContainer.nativeElement.focus()
  }

  public ngOnInit(): void {
    this.init()
  }

  public init(): void {
    this.documentFacade.loadExportDetails = {
      exportId: 0,
      imageType: ImageType.OriginalImage,
    }

    this.reviewParamService.projectId
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((projectId: number) => {
        this.projectId = projectId
        this.fetchRedactionSets()
        this.fetchPdf()
        this.fetchProducedPdf()
      })

    this.searchFacade.getSearchInitialParameters$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((res) => {
        this.searchParameters = res
      })

    this.fulltextFacade.fetchHighlightSettings(this.projectId).pipe()

    this.fulltextFacade
      .fetchTextHighlightGroups(this.projectId)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((result: ResponseModel) => {
        this.cdr.markForCheck()
        this.highlightGroups = result.data
      })

    this.pdfViewerFacade.highlightGroupActionHandler
      .pipe(
        tap((action: ActionModel) => {
          this.cdr.markForCheck()
          this.showHighlightGroup = false
          if (action?.data?.action) {
            this.selectedHighlightGroups = action?.data?.selectedHighlightGroups
          }
        }),
        filter((action: ActionModel) => action?.data?.action),
        switchMap(() => {
          const highlightGroupIds = this.selectedHighlightGroups.map(
            (group) => group.itemKey
          )
          return this.fulltextFacade.fetchTextHighlightGroupsTermsByIds(
            this.projectId,
            highlightGroupIds
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.selectedHighlightGroupsTerms = response.data
        this.SearchAndHighlightTerms()
      })

    this.pdfViewerFacade.saveAnnotationAction
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.saveAnnotationHandler()
      })
    this.SelectSimilarTerms()
  }

  private SelectSimilarTerms(): void {
    this.reviewPanelFacade.selectSimilarDocumentTerms$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((similarTerms: string[]) => {
        this.similarTerms = similarTerms
        this.SearchAndHighlightTerms()
        this.cdr.markForCheck()
      })
  }

  private pdfShortcutHandlers: Partial<{
    [key in ShortcutKeyBindings]: {
      description: ShortcutKeyDescriptions
      handler: (event: Event) => void
    }
  }> = {
    [ShortcutKeyBindings.MOVE_PREVIOUS_PAGE]: {
      description: ShortcutKeyDescriptions.MOVE_PREVIOUS_PAGE,
      handler: (event) =>
        this.handleNavigationEvent(event, () => this.navigateToPreviousPage()),
    },
    [ShortcutKeyBindings.MOVE_NEXT_PAGE]: {
      description: ShortcutKeyDescriptions.MOVE_NEXT_PAGE,
      handler: (event) =>
        this.handleNavigationEvent(event, () => this.navigateToNextPage()),
    },
    [ShortcutKeyBindings.FIRST_PAGE]: {
      description: ShortcutKeyDescriptions.FIRST_PAGE,
      handler: (event) =>
        this.handleNavigationEvent(event, () => this.navigateToFirstPage()),
    },
    [ShortcutKeyBindings.LAST_PAGE]: {
      description: ShortcutKeyDescriptions.LAST_PAGE,
      handler: (event) =>
        this.handleNavigationEvent(event, () => this.navigateToLastPage()),
    },
    [ShortcutKeyBindings.NAVIGATE_NEXT_HIGHLIGHT]: {
      description: ShortcutKeyDescriptions.NAVIGATE_NEXT_HIGHLIGHT,
      handler: (event) =>
        this.handleNavigationEvent(event, () => this.navigateToNextResult()),
    },
    [ShortcutKeyBindings.NAVIGATE_PREVIOUS_HIGHLIGHT]: {
      description: ShortcutKeyDescriptions.NAVIGATE_PREVIOUS_HIGHLIGHT,
      handler: (event) =>
        this.handleNavigationEvent(event, () => this.navigateToPrevResult()),
    },
  }

  private handleNavigationEvent(
    event: Event,
    navigationMethod: () => void
  ): void {
    event.preventDefault()
    event.stopPropagation()
    navigationMethod()
  }

  #initializeShortCutsInIframes(): void {
    const frames = document.querySelectorAll('iframe')
    for (let index = 0; index < frames.length; index++) {
      const element = frames[index]
      this.#initPDFPaginationShortcutKeys(element.contentDocument)
    }
  }

  #initPDFPaginationShortcutKeys(control: HTMLElement | Document = null): void {
    if (control) this.shortcutManager = new ShortcutManager(control)
    else this.shortcutManager = new ShortcutManager()
    Object.entries(this.pdfShortcutHandlers).forEach(
      ([combo, { description, handler }]) => {
        this.shortcutManager.bind(combo, handler, { description })
      }
    )
  }

  #removePDFPaginationShortcutKeys(): void {
    Object.entries(this.pdfShortcutHandlers).forEach(([combo, { handler }]) => {
      this.shortcutManager.unbind(combo, undefined, handler)
    })
  }

  public ngOnDestroy(): void {
    this.#removePDFPaginationShortcutKeys()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public fetchRedactionSets(): void {
    this.pdfViewerFacade
      .fetchRedactionSets(this.projectId)
      .pipe(take(1))
      .subscribe((response: ResponseModel) => {
        this.categorizeRedactionSets(response.data)
      })
  }

  private categorizeRedactionSets(allRedactionSets: RedactionSet[]): void {
    this.redactionSets = allRedactionSets.filter(
      (s) => s.type.toLowerCase() === 'solid rectangle'
    )
    this.highlightSets = allRedactionSets.filter(
      (s) => s.type.toLowerCase() === 'highlight'
    )
  }

  public fetchPdf(): void {
    this.documentFacade.onLoadPdf
      .pipe(
        tap((args: PDFLoadingArgs) => {
          this.pdfViewerFacade.setAnnotationChanged(false)
          if (args.fileId !== this.currentFileId) this.tiffPageNumber = 1
        }),
        filter(
          (arg: PDFLoadingArgs) =>
            (arg.fileId > 0 && arg.fileId !== this.currentFileId) ||
            arg.isRefresh
        ),
        switchMap((args) => {
          this.cdr.markForCheck()
          this.isLoading = true
          this.viewerErrorMessage = ''

          // Check if PDF exists in IndexedDB and return Observable
          return from(
            this.indexDb.ifPdfExists(this.projectId, args.fileId)
          ).pipe(
            switchMap((pdfExists) => {
              if (pdfExists && !args.isRefresh) {
                // Load from IndexedDB
                return from(
                  this.indexDb.getPdfContent(this.projectId, args.fileId)
                ).pipe(
                  switchMap((cachedBlob: any) => {
                    console.log({ cachedBlob })
                    const fetchAnnotation$ =
                      this.pdfViewerFacade.fetchPdfAnnotations(
                        this.projectId,
                        args.fileId
                      )
                    const fetchExportDetails$ =
                      this.pdfViewerFacade.fetchExportDetails(
                        this.projectId,
                        args.fileId
                      )

                    return forkJoin([
                      of(args.fileId),
                      of(cachedBlob?.PdfContent),
                      fetchAnnotation$,
                      fetchExportDetails$,
                    ])
                  })
                )
              }
              // Continue with existing backend fetch logic
              return this.pdfViewerFacade
                .fetchImageStatus(this.projectId, args.fileId)
                .pipe(
                  take(1),
                  switchMap((response: ResponseModel) => {
                    this.cdr.markForCheck()
                    const message = response.message
                    if (message) {
                      this.currentFileId = args.fileId
                      this.viewerErrorMessage = message
                      this.isLoading = false
                      return EMPTY
                    }
                    const fetchDocument$ = this.pdfViewerFacade
                      .fetchPdfDocument(this.projectId, args.fileId)
                      .pipe(
                        tap((blob) => {
                          this.indexDb.addPdfContent(
                            this.projectId,
                            args.fileId,
                            blob
                          )
                        }),
                        catchError((err: unknown) => {
                          this.currentFileId = args.fileId
                          this.isLoading = false
                          if ((err as HttpErrorResponse).status === 404) {
                            this.viewerErrorMessage =
                              'PDF not available for this document'
                            return throwError(() => err)
                          }
                          this.viewerErrorMessage = (
                            err as HttpErrorResponse
                          ).message
                          console.error(err)
                          return throwError(
                            () => (err as HttpErrorResponse).message
                          )
                        })
                      )
                    const fetchAnnotation$ =
                      this.pdfViewerFacade.fetchPdfAnnotations(
                        this.projectId,
                        args.fileId
                      )

                    const fetchExportDetails$ =
                      this.pdfViewerFacade.fetchExportDetails(
                        this.projectId,
                        args.fileId
                      )

                    return forkJoin([
                      of(args.fileId),
                      fetchDocument$,
                      fetchAnnotation$,
                      fetchExportDetails$,
                    ]).pipe(
                      catchError((err: unknown) => {
                        this.cdr.markForCheck()
                        this.isLoading = false
                        return of(undefined)
                      })
                    )
                  })
                )
            }),
            catchError((err: unknown) => {
              this.cdr.markForCheck()
              this.isLoading = false
              return of(undefined)
            })
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([fileId, blob, annotationResponse, exportResponse]: [
          number,
          Blob,
          ResponseModel,
          ResponseModel
        ]) => {
          this.cdr.markForCheck()
          this.isLoading = false
          this.viewerErrorMessage = ''
          this.currentFileId = fileId
          this.annotationData = annotationResponse.data
          this.currentPdf = blob

          this.loadPdf()
          this.handleExportDetails(exportResponse, this.selectedExportId)

          this.prefetchPdfDocumentsAfterLoad()
          this.indexDb.deleteOldPdfRecords().catch((error) => {
            console.warn('Failed to delete old pdf records', error)
          })
        }
      )
  }

  private getPdfConversionQueuePayload(): NativePrefetchDocumentModel {
    const thresholdFileSize = 0
    const invalidExtensions = []
    const documentPrefetchCount = 5
    const data: NativePrefetchDocumentModel = {
      currentDocumentId: this.currentFileId,
      documentPrefetchCount,
      thresholdFileSize: thresholdFileSize * 1024,
      invalidFileExtensions: invalidExtensions,
    }
    return data
  }

  private prefetchPdfDocumentsAfterLoad(): void {
    const data: NativePrefetchDocumentModel =
      this.getPdfConversionQueuePayload()
    this.searchResultFacade
      .getPdfConversionElegibleFileIds(data)
      .pipe(take(1))
      .subscribe((fileIds: number[]) => {
        if (fileIds.length > 0) {
          this.prefetchPdfDocuments(fileIds).catch((err) => console.error(err))
        }
      })
  }

  private async prefetchPdfDocuments(fileIds: number[]): Promise<void> {
    const documentPrefetchHandler = new Worker(
      new URL('../worker/pdf-document-prefetch.worker', import.meta.url),
      { type: 'module' }
    )
    const obj: Remote<PdfDocumentPrefetchAction> =
      wrap<PdfDocumentPrefetchAction>(documentPrefetchHandler)

    await obj({
      fileIds: fileIds,
      projectId: this.projectId,
      accessToken: localStorage.getItem(AuthStorageKeys.AccessToken),
      serviceUrl: environment.apiUrl,
    })
    //this.errorredFileIds = new Set([...this.errorredFileIds, ...erroredFileIds])
    //release the worker
    obj[releaseProxy]()
  }

  private handleExportDetails(
    exportResponse: ResponseModel,
    exportId?: number
  ): void {
    if (exportResponse) {
      this.exportDetails = exportResponse.data.filter(
        (item) => item.exportType === 'PRODUCTION'
      )
      if (
        (this.exportDetails && this.exportDetails.length > 0) ||
        this.selectedImage === 'ProducedImage'
      ) {
        if (exportId === null) {
          this.selectedExportId = this.exportDetails[0].exportId
        } else this.selectedExportId = exportId
        this.hasProducedImage = true
      } else {
        this.selectedImage = 'OriginalImage'
        this.hasProducedImage = false
      }

      this.previousFileId = this.currentFileId

      this.documentFacade.loadExportDetails = {
        exportId: this.selectedExportId,
        imageType:
          this.selectedImage === 'OriginalImage'
            ? ImageType.OriginalImage
            : ImageType.ProducedImage,
      }
    }
  }

  public fetchProducedPdf(): void {
    this.documentFacade.onLoadProducedPdf
      .pipe(
        tap((args: PDFLoadingArgs) => {
          this.pdfViewerFacade.setAnnotationChanged(false)
          if (args.fileId !== this.currentFileId) this.tiffPageNumber = 1
        }),
        filter((arg: PDFLoadingArgs) => arg.fileId > 0 || arg.isRefresh),

        switchMap((args) => {
          this.cdr.markForCheck()
          this.currentFileId = args.fileId
          this.isLoading = true
          this.viewerErrorMessage = ''
          let obsFetchDocument$: Observable<Blob>
          if (args.imageFileFormat === ImageExportFormat.Pdf)
            obsFetchDocument$ = this.pdfViewerFacade.fetchProducedPdfImage(
              this.projectId,
              args.exportId,
              args.fileId
            )
          else
            obsFetchDocument$ = this.pdfViewerFacade.fetchProducedTiffImage(
              this.projectId,
              args.exportId,
              args.fileId,
              args.pageNumber ?? 1
            )
          const fetchDocument$ = obsFetchDocument$.pipe(
            catchError((err: unknown) => {
              this.currentFileId = args.fileId
              this.isLoading = false
              if ((err as HttpErrorResponse).status === 404) {
                this.viewerErrorMessage =
                  'PDF not available for this production'
                return of(undefined)
              }
              this.viewerErrorMessage = (err as HttpErrorResponse).message
              return of(undefined)
            })
          )

          const fetchExportDetails$ = this.pdfViewerFacade.fetchExportDetails(
            this.projectId,
            args.fileId
          )

          return forkJoin([
            of(args.exportId),
            fetchExportDetails$,
            fetchDocument$,
          ]).pipe(
            finalize(() => {
              this.cdr.markForCheck()
              this.isLoading = false
            })
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([exportId, exportResponse, blob]: [number, ResponseModel, Blob]) => {
          this.isLoading = false
          if (blob) {
            this.cdr.markForCheck()
            this.viewerErrorMessage = ''
            this.currentPdf = blob
            this.loadProducedPdf()
          }
          this.handleExportDetails(exportResponse, exportId)
        }
      )
  }

  private loadPdf(): void {
    this.loadPdfDocument(false)
  }

  private loadProducedPdf(): void {
    this.loadPdfDocument(true)
  }

  private loadPdfDocument(isProduced: boolean): void {
    NutrientViewer.unload('#pdfViewerApp')
    const documentUrl = URL.createObjectURL(this.currentPdf)

    // Prepare PSPDFKit load options
    const loadOptions: any = {
      baseUrl: this.applicationUrl + '/assets/',
      document: documentUrl,
      container: '#pdfViewerApp',
      XFDFKeepCurrentAnnotations: false,
      inlineTextSelectionToolbarItems: () => {
        return []
      },
      autoSaveMode: 'DISABLED',
      licenseKey: environment.pspdfkitLicense,
    }

    // Conditional options for non-produced PDF
    if (!isProduced) {
      loadOptions.XFDF = this.annotationData?.xfdf || undefined
      loadOptions.isEditableAnnotation = (annotation): any => {
        if (!this.showToolbars) return false
        return !this.annotationData?.annotationList?.find(
          (a) => a.name === annotation.name
        )?.isLocked
      }
    }

    // Load the PDF using PSPDFKit
    NutrientViewer.load(loadOptions).then((instance: Instance) => {
      this.cdr.markForCheck()
      this.viewerInstance = instance
      this.setViewState()
      this.registerViewerEvents()
      this.resetFlags()
      this.currentResultIndex = -1
      this.pageNumberList = this.getAllPageNumbers()
      if (this.selectedExportDetail?.imageFormat !== ImageExportFormat.Tiff)
        this.currentPageNumber = 1
      this.SearchAndHighlightTerms()
      this.#buildStampMapper()
      this.#initializeShortCutsInIframes()
    })
  }

  async #buildStampMapper(): Promise<void> {
    const pagesAnnotations = await Promise.all(
      Array.from({ length: this.viewerInstance.totalPageCount }).map(
        (_, pageIndex) => this.viewerInstance.getAnnotations(pageIndex)
      )
    )

    // Convert the filtered stamp annotations to a dictionary

    pagesAnnotations.forEach((pageAnnotations) => {
      pageAnnotations
        .filter(
          (annotation) =>
            annotation.customData &&
            annotation instanceof NutrientViewer.Annotations.StampAnnotation
        )
        .forEach((annotation) => {
          // Only add to the dictionary if it has a name
          if (annotation.name) {
            this.stampAnnotationMap[annotation.name] =
              annotation as StampAnnotation
          }
        })
    })
  }

  public resetFlags(): void {
    this.stampAnnotationMap = {}
    this.isAnnotationHidden = false
    this.isRedactedPagesOnly = false
    this.isOpaque = true
  }

  public setViewState(): void {
    this.viewerInstance.setViewState(
      this.viewerInstance.viewState.set('showToolbar', false)
    )
    this.viewerInstance.setViewState(
      this.viewerInstance.viewState.set('enableAnnotationToolbar', false)
    )

    // Apply the saved zoom level, default to FIT_TO_WIDTH if null/undefined
    const currentZoom = this.pdfViewerFacade.currentZoomLevel()
    const zoomLevel = currentZoom ?? NutrientViewer.ZoomMode.FIT_TO_WIDTH
    this.viewerInstance.setViewState(
      this.viewerInstance.viewState.set('zoom', zoomLevel)
    )

    this.viewerInstance.setAnnotationToolbarItems(() => [])
    this.viewerInstance.setViewState((viewState) => {
      return viewState.set('keepSelectedTool', true)
    })
  }

  public registerViewerEvents(): void {
    //this.viewerInstance.addEventListener('annotations.didSave', () => {})

    this.viewerInstance.addEventListener('annotations.willChange', (e) => {
      // Don't return the Promise - just execute the async function
      void (async (): Promise<void> => {
        //do not let user save the annotation if the annotations are made hidden by user
        if (this.isAnnotationHidden) return
        switch (e.reason) {
          case 'RESIZE_END':
          case 'DELETE_END':
          case 'MOVE_END': {
            this.pdfViewerFacade.setAnnotationChanged(true)
            await this.viewerInstance.save()
            break
          }
          case 'DRAW_END': {
            const annotation = e.annotations.get(0)

            if (
              e.reason ===
                NutrientViewer.AnnotationsWillChangeReason.DRAW_END &&
              this.viewerInstance.viewState.interactionMode?.includes(
                'REDACT_'
              ) &&
              annotation instanceof
                NutrientViewer.Annotations.RedactionAnnotation
            ) {
              await this.viewerInstance.delete(annotation)

              // Get annotation properties
              const boundingBox = annotation.boundingBox
              const pageIndex = annotation.pageIndex

              const myAnnotation =
                this.annotationHelper.getAnnotationByRedactionSet(
                  this.activeRedactionSet,
                  pageIndex,
                  boundingBox,
                  false
                )
              await this.viewerInstance.create([myAnnotation])
            }
            this.pdfViewerFacade.setAnnotationChanged(true)
            await this.viewerInstance.save()
            break
          }
          default:
            break
        }
      })()
    })
    this.viewerInstance.addEventListener(
      'viewState.currentPageIndex.change',
      (pageIndex) => {
        this.currentPageIndex = pageIndex
        this.cdr.markForCheck()
      }
    )
    this.viewerInstance.addEventListener(
      'annotations.create',
      (annotations) => {
        const arrAnnotation = annotations.toJS()
        if (arrAnnotation.length > 0 && arrAnnotation[0]?.customData) {
          if (
            arrAnnotation[0]?.customData &&
            'wholePageRedaction' in arrAnnotation[0].customData
          ) {
            this.saveWholePageRedactions()
          } else {
            if (arrAnnotation[0].stampType === 'Custom')
              this.stampAnnotationMap[arrAnnotation[0].name] =
                arrAnnotation[0] as StampAnnotation
            this.pdfViewerFacade.setAnnotationChanged(true)
          }
        }
      }
    )
  }

  private SearchAndHighlightTerms(): void {
    this.clearHighlights()
    const availablePrivilegedTerms = this.selectedHighlightGroupsTerms?.filter(
      (term) => term.privilegedTerm
    )
    let allSearchTerms = availablePrivilegedTerms.map((t) => t.privilegedTerm)

    const searchTerms = this.searchParameters.searchHighlightList.filter(
      (term) =>
        availablePrivilegedTerms.findIndex((t) => t.privilegedTerm === term) ===
        -1
    )
    allSearchTerms = [
      ...allSearchTerms,
      ...searchTerms,
      ...(this.similarTerms ?? []),
    ]
    if (allSearchTerms.length > 0) {
      const regexPattern = `\\b(${uniq([...allSearchTerms]).join('|')})\\b`
      if (this.viewerInstance) this.searchAndHighlight(regexPattern)
    }
  }

  private searchAndHighlight(regexPattern: string): void {
    this.viewerInstance
      .search(regexPattern, {
        searchType: NutrientViewer.SearchType.REGEX,
        caseSensitive: false,
      })
      .then((results) => {
        const annotations = results.map((result) => {
          const searchTerm = result.previewText
            .substring(
              result.locationInPreview,
              result.locationInPreview + result.lengthInPreview
            )
            .toLowerCase()

          const color = this.getSearchTermColor(searchTerm)
          return new NutrientViewer.Annotations.HighlightAnnotation({
            pageIndex: result.pageIndex,
            rects: result.rectsOnPage,
            color: this.getPSPDFKitColor(color),
            boundingBox: NutrientViewer.Geometry.Rect.union(result.rectsOnPage),
            opacity: 0.5,
          })
        })

        this.viewerInstance.create(annotations)

        // const newState = this.viewerInstance.searchState.set('results', results)
        // this.viewerInstance.setSearchState(newState)
        this.searchResults = results
        this.cdr.markForCheck()
      })
  }

  private getSearchTermColor(searchTerm: string): string {
    const isHighlightGroupTerm = this.selectedHighlightGroupsTerms.some(
      (t) => t.privilegedTerm === searchTerm
    )
    if (isHighlightGroupTerm)
      return this.selectedHighlightGroupsTerms.find(
        (t) => t.privilegedTerm === searchTerm.toLowerCase()
      )?.colorHexCode
    else if (
      this.searchParameters.searchHighlightList.some(
        (t) => t === searchTerm.toLowerCase()
      )
    ) {
      return '#ffff00'
    } else if (this.similarTerms.some((t) => t === searchTerm)) {
      return '#deecab'
    }
  }

  public navigateToFirstResult(): void {
    if (!this.searchResults) return
    this.currentResultIndex = 0
    const result = this.searchResults.get(this.currentResultIndex)
    this.focusSelectedTerm(result)
  }

  public navigateToLastResult(): void {
    if (!this.searchResults) return
    this.currentResultIndex = this.searchResults.size - 1
    const result = this.searchResults.get(this.currentResultIndex)
    this.focusSelectedTerm(result)
  }

  public navigateToNextResult(): void {
    if (!this.searchResults) return
    if (this.currentResultIndex < this.searchResults.size - 1) {
      this.currentResultIndex++

      const result = this.searchResults.get(this.currentResultIndex)
      this.focusSelectedTerm(result)
    }
  }

  public navigateToPrevResult(): void {
    if (!this.searchResults) return
    if (this.currentResultIndex > 0) {
      this.currentResultIndex--

      const result = this.searchResults.get(this.currentResultIndex)
      this.focusSelectedTerm(result)
    }
  }

  private focusSelectedTerm(result: SearchResult): void {
    this.findAnnotationIdFromRect(
      result.pageIndex,
      NutrientViewer.Geometry.Rect.union(result.rectsOnPage)
    ).then((annotation) => {
      try {
        const previousAnnot = this.previousSearchHighlight
        this.viewerInstance.update(previousAnnot)

        const updatedannot = annotation.set('color', NutrientViewer.Color.RED)
        this.previousSearchHighlight = annotation
        this.viewerInstance.update(updatedannot)
      } catch (err) {
        console.warn('Failed to update the highlight color', err)
      }
      const newState = this.viewerInstance.viewState.set(
        'currentPageIndex',
        result.pageIndex
      )

      this.viewerInstance.jumpAndZoomToRect(
        result.pageIndex,
        NutrientViewer.Geometry.Rect.union(result.rectsOnPage)
      )
      this.viewerInstance.setViewState(newState)
    })
  }

  private async findAnnotationIdFromRect(
    pageIndex,
    searchRect
  ): Promise<HighlightAnnotation> {
    const annotations = await this.viewerInstance.getAnnotations(pageIndex)

    for (const annotation of annotations) {
      if (
        annotation instanceof NutrientViewer.Annotations.HighlightAnnotation
      ) {
        const annotationRects = NutrientViewer.Geometry.Rect.union(
          annotation.rects
        )
        if (this.rectsAreEqual(annotationRects, searchRect)) {
          return annotation
        }
      }
    }

    return null
  }

  private rectsAreEqual(rect1: Rect, rect2: Rect, tolerance = 1e-2): boolean {
    return (
      Math.abs(rect1.left - rect2.left) < tolerance &&
      Math.abs(rect1.top - rect2.top) < tolerance &&
      Math.abs(rect1.width - rect2.width) < tolerance &&
      Math.abs(rect1.height - rect2.height) < tolerance
    )
  }

  private getPSPDFKitColor(hex: string): Color {
    const rgb = this.hexToRgb(hex)
    return new NutrientViewer.Color(rgb)
  }

  private hexToRgb(hex): { r: number; g: number; b: number } {
    hex = hex.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)
    return { r, g, b }
  }

  private clearHighlights(): void {
    for (let i = 0; i < this.viewerInstance?.totalPageCount; i++) {
      this.viewerInstance.getAnnotations(i).then((annotations) => {
        const highlights = annotations.filter(
          (ann) => ann instanceof NutrientViewer.Annotations.HighlightAnnotation
        )
        highlights.forEach((highlight) => {
          this.viewerInstance.delete([highlight.id])
        })
      })
    }
  }

  private openWholePageRedactionOption(): void {
    import('./whole-page-redaction/whole-page-redaction.component').then(
      (d) => {
        const dialog: DialogRef = this.dialogService.open({
          content: d.WholePageRedactionComponent,
          width: '32%',
          maxWidth: '500px',
          minWidth: '400px',
          maxHeight: '95vh',
        })

        const wprComponent = dialog.content.instance as WholePageRedaction
        wprComponent.highlights = this.redactionSets.filter(
          (s) => s.type.toLowerCase() === 'highlight'
        )
        wprComponent.solidRectangles = this.redactionSets.filter(
          (s) => s.type.toLowerCase() === 'solid rectangle'
        )
        wprComponent.pageCount = this.viewerInstance?.totalPageCount
          ? this.viewerInstance?.totalPageCount
          : 0
        wprComponent.currentPageNumber = this.currentPageIndex + 1

        dialog.result
          .pipe(
            filter((res) => !!res),
            take(1)
          )
          .subscribe((result: WholePageRedactionOptions) => {
            //when dialog is closed from header, result holds the dialogcloseresult which is not undefined. So, we need to check if the result is undefined or not.
            if (result?.redactionSet) this.redactWholePage(result)
          })
      }
    )
  }

  private openCopyRedactionOption(): void {
    this.cdr.markForCheck()
    import('./bulk-copy-redaction/bulk-copy-redaction.component').then((d) => {
      const dialog: DialogRef = this.dialogService.open({
        content: d.BulkCopyRedactionComponent,
        width: '55%',
        maxWidth: '750px',
        minWidth: '400px',
        maxHeight: '500px',
      })

      const bcrComponent = dialog.content.instance as BulkCopyRedaction
      bcrComponent.redactionSets = [
        ...this.redactionSets,
        ...this.highlightSets,
      ]
      bcrComponent.fileId = this.currentFileId
      bcrComponent.projectId = this.projectId
      dialog.result
        .pipe(
          filter((res) => !!res),
          take(1)
        )
        .subscribe((result) => {
          this.loadPdf()
        })
    })
  }

  public async actionButtonHandler(
    action: PageControlActionType
  ): Promise<void> {
    switch (action) {
      case PageControlActionType.PAN:
        this.viewerInstance.setViewState((viewState) =>
          viewState.set('interactionMode', NutrientViewer.InteractionMode.PAN)
        )
        break
      case PageControlActionType.DELETE:
        this.deleteAnnotations()
        break
      case PageControlActionType.REDACT_WHOLE_PAGE:
        this.openWholePageRedactionOption()
        break
      case PageControlActionType.COPY_REDACTION:
        this.openCopyRedactionOption()
        break
      case PageControlActionType.CREATE_REDACTION_SET:
        this.openCreateRedactionSet()
        break
      case PageControlActionType.OPACITY:
        this.changeOpacity()
        break
      case PageControlActionType.ENABLE_REDACTION:
        console.log('xfdf', this.viewerInstance.exportXFDF())
        this.showToolbars = !this.showToolbars
        if (this.isAnnotationHidden) await this.showHideAnnotation(false)
        break
      case PageControlActionType.HIDE_REDACTION:
        await this.showHideAnnotation(!this.isAnnotationHidden)
        break
      case PageControlActionType.ZOOM_IN:
        this.zoomIn()
        break
      case PageControlActionType.ZOOM_OUT:
        this.zoomOut()
        break

      case PageControlActionType.HIGHLIGHT_GROUP:
        this.showHighlightGroups()
        break

      case PageControlActionType.REDACTED_PAGES_ONLY:
        this.redactedPagesOnly()
        break
      case PageControlActionType.FIT_TO_WIDTH:
        this.viewerInstance.setViewState((viewstate) => {
          this.pdfViewerFacade.currentZoomLevel.set(
            NutrientViewer.ZoomMode.FIT_TO_WIDTH
          )
          return viewstate.set('zoom', NutrientViewer.ZoomMode.FIT_TO_WIDTH)
        })
        break
      case PageControlActionType.FIT_TO_HEIGHT:
        this.viewerInstance.setViewState((viewstate) => {
          this.pdfViewerFacade.currentZoomLevel.set(
            NutrientViewer.ZoomMode.FIT_TO_VIEWPORT
          )
          return viewstate.set('zoom', NutrientViewer.ZoomMode.FIT_TO_VIEWPORT)
        })
        break
      case PageControlActionType.PREV_PAGE:
        this.navigateToPreviousPage()
        break
      case PageControlActionType.NEXT_PAGE:
        this.navigateToNextPage()
        break
      case PageControlActionType.FIRST_PAGE:
        this.navigateToFirstPage()
        break
      case PageControlActionType.LAST_PAGE:
        this.navigateToLastPage()
        break
      case PageControlActionType.ACTUAL_SIZE:
        this.viewerInstance.setViewState((viewstate) => {
          this.pdfViewerFacade.currentZoomLevel.set(
            NutrientViewer.ZoomMode.AUTO
          )
          return viewstate.set('zoom', NutrientViewer.ZoomMode.AUTO)
        })
        break
      case PageControlActionType.ROTATE_LEFT:
        this.viewerInstance.setViewState((viewstate) => viewstate.rotateLeft())
        break
      case PageControlActionType.ROTATE_RIGHT:
        this.viewerInstance.setViewState((viewstate) => viewstate.rotateRight())
        break
      case PageControlActionType.REFRESH:
        if (this.selectedImage === 'OriginalImage') {
          this.documentFacade.loadPDF = {
            fileId: this.currentFileId,
            isRefresh: true,
          }
        } else if (this.selectedImage === 'ProducedImage') {
          this.documentFacade.loadProducedPDF = {
            fileId: this.currentFileId,
            exportId: this.selectedExportId,
            imageFileFormat: this.selectedExportDetail.imageFormat,
            isRefresh: true,
          }
        }
        break
      case PageControlActionType.SAVE:
        this.saveAnnotationHandler()
          .then(() => {
            this.notification.showSuccess('Annotation saved successfully')
          })
          .catch((err) =>
            this.notification.showError('Failed to save annotation')
          )

        break
      case PageControlActionType.HIGHLIGHT_FIRST:
        this.navigateToFirstResult()
        break
      case PageControlActionType.HIGHLIGHT_PREVIOUS:
        this.navigateToPrevResult()
        break
      case PageControlActionType.HIGHLIGHT_NEXT:
        this.navigateToNextResult()
        break
      case PageControlActionType.HIGHLIGHT_LAST:
        this.navigateToLastResult()
        break
      default:
        break
    }
    this.viewerInstance?.setAnnotationToolbarItems(() => [])
  }

  private async changeOpacity(): Promise<void> {
    this.cdr.markForCheck()
    this.isOpaque = !this.isOpaque
    await this.addRemoveOpacity(this.isOpaque)
  }

  private async addRemoveOpacity(isOpaque: boolean): Promise<void> {
    const pagesAnnotations = await Promise.all(
      Array.from({ length: this.viewerInstance.totalPageCount }).map(
        (_, pageIndex) => this.viewerInstance.getAnnotations(pageIndex)
      )
    )
    await Promise.allSettled(
      pagesAnnotations.flatMap((pageAnnotations) =>
        pageAnnotations
          .filter(
            (annotation) =>
              annotation.customData &&
              this.redactionSets.some(
                (s) => s.id === annotation.customData.objectId
              )
          )
          .map((annotation) => {
            return (async (): Promise<void> => {
              const stampMapper = this.stampAnnotationMap
              if (
                annotation.name in stampMapper ||
                annotation instanceof NutrientViewer.Annotations.StampAnnotation
              ) {
                if (!isOpaque) {
                  const rectangleAnnotation =
                    new NutrientViewer.Annotations.RectangleAnnotation({
                      strokeColor: NutrientViewer.Color.TRANSPARENT,
                      strokeWidth: 0,
                      font: 'Helvetica',
                      isBold: true,
                      fillColor: this.getPSPDFColorByObjectId(
                        Number(annotation.customData.objectId)
                      ),
                      color: NutrientViewer.Color.BLACK,
                      name: annotation.name,
                      pageIndex: annotation.pageIndex,
                      customData: annotation.customData,
                      opacity: isOpaque ? 1 : 0.5,
                      boundingBox: annotation.boundingBox,
                      locked: annotation.locked,
                    })

                  this.viewerInstance.delete([annotation.id])
                  this.viewerInstance.create([rectangleAnnotation])
                } else {
                  if (stampMapper[annotation.name]) {
                    const stampAnnotation = stampMapper[annotation.name]
                    const stampAnn =
                      new NutrientViewer.Annotations.StampAnnotation({
                        stampType: 'Custom',
                        title: stampAnnotation.title,
                        boundingBox: annotation.boundingBox,
                        pageIndex: annotation.pageIndex,
                        color: this.getPSPDFColorByObjectId(
                          Number(stampAnnotation.customData.objectId)
                        ),
                        customData: stampAnnotation.customData,
                        name: stampAnnotation.name,
                        locked: stampAnnotation.locked,
                      })

                    await this.viewerInstance.delete([annotation.id])
                    await this.viewerInstance.create([stampAnn])
                    await this.viewerInstance.save()
                  }
                }
              } else {
                try {
                  this.viewerInstance.update(
                    annotation.set('opacity', isOpaque ? 1 : 0.5)
                  )
                } catch (err) {
                  console.warn('Failed to update the opacity', err)
                }
              }
            })()
          })
      )
    )
  }

  private getPSPDFColorByObjectId(redactionSetId: number): Color {
    const color = this.redactionSets.find(
      (f) => f.id === redactionSetId
    ).backColor
    return this.annotationHelper.getPSPPDFKitColor(color)
  }

  private async showHighlightGroups(): Promise<void> {
    this.showHighlightGroup = true
    this.highlightGroupVCR.clear()
    //if (!this.isManageReasonVisible) return
    const { PDFHighlightGroupComponent } = await import(
      './highlight-group/highlight-group.component'
    )
    const highlightGroupComponentRef = this.highlightGroupVCR.createComponent(
      PDFHighlightGroupComponent
    )
    highlightGroupComponentRef.instance.projectId = this.projectId
    highlightGroupComponentRef.instance.selectedHighlightGroups =
      this.selectedHighlightGroups

    this.cdr.markForCheck()
  }

  private redactedPagesOnly(): void {
    this.isRedactedPagesOnly = !this.isRedactedPagesOnly
    if (this.isRedactedPagesOnly) {
      this.pdfViewerFacade
        .getRedactedPages(this.projectId, this.currentFileId)
        .pipe(take(1), takeUntil(this.toDestroy$))
        .subscribe((response: ResponseModel) => {
          this.cdr.markForCheck()
          const pages = response.data
          if (pages.length > 0) {
            this.pageNumberList = pages
            this.navigateToPage(pages[0])
          } else {
            this.pageNumberList = this.getAllPageNumbers()
            this.isRedactedPagesOnly = false
            this.notification.showWarning('No redacted pages found')
          }
        })
    } else {
      this.pageNumberList = this.getAllPageNumbers()
    }
  }

  private getAllPageNumbers(): number[] {
    if (this.selectedImage === 'OriginalImage')
      return Array.from(
        {
          length: this.viewerInstance.totalPageCount,
        },
        (_, index) => index + 1
      )

    return this.selectedExportDetail.pageNumbers
  }

  private async deleteAnnotations(): Promise<void> {
    const selectedAnnotationIds = this.viewerInstance
      .getSelectedAnnotations()
      ?.map((ann) => ann.id)
      ?.toArray()
    if (selectedAnnotationIds?.length > 0) {
      await this.viewerInstance.delete(selectedAnnotationIds)

      this.pdfViewerFacade.setAnnotationChanged(true)
    }
  }

  private openCreateRedactionSet(): void {
    this.cdr.markForCheck()
    import('./create-redaction-set/create-redaction-set.component').then(
      (d) => {
        const dialog: DialogRef = this.dialogService.open({
          content: d.CreateRedactionSetComponent,
          width: '32%',
          maxWidth: '500px',
          minWidth: '400px',
          maxHeight: '500px',
        })

        const bcrComponent = dialog.content.instance as CreateRedactionSet
        bcrComponent.projectId = this.projectId
        dialog.result
          .pipe(
            filter((res) => !!res),
            switchMap(() =>
              this.pdfViewerFacade.fetchRedactionSets(this.projectId)
            ),
            take(1)
          )
          .subscribe((result: ResponseModel) => {
            this.categorizeRedactionSets(result.data)
          })
      }
    )
  }

  private zoomIn(): void {
    this.viewerInstance.setViewState((viewState) => viewState.zoomIn())
    this.pdfViewerFacade.currentZoomLevel.set(
      this.viewerInstance.viewState.get('zoom')
    )
  }

  private zoomOut(): void {
    this.viewerInstance.setViewState((viewState) => viewState.zoomOut())
    this.pdfViewerFacade.currentZoomLevel.set(
      this.viewerInstance.viewState.get('zoom')
    )
  }

  private async redactWholePage(
    options: WholePageRedactionOptions
  ): Promise<void> {
    this.wholePageRedactionSet = this.redactionSets.find(
      (f) => f.id === options.redactionSet
    )
    this.getPageInfo(options)

    const firstKey = Object.keys(this.pagesInfo)[0]
    const annotation: Annotation = this.getWholePageAnnotationObject(
      this.wholePageRedactionSet,
      this.currentPageIndex,
      this.pagesInfo[firstKey]
    )

    await this.viewerInstance.create([annotation])
  }

  private getPageInfo(options: WholePageRedactionOptions): void {
    this.pagesInfo = {}
    if (options.pageSelection === PageSelectionOption.ALL_PAGES) {
      for (let i = 0; i < this.viewerInstance.totalPageCount; i++) {
        this.pagesInfo[i] = {
          height: this.viewerInstance.pageInfoForIndex(i).height,
          width: this.viewerInstance.pageInfoForIndex(i).width,
        }
      }
    } else if (options.pageSelection === PageSelectionOption.PAGE_RANGE) {
      for (let i = options.pageFrom - 1; i <= options.pageTo - 1; i++) {
        this.pagesInfo[i] = {
          height: this.viewerInstance.pageInfoForIndex(i).height,
          width: this.viewerInstance.pageInfoForIndex(i).width,
        }
      }
    } else if (options.pageSelection === PageSelectionOption.PAGE_EXCEPT) {
      for (let i = 0; i < this.viewerInstance.totalPageCount; i++) {
        if (i !== options.exceptPage - 1) {
          this.pagesInfo[i] = {
            height: this.viewerInstance.pageInfoForIndex(i).height,
            width: this.viewerInstance.pageInfoForIndex(i).width,
          }
        }
      }
    } else {
      this.pagesInfo[this.currentPageIndex] = {
        height: this.viewerInstance.pageInfoForIndex(this.currentPageIndex)
          .height,
        width: this.viewerInstance.pageInfoForIndex(this.currentPageIndex)
          .width,
      }
    }
  }

  private async saveWholePageRedactions(): Promise<void> {
    await this.viewerInstance.save()
    const xfdf = await this.viewerInstance.exportXFDF()

    const annotationExtractorHandler = new Worker(
      new URL(
        '../worker/whole-page-annotation-extractor.worker',
        import.meta.url
      ),
      { type: 'module' }
    )
    const obj: Remote<AnnotationExtractor> = wrap<AnnotationExtractor>(
      annotationExtractorHandler
    )
    const annotationData = await obj(xfdf)
    //release the worker
    obj[releaseProxy]()

    const payload: WholePageRedactionPayload = {
      xfdf: annotationData,
      redactionSet: this.wholePageRedactionSet,
      pagesInfo: this.pagesInfo,
    }
    this.pdfViewerFacade
      .addWholePageRedaction(this.projectId, this.currentFileId, payload)
      .pipe(take(1))
      .subscribe((annotationResponse: ResponseModel) => {
        this.annotationData = annotationResponse.data
        this.loadPdf()
      })
  }

  private getWholePageAnnotationObject(
    selectedRedaction: RedactionSet,
    pageIndex: number,
    pageInfo: PdfPageInfo
  ): Annotation {
    const boundingBox = new NutrientViewer.Geometry.Rect({
      left: 0,
      top: 0,
      width: pageInfo.width,
      height: pageInfo.height,
    })

    const annotation: Annotation =
      this.annotationHelper.getAnnotationByRedactionSet(
        selectedRedaction,
        pageIndex,
        boundingBox,
        true
      )
    return annotation
  }

  public async onRedactionSelected(
    selectedRedaction: RedactionSet
  ): Promise<void> {
    this.viewerInstance.setViewState((viewState) =>
      viewState.set('interactionMode', null)
    )
    this.activeRedactionSet = selectedRedaction
    const preset = this.viewerInstance.annotationPresets

    if (!selectedRedaction.caption) {
      preset['rectangle'] = {
        ...preset['rectangle'],
        strokeColor: NutrientViewer.Color.TRANSPARENT,
        strokeWidth: 0,
        font: 'Helvetica',
        isBold: true,
        fillColor: this.annotationHelper.getPSPPDFKitColor(
          selectedRedaction.backColor
        ),
        color: this.annotationHelper.getPSPPDFKitColor(
          selectedRedaction.backColor
        ),
        opacity: selectedRedaction.type === 'Highlight' ? 0.5 : 1,
        customData: { objectId: selectedRedaction.id },
      }
      this.viewerInstance.setAnnotationPresets(preset)
      this.viewerInstance.setCurrentAnnotationPreset('rectangle')
      this.viewerInstance.setViewState((viewState) =>
        viewState.set(
          'interactionMode',
          NutrientViewer.InteractionMode.SHAPE_RECTANGLE
        )
      )
    } else {
      preset['redaction'] = {
        ...preset['redaction'],
        color: NutrientViewer.Color.BLACK,
        outlineColor: NutrientViewer.Color.BLACK,
        fillColor: NutrientViewer.Color.BLACK,
        opacity: 1,
        overlayText: 'Redacted',
      }
      this.viewerInstance.setAnnotationPresets(preset)
      this.viewerInstance.setCurrentAnnotationPreset('redaction')
      this.viewerInstance.setViewState((viewState) =>
        viewState.set(
          'interactionMode',
          NutrientViewer.InteractionMode.REDACT_SHAPE_RECTANGLE
        )
      )
    }
    this.viewerInstance.setAnnotationToolbarItems(() => [])
  }

  private async saveAnnotationHandler(): Promise<void> {
    try {
      await this.addRemoveOpacity(true)
      await this.viewerInstance.save()

      const xfdf = await this.viewerInstance.exportXFDF()

      const annotationFilterHandler = new Worker(
        new URL(
          '../worker/filter-non-venio-annotations.worker',
          import.meta.url
        ),
        { type: 'module' }
      )
      const obj: Remote<AnnotationFilter> = wrap<AnnotationFilter>(
        annotationFilterHandler
      )

      try {
        const filteredXfdf = await obj(xfdf)
        this.saveAnnotation(filteredXfdf)
      } finally {
        obj[releaseProxy]() // always release worker
      }
    } catch (error: unknown) {
      console.error('Error saving annotation:', error)
    }
  }

  private saveAnnotation(xfdf: string): void {
    const payload: PdfSaveAnnotationPayload = { xfdfString: xfdf }
    this.pdfViewerFacade
      .savePdfAnnotation(this.projectId, this.currentFileId, payload)
      .pipe(
        take(1),
        catchError((err: unknown) => {
          this.pdfViewerFacade.saveActionCompleteAction.next()
          return throwError(() => err)
        })
      )
      .subscribe(() => {
        this.#sendNotificaitonToDocumentHistory()
        this.pdfViewerFacade.saveActionCompleteAction.next()
      })
  }

  private async showHideAnnotation(hideAnnotation: boolean): Promise<void> {
    this.isAnnotationHidden = hideAnnotation
    if (hideAnnotation) this.showToolbars = false
    const pagesAnnotations = await Promise.all(
      Array.from({ length: this.viewerInstance.totalPageCount }).map(
        (_, pageIndex) => this.viewerInstance.getAnnotations(pageIndex)
      )
    )
    await Promise.allSettled(
      pagesAnnotations.flatMap((pageAnnotations) =>
        pageAnnotations
          .filter((annotation) => annotation.customData)
          .map((annotation) => {
            try {
              return this.viewerInstance.update(
                annotation
                  .set('noView', this.isAnnotationHidden)
                  .set('noPrint', this.isAnnotationHidden)
              )
            } catch (err) {
              console.warn('Failed to update the annotation', err)
            }
          })
      )
    )
  }

  public navigateToNextPage(): void {
    const currentIndex = this.pageNumberList.indexOf(this.currentPageNumber)
    if (currentIndex + 1 < this.pageNumberList.length) {
      const nextPageNumber = this.pageNumberList[currentIndex + 1]
      this.navigateToPage(nextPageNumber)
    }
  }

  public navigateToPreviousPage(): void {
    const currentIndex = this.pageNumberList.indexOf(this.currentPageNumber)
    if (currentIndex - 1 >= 0) {
      const previousPageNumber = this.pageNumberList[currentIndex - 1]
      this.navigateToPage(previousPageNumber)
    }
  }

  public navigateToFirstPage(): void {
    if (this.pageNumberList.length > 0)
      this.navigateToPage(this.pageNumberList[0])
  }

  public navigateToLastPage(): void {
    this.navigateToPage(this.pageNumberList[this.pageNumberList.length - 1])
  }

  public navigateToPage(pageNumber: number): void {
    if (
      this.selectedImage === 'OriginalImage' ||
      (this.selectedImage === 'ProducedImage' &&
        this.selectedExportDetail.imageFormat === ImageExportFormat.Pdf)
    ) {
      if (pageNumber > this.viewerInstance.totalPageCount)
        pageNumber = this.viewerInstance.totalPageCount
      else if (pageNumber < 1) pageNumber = 1
    } else {
      this.documentFacade.loadProducedPDF = {
        fileId: this.currentFileId,
        isRefresh: false,
        imageFileFormat: this.selectedExportDetail.imageFormat,
        exportId: this.selectedExportId,
        pageNumber: pageNumber,
      }
    }
    if (
      this.selectedImage === 'OriginalImage' ||
      this.selectedExportDetail.imageFormat === ImageExportFormat.Pdf
    ) {
      this.viewerInstance.setViewState((viewState) =>
        viewState.set('currentPageIndex', pageNumber - 1)
      )
    } else this.tiffPageNumber = pageNumber

    this.cdr.markForCheck()
  }

  public getCurrentPageIndex(): number {
    return this.pageNumberList.indexOf(this.currentPageNumber) + 1
  }

  #sendNotificaitonToDocumentHistory(): void {
    if (!this.isViewerPanelPopout) {
      this.reviewPanelFacade.refrehDocumentHistory(
        UpdateDocumentHistoryPage.UPDATE_PDF_REDACTION
      )
      return
    }
    this.#sendReactionNotificationToParentWindow()
  }

  /**
   * Sends a redaction notification to the parent window.
   * @returns {void} This method does not return anything.
   */
  #sendReactionNotificationToParentWindow(): void {
    if (!this.isViewerPanelPopout) return
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      payload: {
        type: MessageType.REDACTION_UPDATE,
        content: {
          isRefreshPage: true,
        },
      },
      eventTriggeredFor: 'ALL_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  public onSelectionChange(selectedValue: string): void {
    this.resetPageNumber()
    if (selectedValue === 'OriginalImage') {
      // Handle Original Image selection logic
      this.documentFacade.loadPDF = {
        fileId: this.currentFileId,
        isRefresh: true,
      }
      this.documentFacade.loadExportDetails = {
        exportId: this.selectedExportId,
        imageType: ImageType.OriginalImage,
      }
    } else if (selectedValue === 'ProducedImage') {
      //save the annotation of original image if required.
      this.pdfViewerFacade.saveAnnotationAction.next()
      this.showToolbars = false
      this.documentFacade.loadExportDetails = {
        exportId: this.selectedExportId,
        imageType: ImageType.ProducedImage,
      }
      this.documentFacade.loadProducedPDF = {
        fileId: this.currentFileId,
        exportId: this.selectedExportId,
        imageFileFormat: this.selectedExportDetail.imageFormat,
        isRefresh: true,
      }
    }
  }

  public onExportChange(selectedValue: number): void {
    this.resetPageNumber()
    this.selectedExportId = selectedValue
    this.documentFacade.loadProducedPDF = {
      fileId: this.currentFileId,
      exportId: this.selectedExportId,
      imageFileFormat: this.selectedExportDetail.imageFormat,
      isRefresh: true,
    }
    this.documentFacade.loadExportDetails = {
      exportId: this.selectedExportId,
      imageType: ImageType.ProducedImage,
    }
  }

  private resetPageNumber(): void {
    this.tiffPageNumber = 1
    this.currentPageNumber = 1
  }
}
