import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  RepositoryHierarchyModel,
  RepositoryModel,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import {
  RepositoryBrowserStateService,
  ReprocessingFacade,
} from '@venio/data-access/common'
import {
  LoaderModule,
  SkeletonComponent,
} from '@progress/kendo-angular-indicators'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  fileIcon,
  moreVerticalIcon,
  SVGIcon,
  xIcon,
} from '@progress/kendo-svg-icons'
import { filter, map, Observable, Subject, take } from 'rxjs'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  CheckableSettings,
  TreeItem,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'

@Component({
  selector: 'venio-repository-browser',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    TreeViewModule,
    GridModule,
    LoaderModule,
    IconsModule,
    LabelModule,
    FormsModule,
    ButtonsModule,
    SvgLoaderDirective,
    TooltipModule,
    SkeletonComponent,
  ],
  templateUrl: './repository-browser.component.html',
  styleUrl: './repository-browser.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RepositoryBrowserComponent implements OnInit, OnDestroy {
  public unsubscribe$ = new Subject<void>()

  private reprocessingFacade = inject(ReprocessingFacade)

  public repoStateService = inject(RepositoryBrowserStateService)

  public isForReprocessing = input<boolean>(false)

  public selectedReplacementFile = output<RepositoryHierarchyModel>({})

  public selectedReplacementFiles = output<RepositoryHierarchyModel[]>()

  public isRepositoryListLoading = signal<boolean>(false)

  public repositoryList = signal<RepositoryModel[]>([])

  public filteredRepositoryList = signal<RepositoryModel[]>([])

  public _reposiotoryList = toSignal(this.reprocessingFacade.getRepositoryList$)

  public selectedRepositoryKeys = signal<number[]>([])

  public selectedRepository: RepositoryModel[] = []

  public searchTerm = ''

  public isHierarchyLoading = signal<boolean>(false)

  //public repositoryHierarchy: RepositoryHierarchyModel[] = []

  public _repositoryHierarchy = toSignal(
    this.reprocessingFacade.getRepositoryHierarchy$
  )

  public checkedFileFoldersIds = []

  public filterTerm = ''

  public checkableSettings: CheckableSettings = {
    checkChildren: true,
    checkDisabledChildren: true,
    checkParents: true,
    enabled: false, //this.isForReprocessing() ? false : true,
    mode: !this.isForReprocessing() ? 'multiple' : 'single',
    checkOnClick: true,
    uncheckCollapsedChildren: false,
  }

  public icons = {
    closeIcon: xIcon,
    moreVerticalIcon: moreVerticalIcon,
    fileIcon: fileIcon,
  }

  public getFileIcon(): SVGIcon {
    return this.icons.fileIcon
  }

  constructor() {
    this.fetchLoadRepositoryListEffect()
    this.fetchHierarchyListEffect()
  }

  public fetchLoadRepositoryListEffect(): void {
    effect(
      () => {
        if (this._reposiotoryList() !== null) {
          if (this._reposiotoryList()?.length > 0) {
            this.repositoryList.set(this._reposiotoryList())
            this.filteredRepositoryList.set(this._reposiotoryList())

            if (this.selectedRepositoryKeys().length === 0) {
              this.selectedRepositoryKeys.set([this.repositoryList()[0].fsid])
              if (
                this.selectedRepository.findIndex(
                  (x) => x.fsid === this.repositoryList()[0].fsid
                ) === -1
              ) {
                this.selectedRepository.push(this.repositoryList()[0])
              }

              this.isHierarchyLoading.set(true)
              this.reprocessingFacade.fetchRepositoryHeirarchy(
                this.selectedRepositoryKeys(),
                false,
                false,
                'none',
                0,
                false
              )
            }
            this.isRepositoryListLoading.set(false)
          } else {
            this.repositoryList.set([])
            this.filteredRepositoryList.set([])
            this.isRepositoryListLoading.set(false)
          }
        }
      },
      { allowSignalWrites: true }
    )
  }

  public fetchHierarchyListEffect(): void {
    effect(
      () => {
        if (this._repositoryHierarchy() !== null) {
          const rootNodes = this.getFileFolderHierarchyFromSelectedRepo(
            this._repositoryHierarchy()
          )
          this.repoStateService.filteredRepositoryHierarchy.set(rootNodes)
          this.isHierarchyLoading.set(false)
        }
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnInit(): void {
    this.checkableSettings = {
      checkChildren: true,
      checkDisabledChildren: true,
      checkParents: true,
      enabled: this.isForReprocessing() ? false : true,
      mode: !this.isForReprocessing() ? 'multiple' : 'single',
      checkOnClick: true,
      uncheckCollapsedChildren: false,
    }
    this.fetchLoadRepositoryList()
  }

  private fetchLoadRepositoryList(): void {
    this.isRepositoryListLoading.set(true)
    this.reprocessingFacade.fetchReposirotyList(true)
  }

  private getFileFolderHierarchyFromSelectedRepo(
    data: RepositoryHierarchyModel[]
  ): RepositoryHierarchyModel[] {
    if (data?.length > 0) {
      const updated = data.map(
        (node) =>
          ({
            ...node,
            parentId: node.parentId === '-1' ? null : node.parentId,
          } as RepositoryHierarchyModel)
      )
      const children = updated.filter((node) => node.parentId !== null)
      children.forEach((child) => {
        if (child.type === 'FOLDER')
          updated.push(this.repoStateService.createDummyChildNode(child))
      })
      return updated
    }
    return []
  }

  private createRepositoryHierarchyObservable(
    item: any
  ): Observable<RepositoryHierarchyModel[]> {
    return this.reprocessingFacade
      .fetchSpecificRepositoryHeirarchy(
        Number(item.fsid),
        item,
        false,
        false,
        'none',
        0
      )
      .pipe(
        filter(() =>
          this.repoStateService
            .filteredRepositoryHierarchy()
            .some(
              (node) =>
                node.parentId === item.id && node.id === `${item.id}_dummy`
            )
        ),
        map((response: ResponseModel) => {
          if (!Array.isArray(response?.data) || response.data.length === 0) {
            item.hasChildren = false
            return []
          }

          return response.data.map(
            (node: RepositoryHierarchyModel): RepositoryHierarchyModel => ({
              ...node,
              parentId: node.parentId === '-1' ? null : node.parentId,
              hasChildren:
                node.type === 'FOLDER' ||
                response.data.some((child) => child.parentId === node.id),
              isExpanded: false,
            })
          )
        })
      )
  }

  public onFolderExpand(event): void {
    const item = event.dataItem as RepositoryHierarchyModel
    const repositoryHierarchy$ = this.createRepositoryHierarchyObservable(item)
    repositoryHierarchy$.pipe(take(1)).subscribe((data) => {
      //const foldersOnly = data.filter((f) => f.type === 'FOLDER')
      this.repoStateService.createDummyNodesOfChildren(item, data)
    })
  }

  public addFileFolder(): void {
    const repos = this.repoStateService.getSelectedFilesFolderWithoutRepeating(
      this.checkedFileFoldersIds
    )

    this.selectedReplacementFiles.emit(repos)
    this.checkedFileFoldersIds = []
  }

  public onFileSelectionChange(event: TreeItem): void {
    const data = event.dataItem
    if (this.isForReprocessing()) {
      if (data) {
        if (data?.type !== 'FOLDER') {
          this.selectedReplacementFile.emit(data)
        }
      }
    }
  }

  public onSelectionChange(event: any): void {
    this.isHierarchyLoading.set(true)
    event.deselectedRows?.forEach((row) => {
      if (
        this.selectedRepository.findIndex(
          (x) => x.fsid === row.dataItem.fsid
        ) >= 0
      ) {
        this.selectedRepository.splice(
          this.selectedRepository.findIndex(
            (x) => x.fsid === row.dataItem.fsid
          ),
          1
        )
      }
    })

    event.selectedRows?.forEach((row) => {
      if (
        this.selectedRepository.findIndex(
          (x) => x.fsid === row.dataItem.fsid
        ) === -1
      ) {
        this.selectedRepository.push(row.dataItem)
      }
    })

    if (this.selectedRepository.length > 0) {
      this.reprocessingFacade.fetchRepositoryHeirarchy(
        this.selectedRepository.map((x) => x.fsid),
        false,
        false,
        'none',
        0,
        false
      )
    } else {
      this.isHierarchyLoading.set(false)
      this.reprocessingFacade.clearRepositoryHierarchy()
    }
  }

  public onRepositorySearch(event): void {
    this.searchTerm = event.trim().toLowerCase()

    if (this.searchTerm) {
      const filteredData = this.repositoryList().filter((repo) =>
        repo.fsDisplayName.toLowerCase().includes(this.searchTerm)
      )
      this.filteredRepositoryList.set(filteredData)
    } else {
      this.filteredRepositoryList.set(this.repositoryList())
    }
  }

  public refreshRepositoryList(): void {
    this.reprocessingFacade.fetchReposirotyList(true)
  }

  public noRecordsAvailable = (): boolean => {
    return this.selectedRepository.length === 0
  }

  public ngOnDestroy(): void {
    this.reprocessingFacade.clearRepositoryHierarchy()
  }
}
