import { inject, Injectable } from '@angular/core'
import { Store } from '@ngrx/store'
import * as UploadActions from './upload.actions'
import * as UploadSelectors from './upload.selectors'
import { UploadState } from './upload.reducer'
import {
  CaseDetailModel,
  MediaSourceType,
  NsfUserIdFile,
  QueuedRepositoryModel,
  SelectedFileMetaInfo,
  UploadSourceSelection,
  UploadSourceTypes,
} from '@venio/shared/models/interfaces'
import { BehaviorSubject, Observable, map } from 'rxjs'
import { MediaSourceTypeMapper } from './media-source-type-mapper'

type UploadStateKeys = keyof UploadState | Array<keyof UploadState>
@Injectable({ providedIn: 'root' })
export class UploadFacade {
  public validationMessage$: BehaviorSubject<any> = new BehaviorSubject<any>(
    undefined
  )

  private readonly store = inject(Store)

  public readonly selectStepperActiveIndex$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('activeStepperIndex')
  )

  public readonly selectUploadSourceSelection$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('uploadSourceSelection')
  )

  public readonly selectSelectedCaseInfo$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('selectedCaseInfo')
  )

  public readonly selectProcessFromRepositorySuccessResponse$ =
    this.store.select(
      UploadSelectors.getStateFromUploadStore('processFromRepositorySuccess')
    )

  public readonly selectProcessFromRepositoryErrorResponse$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('processFromRepositoryError')
  )

  public readonly selectFileFolderForUpload$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('fileFolderToUpload')
  )
  /**
   * Checks if the current upload source is set to repository
   * @returns Observable<boolean> indicating if repository is selected
   */
  public selectIsRepositoryUpload(): Observable<boolean> {
    return this.store
      .select(UploadSelectors.getStateFromUploadStore('uploadSourceSelection'))
      .pipe(
        map((sourceOption: UploadSourceSelection) => {
          return sourceOption?.mediaSourceType?.value === 'REPOSITORY'
        })
      )
  }

  /**
   * Gets the current upload source type
   * @returns Observable<UploadSourceTypes> with the current source type
   */
  public selectUploadSourceType(): Observable<UploadSourceTypes> {
    return this.store
      .select(UploadSelectors.getStateFromUploadStore('uploadSourceSelection'))
      .pipe(
        map((sourceOption: UploadSourceSelection) => {
          return sourceOption?.sourceType
        })
      )
  }

  /**
   * Gets the appropriate MediaSourceType based on the current upload source selection
   * @returns Observable<MediaSourceType> with the appropriate media source type
   */
  public selectMediaSourceType(): Observable<MediaSourceType> {
    return this.store
      .select(UploadSelectors.getStateFromUploadStore('uploadSourceSelection'))
      .pipe(
        map((sourceOption: UploadSourceSelection) => {
          return MediaSourceTypeMapper.mapToMediaSourceType(sourceOption)
        })
      )
  }

  public readonly selectCustodians$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('custodians')
  )

  public readonly selectSelectedFileMetaInfo$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('selectedFileMetaInfo')
  )

  public readonly selectCustomCustodian$ = this.store.select(
    UploadSelectors.getStateFromUploadStore('customCustodians')
  )

  public storeSelectedCaseInfo(selectedCaseInfo: CaseDetailModel): void {
    this.store.dispatch(
      UploadActions.StoreSelectedCaseInfo({ selectedCaseInfo })
    )
  }

  public addFileFolderForUpload(fileFolder: QueuedRepositoryModel): void {
    this.store.dispatch(UploadActions.addFileFolder({ fileFolder }))
  }

  public updateFileFolderForUpload(fileFolder: QueuedRepositoryModel): void {
    this.store.dispatch(UploadActions.updateFileFolder({ fileFolder }))
  }

  public updateFileFolderForUploadInBulk(
    fileFolders: QueuedRepositoryModel[]
  ): void {
    this.store.dispatch(UploadActions.updateFileFolderInBulk({ fileFolders }))
  }

  public removeFileFolder(folderId: string): void {
    this.store.dispatch(UploadActions.removeFileFolder({ folderId }))
  }

  public removeQueuedMedia(queuedMediaId: string): void {
    this.store.dispatch(UploadActions.removeQueuedMedia({ queuedMediaId }))
  }

  public clearFileFolder(): void {
    this.store.dispatch(UploadActions.clearFileFolder())
  }

  public fetchCustodians(projectId: number): void {
    this.store.dispatch(UploadActions.fetchCustodians({ projectId }))
  }

  /**
   * Resetting a specific property of the User State
   * @param {UploadStateKeys} stateKey - One or more properties of the Upload State
   *
   * @example
   * // Resetting states
   * this.uploadFacade.resetUploadState('x') // single property
   * this.uploadFacade.resetUploadState(['x', 'y']) // multiple property
   * @returns {void}
   */
  public resetUploadState(stateKey: UploadStateKeys): void {
    this.store.dispatch(UploadActions.resetUploadState({ stateKey }))
  }

  public updateUploadSourceSelection(
    uploadSourceSelection: Partial<UploadSourceSelection>
  ): void {
    this.store.dispatch(
      UploadActions.updateUploadSourceSelection({ uploadSourceSelection })
    )
  }

  public processFromRepository(
    projectId: number,
    repositoryFiles: QueuedRepositoryModel[],
    nsfUserIdFiles: NsfUserIdFile[]
  ): void {
    this.store.dispatch(
      UploadActions.processFromRepository({
        projectId,
        repositoryFiles,
        nsfUserIdFiles,
      })
    )
  }

  public updateActiveStepperIndex(activeStepperIndex: number): void {
    this.store.dispatch(
      UploadActions.updateActiveStepperIndex({ activeStepperIndex })
    )
  }

  public storeSelectedFileMetaInfo(
    selectedFileMetaInfo: SelectedFileMetaInfo | SelectedFileMetaInfo[]
  ): void {
    this.store.dispatch(
      UploadActions.storeSelectedFileMetaInfo({ selectedFileMetaInfo })
    )
  }

  public storeCustomCustodian(customCustodianName: string): void {
    this.store.dispatch(
      UploadActions.storeCustomCustodian({ customCustodianName })
    )
  }

  public updateSelectedFileMetaInfo(
    selectedFileMetaInfo: SelectedFileMetaInfo
  ): void {
    this.store.dispatch(
      UploadActions.updateSelectedFileMetaInfo({ selectedFileMetaInfo })
    )
  }

  public removeSelectedFileMetaInfo(fileId: string | string[]): void {
    this.store.dispatch(UploadActions.removeSelectedFileMetaInfo({ fileId }))
  }
}
