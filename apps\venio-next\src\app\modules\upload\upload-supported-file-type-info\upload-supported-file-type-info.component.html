<kendo-dialog-titlebar>
  <div class="t-flex t-flex-row t-justify-center t-items-center">
    <div class="t-flex t-flex-row t-mr-[20px]">
      <div
        class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-flex t-items-center t-justify-center t-rounded-full">
        <span
          venioSvgLoader
          height="20px"
          width="20px"
          svgUrl="assets/svg/icon-circle-checked.svg"></span>
      </div>
      <div
        class="t-flex t-text-[#2F3080DE] t-text-[16px] t-font-medium t-relative t-top-[10px] t-ml-2">
        File Type Information
      </div>
    </div>
  </div>
</kendo-dialog-titlebar>
<div class="t-flex t-my-5 t-w-full">
  <kendo-grid
    class="t-flex t-flex-col-reverse t-relative t-overflow-y-auto"
    [data]="supportedTypeInfo()"
    [resizable]="true">
    <kendo-grid-column headerClass="t-text-primary" title="#" [width]="50">
      <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
        {{ rowIndex + 1 }}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="fileType"
      title="File Type"></kendo-grid-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="extension"
      title="File Extension"></kendo-grid-column>
  </kendo-grid>
</div>
