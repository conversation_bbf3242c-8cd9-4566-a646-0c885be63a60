<form [formGroup]="reprocessForm">
  <div *ngIf="showStatus()">
    <venio-reprocessing-status
      [projectId]="projectId()"
      (hideReprocessingStatus)="
        hideReprocessingStatus($event)
      "></venio-reprocessing-status>
  </div>
  <div class="t-flex t-gap-2 t-gap-2 t-h-[90vh]" *ngIf="!showStatus()">
    <div class="t-w-1/3 t-bg-[#FBFBFB] t-p-2 t-pb-4">
      <div
        class="t-flex t-flex-col t-my-2"
        *ngIf="!isFallbackIngestionEngine()">
        <span class="t-inline-flex t-items-center t-gap-2 t-pt-[15px]">
          <input
            type="checkbox"
            #reprocessInNewMedia
            kendoCheckBox
            [checked]="reprocessInNewMediaChecked()"
            [disabled]="isReprocessQueuing"
            (click)="onReprocessinNewMediaClicked()" />
          <kendo-label
            class="k-radio-label t-relative"
            [for]="reprocessInNewMedia"
            [ngClass]="{
              't-text-primary t-font-medium':
                reprocessInNewMediaChecked() && !reprocessInNewMedia?.disabled
            }"
            text="Reprocess in new media"></kendo-label>
        </span>
        <span class="t-mt-4 t-flex t-items-start">
          <input
            kendoTextBox
            (blur)="onMediaNameBlur()"
            placeholder="Media name ..."
            class="t-ml-[20px]"
            formControlName="newMediaName"
            [disabled]="!reprocessInNewMediaChecked() || isReprocessQueuing" />
        </span>
      </div>
      <div
        class="t-flex t-gap-3 t-items-center"
        [hidden]="!isFallbackIngestionEngine()">
        <span class="t-inline-flex t-gap-0">
          <input
            type="radio"
            kendoRadioButton
            name="reprocessingType"
            [value]="1"
            formControlName="reprocessingType"
            checked
            #execptionType />
          <kendo-label
            class="k-radio-label t-relative"
            [for]="execptionType"
            [ngClass]="{
              't-text-primary t-font-medium':
                reprocessForm.get('reprocessingType')?.value === 1 &&
                !execptionType?.disabled
            }"
            text="Exception Types"></kendo-label>
        </span>
        <span class="t-inline-flex t-gap-0">
          <input
            type="radio"
            kendoRadioButton
            name="reprocessingType"
            [value]="2"
            formControlName="reprocessingType"
            #searchTag />
          <kendo-label
            class="k-radio-label t-relative"
            [for]="searchTag"
            [ngClass]="{
              't-text-primary t-font-medium':
                reprocessForm.get('reprocessingType')?.value === 2 &&
                !searchTag?.disabled
            }"
            text="Search Tags"></kendo-label>
        </span>
      </div>

      <div *ngIf="isExceptionTypeSelected()">
        <p class="t-my-4">Show selected type of documents</p>
        <div class="t-flex t-h-[80%]">
          @defer{
          <venio-reprocessing-exception
            [projectId]="projectId()"
            [isFallbackIngestionEngine]="isFallbackIngestionEngine()"
            (bulkSettingsDialogAction)="openBulkSettingsDialog($event)" />
          } @placeholder{
          <div class="t-flex t-flex-col t-gap-0">
            <kendo-skeleton
              *ngFor="let n of [1, 2, 3]"
              height="25px"
              width="100%"
              shape="rectangle"
              class="t-rounded-md" />
          </div>
          }
        </div>
      </div>
      <div *ngIf="!isExceptionTypeSelected()">
        <div class="t-flex t-flex-col t-mt-4">
          @defer{
          <venio-reprocessing-tags></venio-reprocessing-tags>
          } @placeholder{
          <kendo-skeleton
            *ngFor="let n of [1, 2, 3]"
            height="25px"
            width="100%"
            shape="rectangle"
            class="t-rounded-md" />
          }
        </div>
      </div>
    </div>
    <div class="t-w-2/3 t-p-2">
      <div class="t-h-[70px] t-flex t-items-center">
        <div class="t-flex t-justify-between t-items-center t-w-full t-p-4">
          <span class="t-inline-flex t-items-center t-gap-2">
            <input
              type="checkbox"
              #allFiles
              kendoCheckBox
              [disabled]="
                (!isFallbackIngestionEngine() &&
                  reprocessInNewMediaChecked()) ||
                isReprocessFromVODExportServiceNotification() ||
                isReprocessQueuing
              "
              [checked]="isAllFiles()"
              (click)="onAllFileChecked()" />
            <kendo-label
              class="k-radio-label t-relative"
              [for]="allFiles"
              [ngClass]="{
                't-text-primary t-font-medium':
                  isAllFiles() && !allFiles?.disabled
              }"
              text="All Files"></kendo-label>
          </span>

          <div class="t-flex t-gap-2">
            <button
              kendoButton
              themeColor="primary"
              fillMode="outline"
              *ngIf="isFallbackIngestionEngine()"
              [disabled]="
                (!isAllFiles() &&
                  reprocessForm.get('reprocessingType').value !== 2) ||
                isReprocessQueuing
              "
              (click)="openBulkSettingsDialog('REPLACE_ANY')">
              Bulk Settings
            </button>

            <button
              kendoButton
              themeColor="dark"
              fillMode="outline"
              [disabled]="
                !(totalDocumentsCount > 0 && isAllFiles()) || isReprocessQueuing
              "
              (click)="openLoadfileDialog()">
              Use Load File
            </button>

            <button
              kendoButton
              class="v-custom-secondary-button"
              themeColor="secondary"
              [disabled]="!(selectedDocumentsCount > 0) || isLoadFileSaving()"
              (click)="onSaveAsLoadFileClicked()"
              fillMode="outline">
              Save As Load File
              <kendo-loader
                *ngIf="isLoadFileSaving()"
                type="pulsing"
                themeColor="success" />
            </button>

            <button
              kendoButton
              class="v-custom-secondary-button"
              themeColor="secondary"
              fillMode="outline"
              data-qa="reprocess-Job-Status"
              (click)="showJobStatus()">
              Job Status
            </button>
          </div>
        </div>
      </div>
      <ng-template #actionPlaceholder>
        <div class="t-flex t-flex-row t-gap-2">
          <kendo-skeleton
            *ngFor="let n of [1, 2, 3, 4]"
            height="25px"
            width="25px"
            shape="rectangle"
            class="t-rounded-md" />
        </div>
      </ng-template>
      @defer {
      <venio-reprocessing-document
        #documentComponent
        [projectId]="projectId()"
        [isFallbackIngestionEngine]="isFallbackIngestionEngine()"
        [isReprocessFromVODExportServiceNotification]="
          isReprocessFromVODExportServiceNotification()
        "
        (custodianDialogAction)="openCustodianDialog()" />
      } @placeholder {
      <ng-container *ngTemplateOutlet="actionPlaceholder" />
      }
    </div>
  </div>
</form>

<div
  class="t-fixed t-top-[1px] t-left-0 t-w-full t-h-full t-bg-[#212121] t-opacity-10 t-z-[1999]"
  *ngIf="isOverlayActive()"
  (click)="onDialogClosed()"></div>
<div
  class="t-fixed t-top-[1px] t-w-[56%] t-h-full t-bg-white t-overflow-hidden t-shadow-[0px_20px_16px_6px_rgba(0,0,0,0.212)] t-z-[2000] t-transition-all t-duration-400 t-p-5"
  [ngClass]="{
    't-right-0': isOverlayActive(),
    't-right-[-56%]': !isOverlayActive()
  }">
  <div class="t-flex t-justify-between t-items-center t-w-full">
    <span
      class="t-inline-flex t-items-center t-gap-3 t-text-primary t-text-lg t-font-semibold">
      <button
        class="t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-h-[35px] t-flex t-items-center"
        fillMode="clear"
        kendoButton
        [imageUrl]="overlayIconUrl"></button>
      {{ overlayTitle }}
    </span>
    <button
      (click)="closeOverlay()"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-text-white t-w-6 t-h-6 t-p-0 t-bg-[#ED7425] t-leading-none">
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
  </div>
  @defer(when activeComponent === 'custodian'){
  <venio-case-reprocessing-custodian-dialog
    [projectId]="projectId()"
    [isFallbackIngestionEngine]="isFallbackIngestionEngine()"
    [isOpendAfterReprocess]="isOpendAfterReprocess()"
    (closeCustodianDialog)="closeCustodianDialogEvent($event)"
    *ngIf="
      activeComponent === 'custodian'
    "></venio-case-reprocessing-custodian-dialog>
  } @defer(when activeComponent === 'update'){
  <venio-case-reprocessing-update
    *ngIf="activeComponent === 'update'"
    [data]="bulkSettingUpdateData"
    (closeBulkSettingUpdateDialog)="
      closeBulkSettingUpdateDialogEvent($event)
    "></venio-case-reprocessing-update>
  }
</div>

<div kendoDialogContainer></div>
