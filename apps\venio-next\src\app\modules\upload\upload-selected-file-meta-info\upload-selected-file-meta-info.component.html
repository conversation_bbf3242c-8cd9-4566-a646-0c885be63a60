<div
  class="t-flex t-flex-col t-gap-2 t-shadow-[0px_6px_10px_#00000029] t-p-[20px] t-rounded-[2px]">
  <div class="t-flex t-flex-row t-justify-between">
    <div class="t-flex t-items-center">
      <span class="t-text-[#000000] t-text-[14px]">{{
        fileMetaInfo()?.name
      }}</span>
    </div>
    <button
      (click)="removeFile()"
      kendoButton
      class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px]"
      rounded="full"
      fillMode="clear"
      title="Delete">
      <span
        venioSvgLoader
        class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
        svgUrl="assets/svg/Icon-material-delete-grey.svg"></span>
    </button>
  </div>
  @if(shouldShowInputControls()) {
  <kendo-combobox
    #custComp
    [data]="custodianNames"
    [allowCustom]="true"
    [clearButton]="true"
    [filterable]="true"
    [valuePrimitive]="true"
    (blur)="custodianBlur(custComp)"
    [ngModel]="fileMetaInfo()?.custodianName"
    (valueChange)="fileMetaInfoChange($event, 'custodianName')"
    (filterChange)="handleFilter($event)"
    placeholder="Assign your custodian value">
    <ng-template kendoComboBoxNoDataTemplate>
      <span class="t-cursor-pointer" (click)="addCustomCustodian(custComp)"
        >Click to add item <b>{{ custComp.text }}</b></span
      >
    </ng-template>
  </kendo-combobox>
  <kendo-textbox
    [ngModel]="fileMetaInfo()?.mediaName"
    (valueChange)="fileMetaInfoChange($event, 'mediaName')"
    placeholder="Enter media name"
    type="text"
    class="t-flex t-flex-1" />
  <kendo-textbox
    [ngModel]="fileMetaInfo()?.password"
    (valueChange)="fileMetaInfoChange($event, 'password')"
    placeholder="Enter password"
    [type]="passwordVisibilityMap()?.[fileMetaInfo()?.fileId] ? 'text' : 'password'"
    class="t-flex t-flex-1">
    <ng-template kendoTextBoxSuffixTemplate>
      <button
        [tabIndex]="-1"
        kendoButton
        type="button"
        class="t-pr-2"
        look="clear"
        themeColor="none"
        fillMode="clear"
        (click)="passwordVisibilityClicked()">
        <kendo-svgicon
          [icon]="
              passwordVisibilityMap()?.[fileMetaInfo()?.fileId]? iconEye : iconSlashEye
            " />
      </button>
    </ng-template>
  </kendo-textbox>
  @if(fileMetaInfo()?.name?.toLowerCase().trim().endsWith('.nsf')) {
  <div class="t-flex t-gap-2">
    <kendo-textbox
      [ngModel]="fileMetaInfo()?.idFileName"
      (click)="nsfFileSelect.click()"
      placeholder="Browse .id"
      [readonly]="true"
      class="t-flex t-flex-1" />
    <input
      #nsfFileSelect
      (change)="idFileSelect($event)"
      type="file"
      accept=".id"
      class="t-hidden" />
    <button
      (click)="nsfFileSelect.click()"
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline">
      ...
    </button>
  </div>
  } }
</div>
